# College Management System - Core Page Creation Prompts

## Authentication Pages

### 1. <PERSON>gin Page
```
Create a minimal login page for a college management system using Nuxt UI components. The page should include:
- Clean, centered login form with UCard component
- Email and password fields using UInput
- "Remember me" checkbox using UCheckbox
- Primary login button using UButton
- Links for "Forgot Password" and "Don't have an account? Sign up"
- College logo and branding
- Responsive design that works on mobile and desktop
- Dark mode support
- Loading states for form submission
- Form validation with proper error messages using UAlert
- SSO login options (Google, Microsoft) as secondary buttons
```

### 2. Registration Page
```
Create a user registration page for the college management system using Nuxt UI components. Include:
- Multi-step registration form using UCard
- Personal information fields (name, email, phone) using UInput
- Role selection using USelect (Student, Faculty, Staff)
- Student ID/Employee ID field using UInput
- Password creation with strength indicator
- Terms and conditions checkbox using UCheckbox
- Submit button with loading states using UButton
- Progress indicator for multi-step form
- Form validation with real-time feedback
- Responsive design optimized for mobile
- Option to upload profile photo using file input
```

### 3. Password Reset Page
```
Create a password reset page using Nuxt UI components featuring:
- Simple centered form with UCard
- Email input field using UInput
- Submit button using UButton with loading state
- Success/error messages using UAlert
- Link back to login page
- Instructions text with clear typography
- Responsive design
- Dark mode compatibility
- Rate limiting message if applicable
```

## Dashboard Pages

### 4. Admin Dashboard
```
Create an administrative dashboard for college management using Nuxt UI components. Include:
- Header with navigation using UHorizontalNavigation
- Key metrics cards using UCard showing enrollment, revenue, faculty count
- Quick actions panel with UButton components for common tasks
- Recent activities feed using UCard with avatar and timestamps
- Charts and graphs for enrollment trends, financial overview
- System health monitoring and alerts
- Notification panel using UNotification
- Search functionality using UInput with search icon
- Responsive grid layout that adapts to screen size
- Dark mode support with proper contrast
- Collapsible sidebar navigation using UVerticalNavigation
```

### 5. Student Dashboard
```
Create a student dashboard using Nuxt UI components featuring:
- Welcome message with student name and profile picture
- Current semester courses grid using UCard
- Upcoming assignments and deadlines using UCard with status badges
- GPA and academic standing display
- Quick links to registration, grades, financial aid using UButton
- Calendar widget showing important dates
- Recent announcements using UAlert or UCard
- Course schedule overview
- Responsive mobile-first design
- Progress bars for degree completion using UProgress
- Dark mode support
```

### 6. Faculty Dashboard
```
Create a faculty dashboard using Nuxt UI components including:
- Course management overview with UCard components
- Current semester teaching schedule
- Student roster quick access using UTable
- Grading tasks and pending items with status indicators
- Academic deadlines and important dates
- Office hours and appointment scheduling
- Recent student messages or notifications
- Quick actions for creating assignments, updating grades
- Analytics on student performance using charts
- Responsive design for tablet and mobile use
- Dark mode compatibility
```

## Academic Management Pages

### 7. Course Catalog
```
Create a course catalog page using Nuxt UI components featuring:
- Search and filter functionality using UInput and USelect
- Course cards using UCard with course code, title, credits, description
- Pagination using UPagination
- Filter sidebar with department, level, semester options
- Course details modal using UModal for expanded information
- Prerequisites and corequisites display
- Instructor information and ratings
- Course capacity and availability indicators
- Responsive grid layout for course cards
- Dark mode support
- Breadcrumb navigation using UBreadcrumb
```

### 8. Course Details Page
```
Create a detailed course page using Nuxt UI components including:
- Course header with title, code, credits using UCard
- Tabs for Overview, Syllabus, Assignments, Grades using UTabs
- Instructor information with contact details
- Course schedule and location information
- Enrollment button with prerequisite validation
- Student roster (for faculty) using UTable
- Assignment submission interface
- Grade distribution charts
- Discussion forum integration
- Responsive design for mobile access
- Dark mode compatibility
```

### 9. Academic Calendar
```
Create an academic calendar page using Nuxt UI components featuring:
- Full calendar view with semester dates and events
- Month/week/day view toggles using UButton group
- Event creation and editing using UModal
- Color-coded events (exams, holidays, registration periods)
- Filter options for different event types
- Important dates sidebar using UCard
- Responsive calendar that works on mobile
- Dark mode support with proper color schemes
- Integration with personal calendars
- Notification settings for upcoming events
```

### 10. Course Registration
```
Create a course registration page using Nuxt UI components including:
- Course search and filtering using UInput and USelect
- Available courses table using UTable with enrollment status
- Shopping cart functionality for selected courses
- Schedule conflict detection and warnings
- Credit hour tracking and limits
- Prerequisite validation
- Registration confirmation and receipt
- Waitlist management for full courses
- Drop/add functionality with deadlines
- Responsive design for mobile registration
- Dark mode support
```

## Student Management Pages

### 11. Student Directory
```
Create a student directory page using Nuxt UI components including:
- Search functionality using UInput with filters
- Student cards with photos and basic info using UCard
- Pagination for large student lists using UPagination
- Filter options by department, year, status using USelect
- Student detail view using UModal or separate page
- Bulk actions for administrators using UCheckbox
- Export functionality using UButton
- Responsive grid layout for student cards
- Privacy controls based on user role
- Dark mode support
```

### 12. Student Profile
```
Create a comprehensive student profile page using Nuxt UI components featuring:
- Profile header with photo, name, ID, status using UCard
- Tabs for Personal Info, Academic, Financial, Documents using UTabs
- Editable fields for contact information using UInput
- Academic history and transcript display
- Financial aid and billing information
- Document upload functionality
- Emergency contact information
- Advisor assignment and contact details
- Academic holds and alerts using UAlert
- Responsive design for mobile editing
- Dark mode compatibility
```

### 13. Grade Management
```
Create a grade management interface using Nuxt UI components including:
- Course selection dropdown using USelect
- Student roster with grade entry using UTable
- Assignment weight configuration
- Grade calculation and GPA computation
- Bulk grade entry functionality
- Grade history and changes tracking
- Export to CSV/PDF functionality using UButton
- Grade posting and notification system
- Responsive table design for mobile
- Dark mode support with clear contrast
```

### 14. Transcript Management
```
Create a transcript management page using Nuxt UI components featuring:
- Academic transcript display using UTable
- Semester breakdown with expandable sections
- GPA calculation and tracking over time
- Course transfer credit management
- Official transcript request functionality
- Print and PDF export options
- Academic standing indicators
- Degree audit and progress tracking
- Responsive design for document viewing
- Dark mode support with clear typography
```

## Faculty Management Pages

### 15. Faculty Directory
```
Create a faculty directory page using Nuxt UI components featuring:
- Faculty member cards with photos and departments using UCard
- Search and filter by department, specialization using UInput and USelect
- Contact information and office hours display
- Research interests and publications
- Course load and teaching schedule
- Pagination for large faculty lists using UPagination
- Faculty profile modal with detailed information
- Responsive grid layout
- Dark mode support
- Professional credentials display
```

### 16. Faculty Profile
```
Create a faculty profile page using Nuxt UI components including:
- Professional photo and basic information using UCard
- Tabs for Bio, Courses, Research, Publications using UTabs
- Editable fields for contact and office information
- Course assignment and schedule display
- Research projects and grant information
- Publication list with links
- Student evaluation summaries
- Office hours and appointment booking
- Responsive design for mobile viewing
- Dark mode compatibility
```

### 17. Course Assignment
```
Create a course assignment management page using Nuxt UI components featuring:
- Faculty course load overview using UCard
- Semester and course assignment interface
- Schedule conflict detection
- Classroom and resource allocation
- Teaching load balancing tools
- Course preference submission
- Assignment approval workflow
- Historical assignment tracking
- Responsive design for administrative use
- Dark mode support
```

## Financial Management Pages

### 18. Billing and Payments
```
Create a student billing page using Nuxt UI components featuring:
- Account balance overview using UCard with status indicators
- Billing history table using UTable with payment status
- Payment form with credit card integration
- Payment plan options and setup
- Financial aid application and disbursement tracking
- Receipt generation and download
- Automated payment setup
- Due date reminders and notifications
- Responsive design for mobile payments
- Dark mode support with secure payment indicators
```

### 19. Financial Aid
```
Create a financial aid management page using Nuxt UI components including:
- Aid application status tracking using UCard with progress indicators
- Available scholarships and grants display
- Application forms with file upload capability
- Award letters and notifications
- Disbursement schedule and tracking
- Requirements and deadlines checklist
- Document submission interface
- Aid counselor contact information
- Responsive design for mobile access
- Dark mode compatibility
```

### 20. Budget Management (Admin)
```
Create an administrative budget management page using Nuxt UI components featuring:
- Department budget overview using UCard components
- Budget allocation charts and graphs
- Expense tracking and approval workflows
- Budget variance analysis
- Spending trends and forecasting
- Approval requests using UTable with action buttons
- Budget report generation
- Fiscal year selection and comparison
- Responsive dashboard design
- Dark mode support with clear financial indicators
```

### 21. Fee Management
```
Create a fee management page using Nuxt UI components including:
- Fee structure display by program and semester
- Payment deadline tracking and notifications
- Late fee calculation and application
- Fee waiver and exemption management
- Refund processing and tracking
- Payment method management
- Installment plan setup and monitoring
- Financial hold management
- Responsive design for mobile payments
- Dark mode support
```

## Communication Pages

### 22. Messaging System
```
Create a messaging interface using Nuxt UI components including:
- Message thread list using UCard components
- Chat interface with real-time messaging
- Compose new message modal using UModal
- File attachment functionality
- Message search and filtering using UInput
- Contact/user selection for new messages
- Message status indicators (read/unread)
- Responsive design optimized for mobile
- Dark mode support
- Notification settings and preferences
```

### 23. Announcements
```
Create an announcements page using Nuxt UI components featuring:
- Announcement cards with priority indicators using UCard
- Create announcement form for administrators
- Category filtering and search using USelect and UInput
- Announcement detail view with rich text content
- Recipient targeting options (by role, department)
- Scheduling and expiration date settings
- Attachment support for documents
- Responsive design for mobile reading
- Dark mode compatibility
- Notification preferences
```

### 24. Discussion Forums
```
Create a discussion forum interface using Nuxt UI components including:
- Forum category navigation using UVerticalNavigation
- Topic list with sorting and filtering using UTable
- Thread view with nested replies
- Post creation and editing forms using UModal
- User moderation and reporting tools
- Search functionality across forums
- Subscription and notification management
- Responsive design for mobile browsing
- Dark mode support
- Rich text editing capabilities
```

## Analytics and Reporting Pages

### 25. Analytics Dashboard
```
Create an analytics dashboard using Nuxt UI components including:
- Key performance indicator cards using UCard
- Interactive charts and graphs for enrollment, performance, financial data
- Date range selectors and filtering options
- Customizable dashboard widgets
- Report generation and scheduling
- Data export functionality
- Comparison views (year-over-year, semester-over-semester)
- Drill-down capabilities for detailed analysis
- Responsive design for mobile analytics viewing
- Dark mode support with clear data visualization
```

### 26. Reports Generation
```
Create a reports generation page using Nuxt UI components featuring:
- Report template selection using UCard grid
- Parameter input forms for custom reports
- Report scheduling and automation
- Generated report preview and download
- Report sharing and distribution options
- Custom report builder with drag-and-drop interface
- Report history and version management
- Export formats (PDF, CSV, Excel)
- Responsive design for mobile report access
- Dark mode compatibility
```

### 27. Enrollment Analytics
```
Create an enrollment analytics page using Nuxt UI components including:
- Enrollment trend charts and visualizations
- Program popularity and capacity analysis
- Student demographic breakdowns
- Registration timeline and patterns
- Waitlist and drop rate analysis
- Comparative enrollment data
- Predictive enrollment modeling
- Export functionality for data analysis
- Responsive design for data visualization
- Dark mode support
```

## Library and Resources

### 28. Library Management
```
Create a library management interface using Nuxt UI components featuring:
- Book and resource catalog with search using UInput
- Checkout and return management using UTable
- Reservation system for popular items
- Digital resource access and links
- Fine and fee tracking
- Library hours and location information
- Study room booking system
- Research assistance and tutorials
- Responsive design for mobile library access
- Dark mode support
```

### 29. Resource Center
```
Create a resource center page using Nuxt UI components including:
- Academic resource categories using UCard grid
- Document library with search and filter
- Tutorial and help documentation
- FAQ section with search functionality
- Contact information for support services
- Resource request and suggestion forms
- Download tracking and analytics
- Responsive design for mobile resource access
- Dark mode compatibility
- Accessibility features for diverse learners
```

## Settings and Administration

### 30. System Settings
```
Create a system settings page using Nuxt UI components including:
- Settings categories using UTabs (General, Security, Notifications, etc.)
- Configuration forms with validation using UInput and USelect
- User permission and role management
- System maintenance and backup options
- Integration settings for external services
- Theme and appearance customization
- Email and notification templates
- Audit log and system monitoring
- Responsive settings interface
- Dark mode support
```

### 31. User Management (Admin)
```
Create a user management page using Nuxt UI components featuring:
- User list with search and filtering using UTable
- User creation and editing forms using UModal
- Role assignment and permission management
- Bulk user operations and imports
- User activity monitoring and logs
- Account status management (active, suspended, expired)
- Password reset and security settings
- User group and department organization
- Responsive design for mobile administration
- Dark mode compatibility
```

### 32. Profile Settings
```
Create a user profile settings page using Nuxt UI components including:
- Profile information editing form using UInput components
- Password change interface with security requirements
- Notification preferences using UCheckbox and USelect
- Privacy settings and data control options
- Account security features (2FA, login history)
- Profile photo upload and management
- Theme and appearance preferences
- Language and localization settings
- Responsive design for mobile profile management
- Dark mode support
```

### 33. Department Management
```
Create a department management page using Nuxt UI components featuring:
- Department overview cards using UCard
- Faculty and staff assignment management
- Course offering and scheduling
- Budget allocation and tracking
- Department-specific announcements
- Resource and facility management
- Performance metrics and reporting
- Contact information and organizational charts
- Responsive design for administrative use
- Dark mode support
```

## Mobile-Specific Considerations

### General Mobile Optimization Prompts
```
For all pages, ensure:
- Touch-friendly interface with appropriate button sizes
- Swipe gestures for navigation where appropriate
- Optimized forms for mobile keyboard input
- Collapsible sections to save screen space
- Mobile-first responsive design approach
- Fast loading with progressive enhancement
- Offline functionality where possible
- Push notification integration
- Accessible design for screen readers
- Optimized images and assets for mobile bandwidth
```

## Component Reusability Notes
```
Create reusable components for:
- User avatar with status indicators
- Course cards with consistent formatting
- Notification items with read/unread states
- Search and filter combinations
- Data tables with sorting and pagination
- Form validation and error handling
- Loading states and skeleton screens
- Empty states with helpful messaging
- Confirmation dialogs and modals
- Navigation breadcrumbs and menus
```

## Design System Guidelines
```
Maintain consistency across all pages with:
- Consistent spacing using Tailwind CSS utilities
- Unified color scheme supporting light/dark modes
- Typography hierarchy with proper heading levels
- Icon usage from Heroicons or Iconify
- Button styles for primary, secondary, and destructive actions
- Form styling with proper labels and help text
- Card layouts with consistent padding and shadows
- Navigation patterns and breadcrumbs
- Error and success message styling
- Loading and skeleton state designs
```
