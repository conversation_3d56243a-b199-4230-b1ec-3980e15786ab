/**
 * Standardized layout classes for consistent flex container patterns
 * across the entire application
 */

export const useLayoutClasses = () => {
  // Main container classes for different layout types
  const containerClasses = {
    // Flex container for layouts with sidebars (horizontal layout)
    flexHorizontal: 'min-h-screen bg-gray-50 dark:bg-gray-900 flex',
    
    // Flex container for layouts with top navigation (vertical layout)
    flexVertical: 'min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col',
    
    // Auth layout container
    auth: 'min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col'
  }

  // Sidebar classes for consistent sidebar implementations
  const sidebarClasses = {
    // Sticky sidebar for desktop, fixed overlay for mobile
    sticky: 'sticky top-0 h-screen bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 transition-all duration-300 ease-in-out flex flex-col overflow-hidden',
    
    // Mobile overlay backdrop
    backdrop: 'lg:hidden fixed inset-0 z-40 bg-gray-600 bg-opacity-75',
    
    // Sidebar header (non-scrollable)
    header: 'flex-shrink-0 flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-800',
    
    // Sidebar navigation area (scrollable)
    navigation: 'flex-1 px-2 py-4 space-y-1 overflow-y-auto min-h-0',
    
    // Sidebar footer (non-scrollable)
    footer: 'flex-shrink-0 border-t border-gray-200 dark:border-gray-800 p-4'
  }

  // Main content area classes
  const contentClasses = {
    // Main content area in horizontal layout (with sidebar)
    withSidebar: 'flex-1 flex flex-col min-w-0 overflow-hidden',

    // Main content area in vertical layout (with top nav) - UContainer handles width/padding
    withTopNav: 'flex-1 py-8 w-full overflow-y-auto',

    // Page content area
    page: 'flex-1 p-3 sm:p-4 lg:p-6 overflow-y-auto',

    // Auth content area - UContainer handles width/padding
    auth: 'flex-1 flex items-center justify-center'
  }

  // Header/Navigation classes
  const headerClasses = {
    // Top navigation bar (sticky)
    topNav: 'sticky top-0 z-30 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700',
    
    // Horizontal navigation (non-sticky)
    horizontal: 'flex-shrink-0',
    
    // Auth header
    auth: 'flex-shrink-0 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700'
  }

  // Footer classes
  const footerClasses = {
    // Standard footer
    standard: 'flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700',
    
    // Auth footer
    auth: 'flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700'
  }

  // Responsive classes for different screen sizes
  const responsiveClasses = {
    // Mobile sidebar positioning
    mobileSidebar: {
      open: 'fixed inset-y-0 left-0 z-50 translate-x-0',
      closed: 'fixed inset-y-0 left-0 z-50 -translate-x-full lg:translate-x-0',
      hidden: 'hidden lg:flex lg:translate-x-0'
    },
    
    // Sidebar width variations
    sidebarWidth: {
      expanded: 'lg:w-64',
      collapsed: 'lg:w-16',
      default: 'w-64'
    }
  }

  // Z-index hierarchy for proper layering
  const zIndexClasses = {
    backdrop: 'z-40',
    sidebar: 'z-50',
    topNav: 'z-30',
    dropdown: 'z-50'
  }

  // Utility functions for combining classes
  const getSidebarClasses = (options: {
    isOpen?: boolean
    collapsed?: boolean
    isMobile?: boolean
  } = {}) => {
    const { isOpen = false, collapsed = false, isMobile = false } = options
    
    const baseClasses = [sidebarClasses.sticky]
    
    // Add width classes
    baseClasses.push(responsiveClasses.sidebarWidth.default)
    if (collapsed) {
      baseClasses.push(responsiveClasses.sidebarWidth.collapsed)
    } else {
      baseClasses.push(responsiveClasses.sidebarWidth.expanded)
    }
    
    // Add mobile positioning
    if (isMobile) {
      if (isOpen) {
        baseClasses.push(responsiveClasses.mobileSidebar.open)
      } else {
        baseClasses.push(responsiveClasses.mobileSidebar.hidden)
      }
    }
    
    return baseClasses.join(' ')
  }

  const getBackdropClasses = () => {
    return `${sidebarClasses.backdrop} ${zIndexClasses.backdrop}`
  }

  const getTopNavClasses = () => {
    return `${headerClasses.topNav} ${zIndexClasses.topNav}`
  }

  return {
    containerClasses,
    sidebarClasses,
    contentClasses,
    headerClasses,
    footerClasses,
    responsiveClasses,
    zIndexClasses,
    getSidebarClasses,
    getBackdropClasses,
    getTopNavClasses
  }
}
