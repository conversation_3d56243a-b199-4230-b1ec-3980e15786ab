export interface Faculty {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  avatar?: string
  title: string
  rank: 'Professor' | 'Associate Professor' | 'Assistant Professor' | 'Lecturer' | 'Adjunct Professor'
  department: string
  office: string
  officeHours: string
  specializations: string[]
  researchInterests: string[]
  education: Education[]
  publications: Publication[]
  courses: CourseAssignment[]
  bio: string
  website?: string
  linkedIn?: string
  orcid?: string
  hireDate: string
  tenure: boolean
  status: 'Active' | 'Sabbatical' | 'Retired' | 'On Leave'
  workload: number // Teaching load percentage
  maxCourses: number
  preferredSchedule: string[]
}

export interface Education {
  degree: string
  field: string
  institution: string
  year: number
}

export interface Publication {
  id: string
  title: string
  type: 'Journal Article' | 'Conference Paper' | 'Book' | 'Book Chapter' | 'Technical Report'
  venue: string
  year: number
  authors: string[]
  doi?: string
  url?: string
}

export interface CourseAssignment {
  id: string
  courseCode: string
  courseName: string
  semester: string
  year: number
  credits: number
  enrollmentCount: number
  maxEnrollment: number
  schedule: {
    days: string[]
    time: string
    room: string
  }
  status: 'Assigned' | 'Pending' | 'Cancelled'
}

export interface FacultyFilters {
  search: string
  department: string
  rank: string
  status: string
  researchArea: string
}

export interface FacultyStats {
  total: number
  byDepartment: Record<string, number>
  byRank: Record<string, number>
  byStatus: Record<string, number>
  averageWorkload: number
  totalCourses: number
}

export const useFacultyData = () => {
  // Mock faculty data
  const mockFaculty = ref<Faculty[]>([
    {
      id: '1',
      firstName: 'Dr. Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      title: 'Professor of Computer Science',
      rank: 'Professor',
      department: 'Computer Science',
      office: 'CS Building, Room 301',
      officeHours: 'Mon/Wed 2-4 PM, Fri 10-12 PM',
      specializations: ['Machine Learning', 'Artificial Intelligence', 'Data Science'],
      researchInterests: ['Deep Learning', 'Natural Language Processing', 'Computer Vision'],
      education: [
        { degree: 'Ph.D.', field: 'Computer Science', institution: 'Stanford University', year: 2008 },
        { degree: 'M.S.', field: 'Computer Science', institution: 'MIT', year: 2004 },
        { degree: 'B.S.', field: 'Computer Science', institution: 'UC Berkeley', year: 2002 }
      ],
      publications: [
        {
          id: 'pub1',
          title: 'Advanced Machine Learning Techniques in Educational Technology',
          type: 'Journal Article',
          venue: 'Journal of Educational Technology Research',
          year: 2024,
          authors: ['Sarah Johnson', 'Michael Chen'],
          doi: '10.1234/jetr.2024.001'
        }
      ],
      courses: [
        {
          id: 'course1',
          courseCode: 'CS 401',
          courseName: 'Machine Learning',
          semester: 'Fall',
          year: 2024,
          credits: 3,
          enrollmentCount: 45,
          maxEnrollment: 50,
          schedule: {
            days: ['Monday', 'Wednesday', 'Friday'],
            time: '10:00 AM - 11:00 AM',
            room: 'CS 204'
          },
          status: 'Assigned'
        }
      ],
      bio: 'Dr. Sarah Johnson is a leading researcher in machine learning and artificial intelligence with over 15 years of experience in academia and industry.',
      website: 'https://faculty.college.edu/sjohnson',
      linkedIn: 'https://linkedin.com/in/sarahjohnson',
      orcid: '0000-0000-0000-0001',
      hireDate: '2010-08-15',
      tenure: true,
      status: 'Active',
      workload: 75,
      maxCourses: 4,
      preferredSchedule: ['Morning', 'Afternoon']
    },
    {
      id: '2',
      firstName: 'Dr. Michael',
      lastName: 'Chen',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      title: 'Associate Professor of Mathematics',
      rank: 'Associate Professor',
      department: 'Mathematics',
      office: 'Math Building, Room 205',
      officeHours: 'Tue/Thu 1-3 PM',
      specializations: ['Applied Mathematics', 'Statistics', 'Numerical Analysis'],
      researchInterests: ['Computational Mathematics', 'Statistical Modeling', 'Optimization'],
      education: [
        { degree: 'Ph.D.', field: 'Mathematics', institution: 'Harvard University', year: 2012 },
        { degree: 'M.S.', field: 'Applied Mathematics', institution: 'Caltech', year: 2008 }
      ],
      publications: [],
      courses: [
        {
          id: 'course2',
          courseCode: 'MATH 301',
          courseName: 'Advanced Calculus',
          semester: 'Fall',
          year: 2024,
          credits: 4,
          enrollmentCount: 32,
          maxEnrollment: 35,
          schedule: {
            days: ['Tuesday', 'Thursday'],
            time: '2:00 PM - 3:30 PM',
            room: 'Math 101'
          },
          status: 'Assigned'
        }
      ],
      bio: 'Dr. Michael Chen specializes in applied mathematics with a focus on computational methods and statistical modeling.',
      hireDate: '2015-01-10',
      tenure: true,
      status: 'Active',
      workload: 80,
      maxCourses: 3,
      preferredSchedule: ['Afternoon']
    },
    {
      id: '3',
      firstName: 'Dr. Emily',
      lastName: 'Rodriguez',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      title: 'Assistant Professor of Biology',
      rank: 'Assistant Professor',
      department: 'Biology',
      office: 'Bio Building, Room 102',
      officeHours: 'Mon/Wed/Fri 11 AM-12 PM',
      specializations: ['Molecular Biology', 'Genetics', 'Biochemistry'],
      researchInterests: ['Gene Expression', 'Protein Folding', 'Cell Biology'],
      education: [
        { degree: 'Ph.D.', field: 'Biology', institution: 'Yale University', year: 2018 },
        { degree: 'B.S.', field: 'Biology', institution: 'UCLA', year: 2013 }
      ],
      publications: [],
      courses: [],
      bio: 'Dr. Emily Rodriguez is a molecular biologist focusing on gene expression and protein interactions.',
      hireDate: '2020-08-20',
      tenure: false,
      status: 'Active',
      workload: 70,
      maxCourses: 3,
      preferredSchedule: ['Morning']
    },
    {
      id: '4',
      firstName: 'Prof. David',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      title: 'Professor of Physics',
      rank: 'Professor',
      department: 'Physics',
      office: 'Physics Building, Room 401',
      officeHours: 'Tue/Thu 3-5 PM',
      specializations: ['Quantum Physics', 'Theoretical Physics', 'Particle Physics'],
      researchInterests: ['Quantum Mechanics', 'String Theory', 'Cosmology'],
      education: [
        { degree: 'Ph.D.', field: 'Physics', institution: 'Princeton University', year: 2005 },
        { degree: 'M.S.', field: 'Physics', institution: 'University of Chicago', year: 2001 }
      ],
      publications: [],
      courses: [],
      bio: 'Prof. David Wilson is a theoretical physicist with expertise in quantum mechanics and cosmology.',
      hireDate: '2008-01-15',
      tenure: true,
      status: 'Active',
      workload: 85,
      maxCourses: 4,
      preferredSchedule: ['Afternoon', 'Evening']
    },
    {
      id: '5',
      firstName: 'Dr. Lisa',
      lastName: 'Thompson',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      title: 'Associate Professor of Chemistry',
      rank: 'Associate Professor',
      department: 'Chemistry',
      office: 'Chem Building, Room 203',
      officeHours: 'Mon/Wed 1-3 PM',
      specializations: ['Organic Chemistry', 'Analytical Chemistry', 'Environmental Chemistry'],
      researchInterests: ['Green Chemistry', 'Catalysis', 'Environmental Analysis'],
      education: [
        { degree: 'Ph.D.', field: 'Chemistry', institution: 'University of California, Berkeley', year: 2010 }
      ],
      publications: [],
      courses: [],
      bio: 'Dr. Lisa Thompson specializes in green chemistry and environmental analytical methods.',
      hireDate: '2013-09-01',
      tenure: true,
      status: 'Active',
      workload: 75,
      maxCourses: 3,
      preferredSchedule: ['Morning', 'Afternoon']
    },
    {
      id: '6',
      firstName: 'Dr. James',
      lastName: 'Anderson',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Lecturer in English',
      rank: 'Lecturer',
      department: 'English',
      office: 'Humanities Building, Room 105',
      officeHours: 'Tue/Thu 10 AM-12 PM',
      specializations: ['Creative Writing', 'Literature', 'Composition'],
      researchInterests: ['Modern Literature', 'Poetry', 'Writing Pedagogy'],
      education: [
        { degree: 'M.F.A.', field: 'Creative Writing', institution: 'Iowa Writers Workshop', year: 2015 }
      ],
      publications: [],
      courses: [],
      bio: 'Dr. James Anderson is a published poet and creative writing instructor.',
      hireDate: '2018-08-25',
      tenure: false,
      status: 'Active',
      workload: 90,
      maxCourses: 5,
      preferredSchedule: ['Morning', 'Afternoon']
    },
    {
      id: '7',
      firstName: 'Dr. Maria',
      lastName: 'Garcia',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      title: 'Professor of Psychology',
      rank: 'Professor',
      department: 'Psychology',
      office: 'Psychology Building, Room 301',
      officeHours: 'Mon/Wed/Fri 2-3 PM',
      specializations: ['Cognitive Psychology', 'Developmental Psychology', 'Research Methods'],
      researchInterests: ['Memory', 'Learning', 'Child Development'],
      education: [
        { degree: 'Ph.D.', field: 'Psychology', institution: 'University of Pennsylvania', year: 2006 }
      ],
      publications: [],
      courses: [],
      bio: 'Dr. Maria Garcia is a cognitive psychologist specializing in memory and learning processes.',
      hireDate: '2009-01-10',
      tenure: true,
      status: 'Active',
      workload: 80,
      maxCourses: 4,
      preferredSchedule: ['Afternoon']
    },
    {
      id: '8',
      firstName: 'Prof. Robert',
      lastName: 'Kim',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      title: 'Associate Professor of Economics',
      rank: 'Associate Professor',
      department: 'Economics',
      office: 'Business Building, Room 205',
      officeHours: 'Tue/Thu 1-3 PM',
      specializations: ['Macroeconomics', 'International Economics', 'Economic Policy'],
      researchInterests: ['Monetary Policy', 'Trade', 'Economic Development'],
      education: [
        { degree: 'Ph.D.', field: 'Economics', institution: 'London School of Economics', year: 2011 }
      ],
      publications: [],
      courses: [],
      bio: 'Prof. Robert Kim specializes in macroeconomics and international trade policy.',
      hireDate: '2014-09-01',
      tenure: true,
      status: 'Active',
      workload: 75,
      maxCourses: 3,
      preferredSchedule: ['Afternoon']
    },
    {
      id: '9',
      firstName: 'Dr. Jennifer',
      lastName: 'Brown',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      title: 'Assistant Professor of History',
      rank: 'Assistant Professor',
      department: 'History',
      office: 'Humanities Building, Room 201',
      officeHours: 'Mon/Wed 3-5 PM',
      specializations: ['American History', 'Social History', 'Women\'s Studies'],
      researchInterests: ['Civil Rights Movement', 'Labor History', 'Gender Studies'],
      education: [
        { degree: 'Ph.D.', field: 'History', institution: 'Columbia University', year: 2019 }
      ],
      publications: [],
      courses: [],
      bio: 'Dr. Jennifer Brown specializes in American social history and civil rights.',
      hireDate: '2021-08-15',
      tenure: false,
      status: 'Active',
      workload: 70,
      maxCourses: 3,
      preferredSchedule: ['Afternoon']
    },
    {
      id: '10',
      firstName: 'Dr. Ahmed',
      lastName: 'Hassan',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      title: 'Professor of Engineering',
      rank: 'Professor',
      department: 'Engineering',
      office: 'Engineering Building, Room 401',
      officeHours: 'Tue/Thu 2-4 PM',
      specializations: ['Mechanical Engineering', 'Robotics', 'Control Systems'],
      researchInterests: ['Autonomous Systems', 'Machine Design', 'Automation'],
      education: [
        { degree: 'Ph.D.', field: 'Mechanical Engineering', institution: 'Georgia Tech', year: 2004 }
      ],
      publications: [],
      courses: [],
      bio: 'Dr. Ahmed Hassan is a mechanical engineer specializing in robotics and automation.',
      hireDate: '2007-01-20',
      tenure: true,
      status: 'Active',
      workload: 80,
      maxCourses: 4,
      preferredSchedule: ['Morning', 'Afternoon']
    },
    // Additional faculty members (11-20)
    {
      id: '11',
      firstName: 'Dr. Susan',
      lastName: 'Lee',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Associate Professor of Art',
      rank: 'Associate Professor',
      department: 'Art',
      office: 'Art Building, Room 101',
      officeHours: 'Mon/Wed/Fri 10 AM-12 PM',
      specializations: ['Digital Art', 'Graphic Design', 'Art History'],
      researchInterests: ['Contemporary Art', 'Digital Media', 'Visual Culture'],
      education: [{ degree: 'M.F.A.', field: 'Fine Arts', institution: 'RISD', year: 2012 }],
      publications: [],
      courses: [],
      bio: 'Dr. Susan Lee is a digital artist and art historian.',
      hireDate: '2016-08-20',
      tenure: true,
      status: 'Active',
      workload: 75,
      maxCourses: 4,
      preferredSchedule: ['Morning']
    },
    {
      id: '12',
      firstName: 'Prof. Thomas',
      lastName: 'Miller',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Professor of Philosophy',
      rank: 'Professor',
      department: 'Philosophy',
      office: 'Humanities Building, Room 301',
      officeHours: 'Tue/Thu 1-3 PM',
      specializations: ['Ethics', 'Logic', 'Philosophy of Mind'],
      researchInterests: ['Moral Philosophy', 'Consciousness', 'Free Will'],
      education: [{ degree: 'Ph.D.', field: 'Philosophy', institution: 'Oxford University', year: 2000 }],
      publications: [],
      courses: [],
      bio: 'Prof. Thomas Miller is a philosopher specializing in ethics and philosophy of mind.',
      hireDate: '2003-09-01',
      tenure: true,
      status: 'Sabbatical',
      workload: 0,
      maxCourses: 3,
      preferredSchedule: ['Afternoon']
    },
    {
      id: '13',
      firstName: 'Dr. Rachel',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Assistant Professor of Sociology',
      rank: 'Assistant Professor',
      department: 'Sociology',
      office: 'Social Sciences Building, Room 205',
      officeHours: 'Mon/Wed 2-4 PM',
      specializations: ['Social Theory', 'Urban Sociology', 'Research Methods'],
      researchInterests: ['Social Inequality', 'Community Development', 'Social Change'],
      education: [{ degree: 'Ph.D.', field: 'Sociology', institution: 'University of Chicago', year: 2020 }],
      publications: [],
      courses: [],
      bio: 'Dr. Rachel Davis studies social inequality and community development.',
      hireDate: '2022-08-15',
      tenure: false,
      status: 'Active',
      workload: 70,
      maxCourses: 3,
      preferredSchedule: ['Afternoon']
    },
    // Faculty members 14-22 (reaching 22 total)
    {
      id: '14',
      firstName: 'Dr. Kevin',
      lastName: 'Wang',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Adjunct Professor of Music',
      rank: 'Adjunct Professor',
      department: 'Music',
      office: 'Music Building, Room 102',
      officeHours: 'By appointment',
      specializations: ['Music Theory', 'Composition', 'Piano'],
      researchInterests: ['Contemporary Music', 'Music Technology', 'Performance'],
      education: [{ degree: 'D.M.A.', field: 'Music Composition', institution: 'Juilliard', year: 2017 }],
      publications: [],
      courses: [],
      bio: 'Dr. Kevin Wang is a composer and pianist.',
      hireDate: '2019-01-15',
      tenure: false,
      status: 'Active',
      workload: 50,
      maxCourses: 2,
      preferredSchedule: ['Evening']
    },
    {
      id: '15',
      firstName: 'Dr. Patricia',
      lastName: 'Taylor',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Professor of Nursing',
      rank: 'Professor',
      department: 'Nursing',
      office: 'Health Sciences Building, Room 301',
      officeHours: 'Tue/Thu 10 AM-12 PM',
      specializations: ['Clinical Nursing', 'Nursing Education', 'Healthcare Management'],
      researchInterests: ['Patient Care', 'Nursing Practice', 'Healthcare Quality'],
      education: [{ degree: 'Ph.D.', field: 'Nursing', institution: 'Johns Hopkins', year: 2008 }],
      publications: [],
      courses: [],
      bio: 'Dr. Patricia Taylor is a clinical nursing expert and educator.',
      hireDate: '2011-08-20',
      tenure: true,
      status: 'Active',
      workload: 85,
      maxCourses: 4,
      preferredSchedule: ['Morning', 'Afternoon']
    },
    {
      id: '16',
      firstName: 'Dr. Mark',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Associate Professor of Political Science',
      rank: 'Associate Professor',
      department: 'Political Science',
      office: 'Government Building, Room 205',
      officeHours: 'Mon/Wed/Fri 1-2 PM',
      specializations: ['American Politics', 'Public Policy', 'Comparative Government'],
      researchInterests: ['Electoral Systems', 'Policy Analysis', 'Democratic Institutions'],
      education: [{ degree: 'Ph.D.', field: 'Political Science', institution: 'Georgetown University', year: 2013 }],
      publications: [],
      courses: [],
      bio: 'Dr. Mark Johnson studies American political institutions and public policy.',
      hireDate: '2016-01-10',
      tenure: true,
      status: 'Active',
      workload: 75,
      maxCourses: 3,
      preferredSchedule: ['Afternoon']
    },
    // Final faculty members (17-22)
    {
      id: '17',
      firstName: 'Dr. Linda',
      lastName: 'Martinez',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Assistant Professor of Spanish',
      rank: 'Assistant Professor',
      department: 'Modern Languages',
      office: 'Language Building, Room 103',
      officeHours: 'Tue/Thu 11 AM-1 PM',
      specializations: ['Spanish Literature', 'Latin American Studies', 'Translation'],
      researchInterests: ['Contemporary Literature', 'Cultural Studies', 'Bilingualism'],
      education: [{ degree: 'Ph.D.', field: 'Spanish Literature', institution: 'University of Texas', year: 2021 }],
      publications: [],
      courses: [],
      bio: 'Dr. Linda Martinez specializes in contemporary Spanish and Latin American literature.',
      hireDate: '2023-08-15',
      tenure: false,
      status: 'Active',
      workload: 70,
      maxCourses: 4,
      preferredSchedule: ['Morning', 'Afternoon']
    },
    {
      id: '18',
      firstName: 'Prof. William',
      lastName: 'Clark',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Professor of Geology',
      rank: 'Professor',
      department: 'Earth Sciences',
      office: 'Science Building, Room 401',
      officeHours: 'Mon/Wed 3-5 PM',
      specializations: ['Structural Geology', 'Petrology', 'Field Studies'],
      researchInterests: ['Mountain Building', 'Rock Formation', 'Geological Mapping'],
      education: [{ degree: 'Ph.D.', field: 'Geology', institution: 'Colorado School of Mines', year: 2002 }],
      publications: [],
      courses: [],
      bio: 'Prof. William Clark is a structural geologist with extensive field experience.',
      hireDate: '2005-09-01',
      tenure: true,
      status: 'Active',
      workload: 80,
      maxCourses: 3,
      preferredSchedule: ['Morning']
    },
    {
      id: '19',
      firstName: 'Dr. Nancy',
      lastName: 'White',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Associate Professor of Anthropology',
      rank: 'Associate Professor',
      department: 'Anthropology',
      office: 'Social Sciences Building, Room 301',
      officeHours: 'Tue/Thu 2-4 PM',
      specializations: ['Cultural Anthropology', 'Ethnography', 'Medical Anthropology'],
      researchInterests: ['Health Practices', 'Cultural Change', 'Community Studies'],
      education: [{ degree: 'Ph.D.', field: 'Anthropology', institution: 'University of California, San Diego', year: 2014 }],
      publications: [],
      courses: [],
      bio: 'Dr. Nancy White studies cultural practices and health in diverse communities.',
      hireDate: '2017-01-15',
      tenure: true,
      status: 'Active',
      workload: 75,
      maxCourses: 3,
      preferredSchedule: ['Afternoon']
    },
    {
      id: '20',
      firstName: 'Dr. Christopher',
      lastName: 'Green',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Assistant Professor of Environmental Science',
      rank: 'Assistant Professor',
      department: 'Environmental Science',
      office: 'Environmental Building, Room 201',
      officeHours: 'Mon/Wed/Fri 10 AM-11 AM',
      specializations: ['Climate Science', 'Environmental Policy', 'Sustainability'],
      researchInterests: ['Climate Change', 'Renewable Energy', 'Environmental Impact'],
      education: [{ degree: 'Ph.D.', field: 'Environmental Science', institution: 'University of Washington', year: 2020 }],
      publications: [],
      courses: [],
      bio: 'Dr. Christopher Green researches climate change and sustainable energy solutions.',
      hireDate: '2022-01-10',
      tenure: false,
      status: 'Active',
      workload: 70,
      maxCourses: 3,
      preferredSchedule: ['Morning']
    },
    {
      id: '21',
      firstName: 'Dr. Barbara',
      lastName: 'Adams',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Professor Emeritus of Literature',
      rank: 'Professor',
      department: 'English',
      office: 'Emeritus Office',
      officeHours: 'By appointment',
      specializations: ['Victorian Literature', 'Literary Criticism', 'Women Writers'],
      researchInterests: ['19th Century Literature', 'Feminist Criticism', 'Literary History'],
      education: [{ degree: 'Ph.D.', field: 'English Literature', institution: 'Harvard University', year: 1985 }],
      publications: [],
      courses: [],
      bio: 'Dr. Barbara Adams is a distinguished scholar of Victorian literature.',
      hireDate: '1988-09-01',
      tenure: true,
      status: 'Retired',
      workload: 0,
      maxCourses: 0,
      preferredSchedule: []
    },
    {
      id: '22',
      firstName: 'Dr. Daniel',
      lastName: 'Scott',
      email: '<EMAIL>',
      phone: '(*************',
      title: 'Lecturer in Business Administration',
      rank: 'Lecturer',
      department: 'Business',
      office: 'Business Building, Room 105',
      officeHours: 'Mon/Wed 1-3 PM',
      specializations: ['Marketing', 'Business Strategy', 'Entrepreneurship'],
      researchInterests: ['Digital Marketing', 'Startup Management', 'Consumer Behavior'],
      education: [{ degree: 'M.B.A.', field: 'Business Administration', institution: 'Wharton School', year: 2016 }],
      publications: [],
      courses: [],
      bio: 'Dr. Daniel Scott brings industry experience in marketing and entrepreneurship.',
      hireDate: '2020-01-15',
      tenure: false,
      status: 'On Leave',
      workload: 0,
      maxCourses: 4,
      preferredSchedule: ['Afternoon', 'Evening']
    }
  ])

  // Reactive state
  const faculty = ref<Faculty[]>(mockFaculty.value)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed properties
  const facultyStats = computed((): FacultyStats => {
    const total = faculty.value.length
    const byDepartment: Record<string, number> = {}
    const byRank: Record<string, number> = {}
    const byStatus: Record<string, number> = {}
    let totalWorkload = 0
    let totalCourses = 0

    faculty.value.forEach(f => {
      byDepartment[f.department] = (byDepartment[f.department] || 0) + 1
      byRank[f.rank] = (byRank[f.rank] || 0) + 1
      byStatus[f.status] = (byStatus[f.status] || 0) + 1
      totalWorkload += f.workload
      totalCourses += f.courses.length
    })

    return {
      total,
      byDepartment,
      byRank,
      byStatus,
      averageWorkload: total > 0 ? Math.round(totalWorkload / total) : 0,
      totalCourses
    }
  })

  // Methods
  const getFacultyById = (id: string): Faculty | undefined => {
    return faculty.value.find(f => f.id === id)
  }

  const searchFaculty = (filters: Partial<FacultyFilters>): Faculty[] => {
    return faculty.value.filter(f => {
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        const fullName = `${f.firstName} ${f.lastName}`.toLowerCase()
        if (!fullName.includes(searchTerm) && 
            !f.email.toLowerCase().includes(searchTerm) &&
            !f.specializations.some(s => s.toLowerCase().includes(searchTerm))) {
          return false
        }
      }
      
      if (filters.department && f.department !== filters.department) return false
      if (filters.rank && f.rank !== filters.rank) return false
      if (filters.status && f.status !== filters.status) return false
      if (filters.researchArea && !f.researchInterests.some(r =>
        r.toLowerCase().includes(filters.researchArea.toLowerCase()))) return false
      
      return true
    })
  }

  const addFaculty = async (newFaculty: Omit<Faculty, 'id'>): Promise<Faculty> => {
    loading.value = true
    try {
      const faculty_member: Faculty = {
        ...newFaculty,
        id: Date.now().toString()
      }
      faculty.value.push(faculty_member)
      return faculty_member
    } finally {
      loading.value = false
    }
  }

  const updateFaculty = async (id: string, updates: Partial<Faculty>): Promise<Faculty | null> => {
    loading.value = true
    try {
      const index = faculty.value.findIndex(f => f.id === id)
      if (index === -1) return null
      
      faculty.value[index] = { ...faculty.value[index], ...updates }
      return faculty.value[index]
    } finally {
      loading.value = false
    }
  }

  const deleteFaculty = async (id: string): Promise<boolean> => {
    loading.value = true
    try {
      const index = faculty.value.findIndex(f => f.id === id)
      if (index === -1) return false
      
      faculty.value.splice(index, 1)
      return true
    } finally {
      loading.value = false
    }
  }

  const assignCourse = async (facultyId: string, course: Omit<CourseAssignment, 'id'>): Promise<boolean> => {
    const faculty_member = getFacultyById(facultyId)
    if (!faculty_member) return false
    
    const newCourse: CourseAssignment = {
      ...course,
      id: Date.now().toString()
    }
    
    faculty_member.courses.push(newCourse)
    return true
  }

  const removeCourseAssignment = async (facultyId: string, courseId: string): Promise<boolean> => {
    const faculty_member = getFacultyById(facultyId)
    if (!faculty_member) return false
    
    const courseIndex = faculty_member.courses.findIndex(c => c.id === courseId)
    if (courseIndex === -1) return false
    
    faculty_member.courses.splice(courseIndex, 1)
    return true
  }

  return {
    faculty: readonly(faculty),
    loading: readonly(loading),
    error: readonly(error),
    facultyStats,
    getFacultyById,
    searchFaculty,
    addFaculty,
    updateFaculty,
    deleteFaculty,
    assignCourse,
    removeCourseAssignment
  }
}
