export interface ActivityLog {
  id: string
  timestamp: string
  userId: string
  userName: string
  userRole: string
  action: string
  resource: string
  resourceId?: string
  details: string
  ipAddress: string
  userAgent: string
  status: 'success' | 'warning' | 'error'
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: 'authentication' | 'authorization' | 'data_access' | 'data_modification' | 'system' | 'security'
}

export interface SystemMaintenance {
  id: string
  title: string
  description: string
  type: 'database' | 'cache' | 'backup' | 'update' | 'security' | 'performance'
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'critical'
  scheduledDate: string
  startedAt?: string
  completedAt?: string
  estimatedDuration: number // in minutes
  actualDuration?: number // in minutes
  performedBy: string
  affectedSystems: string[]
  downtime: boolean
  results?: string
  errors?: string[]
  createdAt: string
  updatedAt: string
}

export interface SystemNotification {
  id: string
  title: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success' | 'maintenance'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  targetAudience: 'all' | 'students' | 'faculty' | 'staff' | 'admins'
  channels: ('email' | 'sms' | 'push' | 'banner' | 'popup')[]
  status: 'draft' | 'scheduled' | 'sent' | 'failed' | 'cancelled'
  scheduledDate?: string
  sentAt?: string
  expiresAt?: string
  createdBy: string
  recipients?: {
    total: number
    sent: number
    delivered: number
    failed: number
  }
  createdAt: string
  updatedAt: string
}

export interface SystemStats {
  totalLogs: number
  todayLogs: number
  errorLogs: number
  warningLogs: number
  activeMaintenance: number
  scheduledMaintenance: number
  activeNotifications: number
  systemUptime: number
  lastBackup: string
  diskUsage: number
  memoryUsage: number
}

export const useSystemData = () => {
  // Mock activity logs data
  const mockActivityLogs = ref<ActivityLog[]>([
    {
      id: '1',
      timestamp: '2024-12-05T14:30:00Z',
      userId: 'admin001',
      userName: 'John Admin',
      userRole: 'Administrator',
      action: 'User Login',
      resource: 'Authentication System',
      details: 'Successful admin login from dashboard',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      status: 'success',
      severity: 'low',
      category: 'authentication'
    },
    {
      id: '2',
      timestamp: '2024-12-05T14:25:00Z',
      userId: 'student123',
      userName: 'Alice Johnson',
      userRole: 'Student',
      action: 'Course Registration',
      resource: 'Academic System',
      resourceId: 'CS101',
      details: 'Student registered for CS 101 - Introduction to Computer Science',
      ipAddress: '*********',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      status: 'success',
      severity: 'low',
      category: 'data_modification'
    },
    {
      id: '3',
      timestamp: '2024-12-05T14:20:00Z',
      userId: 'faculty456',
      userName: 'Dr. Sarah Johnson',
      userRole: 'Faculty',
      action: 'Grade Update',
      resource: 'Gradebook System',
      resourceId: 'CS101-F24',
      details: 'Updated final grades for CS 101 Fall 2024',
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      status: 'success',
      severity: 'medium',
      category: 'data_modification'
    },
    {
      id: '4',
      timestamp: '2024-12-05T14:15:00Z',
      userId: 'system',
      userName: 'System Process',
      userRole: 'System',
      action: 'Database Backup',
      resource: 'Database System',
      details: 'Automated daily database backup completed successfully',
      ipAddress: '127.0.0.1',
      userAgent: 'System/1.0',
      status: 'success',
      severity: 'low',
      category: 'system'
    },
    {
      id: '5',
      timestamp: '2024-12-05T14:10:00Z',
      userId: 'unknown',
      userName: 'Unknown User',
      userRole: 'Unknown',
      action: 'Failed Login Attempt',
      resource: 'Authentication System',
      details: 'Multiple failed login attempts detected from suspicious IP',
      ipAddress: '************',
      userAgent: 'curl/7.68.0',
      status: 'error',
      severity: 'high',
      category: 'security'
    }
  ])

  // Mock maintenance tasks data
  const mockMaintenance = ref<SystemMaintenance[]>([
    {
      id: '1',
      title: 'Database Performance Optimization',
      description: 'Optimize database queries and rebuild indexes for improved performance',
      type: 'database',
      status: 'completed',
      priority: 'medium',
      scheduledDate: '2024-12-05T02:00:00Z',
      startedAt: '2024-12-05T02:00:00Z',
      completedAt: '2024-12-05T03:30:00Z',
      estimatedDuration: 120,
      actualDuration: 90,
      performedBy: 'System Administrator',
      affectedSystems: ['Database Server', 'Web Application'],
      downtime: false,
      results: 'Successfully optimized 15 slow queries and rebuilt 8 indexes. Query performance improved by 35%.',
      createdAt: '2024-12-04T10:00:00Z',
      updatedAt: '2024-12-05T03:30:00Z'
    },
    {
      id: '2',
      title: 'Security Patch Installation',
      description: 'Install critical security patches for operating system and applications',
      type: 'security',
      status: 'scheduled',
      priority: 'high',
      scheduledDate: '2024-12-06T01:00:00Z',
      estimatedDuration: 60,
      performedBy: 'Security Team',
      affectedSystems: ['Web Servers', 'Application Servers', 'Database Server'],
      downtime: true,
      createdAt: '2024-12-05T09:00:00Z',
      updatedAt: '2024-12-05T09:00:00Z'
    },
    {
      id: '3',
      title: 'Cache Server Maintenance',
      description: 'Clear cache and restart cache servers for optimal performance',
      type: 'cache',
      status: 'in_progress',
      priority: 'low',
      scheduledDate: '2024-12-05T15:00:00Z',
      startedAt: '2024-12-05T15:00:00Z',
      estimatedDuration: 30,
      performedBy: 'DevOps Team',
      affectedSystems: ['Cache Servers', 'CDN'],
      downtime: false,
      createdAt: '2024-12-05T14:00:00Z',
      updatedAt: '2024-12-05T15:00:00Z'
    }
  ])

  // Mock notifications data
  const mockNotifications = ref<SystemNotification[]>([
    {
      id: '1',
      title: 'System Maintenance Scheduled',
      message: 'The system will undergo scheduled maintenance on December 6th from 1:00 AM to 2:00 AM EST. During this time, some services may be temporarily unavailable.',
      type: 'maintenance',
      priority: 'medium',
      targetAudience: 'all',
      channels: ['email', 'banner'],
      status: 'sent',
      scheduledDate: '2024-12-05T12:00:00Z',
      sentAt: '2024-12-05T12:00:00Z',
      expiresAt: '2024-12-06T02:00:00Z',
      createdBy: 'System Administrator',
      recipients: {
        total: 5420,
        sent: 5420,
        delivered: 5398,
        failed: 22
      },
      createdAt: '2024-12-05T11:30:00Z',
      updatedAt: '2024-12-05T12:00:00Z'
    },
    {
      id: '2',
      title: 'New Feature Release',
      message: 'We are excited to announce the release of our new mobile app! Download it now from the App Store or Google Play.',
      type: 'info',
      priority: 'low',
      targetAudience: 'students',
      channels: ['email', 'push'],
      status: 'scheduled',
      scheduledDate: '2024-12-06T09:00:00Z',
      createdBy: 'Marketing Team',
      createdAt: '2024-12-05T10:00:00Z',
      updatedAt: '2024-12-05T10:00:00Z'
    },
    {
      id: '3',
      title: 'Security Alert',
      message: 'We have detected unusual login activity. Please review your account security settings and change your password if necessary.',
      type: 'warning',
      priority: 'high',
      targetAudience: 'all',
      channels: ['email', 'sms', 'push'],
      status: 'draft',
      createdBy: 'Security Team',
      createdAt: '2024-12-05T14:00:00Z',
      updatedAt: '2024-12-05T14:00:00Z'
    }
  ])

  // Reactive state
  const activityLogs = ref<ActivityLog[]>(mockActivityLogs.value)
  const maintenanceTasks = ref<SystemMaintenance[]>(mockMaintenance.value)
  const notifications = ref<SystemNotification[]>(mockNotifications.value)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed properties
  const systemStats = computed((): SystemStats => {
    const totalLogs = activityLogs.value.length
    const today = new Date().toISOString().split('T')[0]
    const todayLogs = activityLogs.value.filter(log => 
      log.timestamp.startsWith(today)
    ).length
    const errorLogs = activityLogs.value.filter(log => log.status === 'error').length
    const warningLogs = activityLogs.value.filter(log => log.status === 'warning').length
    const activeMaintenance = maintenanceTasks.value.filter(task => 
      task.status === 'in_progress'
    ).length
    const scheduledMaintenance = maintenanceTasks.value.filter(task => 
      task.status === 'scheduled'
    ).length
    const activeNotifications = notifications.value.filter(notif => 
      notif.status === 'sent' && (!notif.expiresAt || new Date(notif.expiresAt) > new Date())
    ).length

    return {
      totalLogs,
      todayLogs,
      errorLogs,
      warningLogs,
      activeMaintenance,
      scheduledMaintenance,
      activeNotifications,
      systemUptime: 99.8,
      lastBackup: '2024-12-05T02:00:00Z',
      diskUsage: 68,
      memoryUsage: 45
    }
  })

  // Activity log methods
  const searchActivityLogs = (query: string, filters: any = {}): ActivityLog[] => {
    return activityLogs.value.filter(log => {
      const matchesQuery = !query || 
        log.userName.toLowerCase().includes(query.toLowerCase()) ||
        log.action.toLowerCase().includes(query.toLowerCase()) ||
        log.resource.toLowerCase().includes(query.toLowerCase()) ||
        log.details.toLowerCase().includes(query.toLowerCase())

      const matchesCategory = !filters.category || log.category === filters.category
      const matchesStatus = !filters.status || log.status === filters.status
      const matchesSeverity = !filters.severity || log.severity === filters.severity
      const matchesUser = !filters.userId || log.userId === filters.userId

      return matchesQuery && matchesCategory && matchesStatus && matchesSeverity && matchesUser
    }).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }

  // Maintenance methods
  const addMaintenanceTask = async (task: Omit<SystemMaintenance, 'id' | 'createdAt' | 'updatedAt'>): Promise<SystemMaintenance> => {
    loading.value = true
    try {
      const newTask: SystemMaintenance = {
        ...task,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      maintenanceTasks.value.push(newTask)
      return newTask
    } finally {
      loading.value = false
    }
  }

  const updateMaintenanceTask = async (id: string, updates: Partial<SystemMaintenance>): Promise<SystemMaintenance | null> => {
    loading.value = true
    try {
      const index = maintenanceTasks.value.findIndex(task => task.id === id)
      if (index === -1) return null
      
      maintenanceTasks.value[index] = { 
        ...maintenanceTasks.value[index], 
        ...updates, 
        updatedAt: new Date().toISOString() 
      }
      return maintenanceTasks.value[index]
    } finally {
      loading.value = false
    }
  }

  // Notification methods
  const addNotification = async (notification: Omit<SystemNotification, 'id' | 'createdAt' | 'updatedAt'>): Promise<SystemNotification> => {
    loading.value = true
    try {
      const newNotification: SystemNotification = {
        ...notification,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      notifications.value.push(newNotification)
      return newNotification
    } finally {
      loading.value = false
    }
  }

  const sendNotification = async (id: string): Promise<boolean> => {
    loading.value = true
    try {
      const notification = notifications.value.find(n => n.id === id)
      if (!notification) return false
      
      notification.status = 'sent'
      notification.sentAt = new Date().toISOString()
      notification.updatedAt = new Date().toISOString()
      
      // Simulate recipient tracking
      notification.recipients = {
        total: Math.floor(Math.random() * 10000) + 1000,
        sent: 0,
        delivered: 0,
        failed: 0
      }
      notification.recipients.sent = notification.recipients.total
      notification.recipients.delivered = Math.floor(notification.recipients.total * 0.98)
      notification.recipients.failed = notification.recipients.total - notification.recipients.delivered
      
      return true
    } finally {
      loading.value = false
    }
  }

  return {
    activityLogs: readonly(activityLogs),
    maintenanceTasks: readonly(maintenanceTasks),
    notifications: readonly(notifications),
    loading: readonly(loading),
    error: readonly(error),
    systemStats,
    searchActivityLogs,
    addMaintenanceTask,
    updateMaintenanceTask,
    addNotification,
    sendNotification
  }
}
