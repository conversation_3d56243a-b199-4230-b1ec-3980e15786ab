export interface Budget {
  id: string
  name: string
  departmentId: string
  department: string
  fiscalYear: number
  totalBudget: number
  allocatedAmount: number
  spentAmount: number
  remainingAmount: number
  categories: BudgetCategory[]
  status: 'draft' | 'approved' | 'active' | 'closed'
  approvedBy?: string
  approvedDate?: string
  createdAt: string
  updatedAt: string
}

export interface BudgetCategory {
  id: string
  name: string
  allocatedAmount: number
  spentAmount: number
  description: string
  type: 'personnel' | 'equipment' | 'supplies' | 'travel' | 'other'
}

export interface TuitionRate {
  id: string
  name: string
  type: 'undergraduate' | 'graduate' | 'doctoral' | 'continuing_education'
  residencyStatus: 'in_state' | 'out_of_state' | 'international'
  creditHourRate: number
  flatRate?: number
  fees: TuitionFee[]
  effectiveDate: string
  expirationDate?: string
  status: 'active' | 'inactive' | 'archived'
  createdAt: string
  updatedAt: string
}

export interface TuitionFee {
  id: string
  name: string
  amount: number
  type: 'mandatory' | 'optional' | 'program_specific'
  description: string
  applicablePrograms?: string[]
}

export interface FinancialReport {
  id: string
  title: string
  type: 'revenue' | 'expenses' | 'budget_variance' | 'enrollment_revenue' | 'department_summary'
  period: 'monthly' | 'quarterly' | 'yearly' | 'custom'
  startDate: string
  endDate: string
  data: any
  generatedBy: string
  generatedAt: string
  status: 'generating' | 'completed' | 'error'
  downloadUrl?: string
}

export interface FinancialStats {
  totalBudget: number
  totalSpent: number
  totalRevenue: number
  budgetUtilization: number
  revenueGrowth: number
  activeBudgets: number
  pendingApprovals: number
  overdraftAlerts: number
}

export const useFinancialData = () => {
  // Mock budgets data
  const mockBudgets = ref<Budget[]>([
    {
      id: '1',
      name: 'Computer Science Department Budget FY2024',
      departmentId: 'cs',
      department: 'Computer Science',
      fiscalYear: 2024,
      totalBudget: 2500000,
      allocatedAmount: 2200000,
      spentAmount: 1650000,
      remainingAmount: 550000,
      categories: [
        {
          id: '1',
          name: 'Faculty Salaries',
          allocatedAmount: 1500000,
          spentAmount: 1200000,
          description: 'Full-time and part-time faculty compensation',
          type: 'personnel'
        },
        {
          id: '2',
          name: 'Equipment & Technology',
          allocatedAmount: 400000,
          spentAmount: 250000,
          description: 'Computers, servers, and lab equipment',
          type: 'equipment'
        },
        {
          id: '3',
          name: 'Office Supplies',
          allocatedAmount: 50000,
          spentAmount: 35000,
          description: 'General office and classroom supplies',
          type: 'supplies'
        }
      ],
      status: 'active',
      approvedBy: 'Dr. Jane Smith',
      approvedDate: '2024-01-15T00:00:00Z',
      createdAt: '2024-01-10T00:00:00Z',
      updatedAt: '2024-08-20T00:00:00Z'
    },
    {
      id: '2',
      name: 'Mathematics Department Budget FY2024',
      departmentId: 'math',
      department: 'Mathematics',
      fiscalYear: 2024,
      totalBudget: 1800000,
      allocatedAmount: 1600000,
      spentAmount: 1100000,
      remainingAmount: 500000,
      categories: [
        {
          id: '4',
          name: 'Faculty Salaries',
          allocatedAmount: 1200000,
          spentAmount: 900000,
          description: 'Full-time and part-time faculty compensation',
          type: 'personnel'
        },
        {
          id: '5',
          name: 'Research Materials',
          allocatedAmount: 200000,
          spentAmount: 120000,
          description: 'Books, journals, and research software',
          type: 'supplies'
        }
      ],
      status: 'active',
      approvedBy: 'Dr. Robert Johnson',
      approvedDate: '2024-01-20T00:00:00Z',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-08-18T00:00:00Z'
    }
  ])

  // Mock tuition rates data
  const mockTuitionRates = ref<TuitionRate[]>([
    {
      id: '1',
      name: 'Undergraduate In-State Tuition',
      type: 'undergraduate',
      residencyStatus: 'in_state',
      creditHourRate: 450,
      fees: [
        {
          id: '1',
          name: 'Student Activity Fee',
          amount: 150,
          type: 'mandatory',
          description: 'Supports student organizations and activities'
        },
        {
          id: '2',
          name: 'Technology Fee',
          amount: 200,
          type: 'mandatory',
          description: 'Maintains campus technology infrastructure'
        }
      ],
      effectiveDate: '2024-08-01T00:00:00Z',
      status: 'active',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    },
    {
      id: '2',
      name: 'Undergraduate Out-of-State Tuition',
      type: 'undergraduate',
      residencyStatus: 'out_of_state',
      creditHourRate: 1200,
      fees: [
        {
          id: '3',
          name: 'Student Activity Fee',
          amount: 150,
          type: 'mandatory',
          description: 'Supports student organizations and activities'
        },
        {
          id: '4',
          name: 'Technology Fee',
          amount: 200,
          type: 'mandatory',
          description: 'Maintains campus technology infrastructure'
        },
        {
          id: '5',
          name: 'Non-Resident Fee',
          amount: 500,
          type: 'mandatory',
          description: 'Additional fee for out-of-state students'
        }
      ],
      effectiveDate: '2024-08-01T00:00:00Z',
      status: 'active',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    },
    {
      id: '3',
      name: 'Graduate Tuition',
      type: 'graduate',
      residencyStatus: 'in_state',
      creditHourRate: 650,
      fees: [
        {
          id: '6',
          name: 'Graduate Student Fee',
          amount: 100,
          type: 'mandatory',
          description: 'Supports graduate student services'
        },
        {
          id: '7',
          name: 'Research Fee',
          amount: 300,
          type: 'mandatory',
          description: 'Supports graduate research facilities'
        }
      ],
      effectiveDate: '2024-08-01T00:00:00Z',
      status: 'active',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    }
  ])

  // Mock financial reports data
  const mockReports = ref<FinancialReport[]>([
    {
      id: '1',
      title: 'Q3 2024 Revenue Report',
      type: 'revenue',
      period: 'quarterly',
      startDate: '2024-07-01T00:00:00Z',
      endDate: '2024-09-30T00:00:00Z',
      data: {
        totalRevenue: 15750000,
        tuitionRevenue: 12500000,
        feesRevenue: 2250000,
        otherRevenue: 1000000
      },
      generatedBy: 'Financial Office',
      generatedAt: '2024-10-01T00:00:00Z',
      status: 'completed',
      downloadUrl: '/reports/q3-2024-revenue.pdf'
    },
    {
      id: '2',
      title: 'Department Budget Variance Analysis',
      type: 'budget_variance',
      period: 'yearly',
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2024-12-31T00:00:00Z',
      data: {
        totalBudget: 25000000,
        totalSpent: 18750000,
        variance: 6250000,
        departments: [
          { name: 'Computer Science', budget: 2500000, spent: 1650000, variance: 850000 },
          { name: 'Mathematics', budget: 1800000, spent: 1100000, variance: 700000 }
        ]
      },
      generatedBy: 'Budget Office',
      generatedAt: '2024-08-15T00:00:00Z',
      status: 'completed',
      downloadUrl: '/reports/budget-variance-2024.pdf'
    }
  ])

  // Reactive state
  const budgets = ref<Budget[]>(mockBudgets.value)
  const tuitionRates = ref<TuitionRate[]>(mockTuitionRates.value)
  const reports = ref<FinancialReport[]>(mockReports.value)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed properties
  const financialStats = computed((): FinancialStats => {
    const totalBudget = budgets.value.reduce((sum, b) => sum + b.totalBudget, 0)
    const totalSpent = budgets.value.reduce((sum, b) => sum + b.spentAmount, 0)
    const totalRevenue = 15750000 // Mock revenue data
    const budgetUtilization = totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0
    const revenueGrowth = 8.5 // Mock growth percentage
    const activeBudgets = budgets.value.filter(b => b.status === 'active').length
    const pendingApprovals = budgets.value.filter(b => b.status === 'draft').length
    const overdraftAlerts = budgets.value.filter(b => b.spentAmount > b.allocatedAmount).length

    return {
      totalBudget,
      totalSpent,
      totalRevenue,
      budgetUtilization,
      revenueGrowth,
      activeBudgets,
      pendingApprovals,
      overdraftAlerts
    }
  })

  // Budget management methods
  const getBudgetById = (id: string): Budget | undefined => {
    return budgets.value.find(b => b.id === id)
  }

  const searchBudgets = (query: string, filters: any = {}): Budget[] => {
    return budgets.value.filter(budget => {
      const matchesQuery = !query || 
        budget.name.toLowerCase().includes(query.toLowerCase()) ||
        budget.department.toLowerCase().includes(query.toLowerCase())

      const matchesDepartment = !filters.department || budget.department === filters.department
      const matchesStatus = !filters.status || budget.status === filters.status
      const matchesFiscalYear = !filters.fiscalYear || budget.fiscalYear === filters.fiscalYear

      return matchesQuery && matchesDepartment && matchesStatus && matchesFiscalYear
    })
  }

  const addBudget = async (newBudget: Omit<Budget, 'id' | 'createdAt' | 'updatedAt'>): Promise<Budget> => {
    loading.value = true
    try {
      const budget: Budget = {
        ...newBudget,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      budgets.value.push(budget)
      return budget
    } finally {
      loading.value = false
    }
  }

  const updateBudget = async (id: string, updates: Partial<Budget>): Promise<Budget | null> => {
    loading.value = true
    try {
      const index = budgets.value.findIndex(b => b.id === id)
      if (index === -1) return null
      
      budgets.value[index] = { 
        ...budgets.value[index], 
        ...updates, 
        updatedAt: new Date().toISOString() 
      }
      return budgets.value[index]
    } finally {
      loading.value = false
    }
  }

  const deleteBudget = async (id: string): Promise<boolean> => {
    loading.value = true
    try {
      const index = budgets.value.findIndex(b => b.id === id)
      if (index === -1) return false
      
      budgets.value.splice(index, 1)
      return true
    } finally {
      loading.value = false
    }
  }

  // Tuition management methods
  const getTuitionRateById = (id: string): TuitionRate | undefined => {
    return tuitionRates.value.find(t => t.id === id)
  }

  const calculateTuition = (rateId: string, creditHours: number): number => {
    const rate = getTuitionRateById(rateId)
    if (!rate) return 0

    const tuitionCost = rate.flatRate || (rate.creditHourRate * creditHours)
    const feesCost = rate.fees.reduce((sum, fee) => sum + fee.amount, 0)
    
    return tuitionCost + feesCost
  }

  // Report management methods
  const generateReport = async (reportConfig: Omit<FinancialReport, 'id' | 'generatedAt' | 'status'>): Promise<FinancialReport> => {
    loading.value = true
    try {
      const report: FinancialReport = {
        ...reportConfig,
        id: Date.now().toString(),
        generatedAt: new Date().toISOString(),
        status: 'generating'
      }
      reports.value.push(report)
      
      // Simulate report generation
      setTimeout(() => {
        report.status = 'completed'
        report.downloadUrl = `/reports/${report.id}.pdf`
      }, 2000)
      
      return report
    } finally {
      loading.value = false
    }
  }

  return {
    budgets: readonly(budgets),
    tuitionRates: readonly(tuitionRates),
    reports: readonly(reports),
    loading: readonly(loading),
    error: readonly(error),
    financialStats,
    getBudgetById,
    searchBudgets,
    addBudget,
    updateBudget,
    deleteBudget,
    getTuitionRateById,
    calculateTuition,
    generateReport
  }
}
