import { navigationConfig, publicNavigation, type NavigationItem } from '~/config/navigation'

export const useNavigation = () => {
  const route = useRoute()
  
  // Mock user data - will be replaced with real auth later
  const mockUser = ref({
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin', // Change this to test different roles: 'admin', 'student', 'faculty'
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facepad&facepad=2&w=256&h=256&q=80'
  })

  const currentUser = computed(() => mockUser.value)
  
  const userRole = computed(() => currentUser.value?.role || 'guest')
  
  const navigationItems = computed(() => {
    if (!currentUser.value) {
      return publicNavigation
    }
    
    const role = userRole.value
    return navigationConfig[role] || publicNavigation
  })
  
  const isActiveRoute = (path: string): boolean => {
    if (path === '/') {
      return route.path === '/'
    }
    return route.path.startsWith(path)
  }
  
  const getActiveNavigationItem = (items: NavigationItem[]): NavigationItem | null => {
    for (const item of items) {
      if (isActiveRoute(item.path)) {
        return item
      }
      if (item.children) {
        const activeChild = getActiveNavigationItem(item.children)
        if (activeChild) {
          return activeChild
        }
      }
    }
    return null
  }
  
  const activeNavigationItem = computed(() => {
    return getActiveNavigationItem(navigationItems.value)
  })
  
  const breadcrumbs = computed(() => {
    const crumbs: Array<{ label: string; path: string }> = []
    const pathSegments = route.path.split('/').filter(Boolean)
    
    // Add home/dashboard as first crumb
    if (currentUser.value) {
      const role = userRole.value
      crumbs.push({
        label: 'Dashboard',
        path: `/${role}`
      })
    } else {
      crumbs.push({
        label: 'Home',
        path: '/'
      })
    }
    
    // Build breadcrumbs from path segments
    let currentPath = ''
    for (let i = 0; i < pathSegments.length; i++) {
      currentPath += `/${pathSegments[i]}`
      
      // Skip the role segment as it's already in dashboard
      if (i === 0 && ['admin', 'student', 'faculty'].includes(pathSegments[i])) {
        continue
      }
      
      // Find the navigation item for this path
      const findItemByPath = (items: NavigationItem[]): NavigationItem | null => {
        for (const item of items) {
          if (item.path === currentPath) {
            return item
          }
          if (item.children) {
            const found = findItemByPath(item.children)
            if (found) return found
          }
        }
        return null
      }
      
      const navItem = findItemByPath(navigationItems.value)
      if (navItem) {
        crumbs.push({
          label: navItem.label,
          path: currentPath
        })
      } else {
        // Fallback to formatted segment name
        const label = pathSegments[i]
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
        crumbs.push({
          label,
          path: currentPath
        })
      }
    }
    
    return crumbs
  })
  
  const hasPermission = (requiredRoles?: string[]): boolean => {
    if (!requiredRoles || requiredRoles.length === 0) {
      return true
    }
    
    const role = userRole.value
    return requiredRoles.includes(role)
  }
  
  const filteredNavigationItems = computed(() => {
    const filterItems = (items: NavigationItem[]): NavigationItem[] => {
      return items.filter(item => {
        if (!hasPermission(item.roles)) {
          return false
        }
        
        if (item.children) {
          item.children = filterItems(item.children)
        }
        
        return true
      })
    }
    
    return filterItems(navigationItems.value)
  })
  
  // Mock authentication functions
  const login = (role: string) => {
    mockUser.value = {
      ...mockUser.value,
      role
    }
  }
  
  const logout = () => {
    mockUser.value = null
    navigateTo('/auth/login')
  }
  
  const switchRole = (role: string) => {
    if (['admin', 'student', 'faculty'].includes(role)) {
      mockUser.value.role = role
      navigateTo(`/${role}`)
    }
  }
  
  return {
    currentUser: readonly(currentUser),
    userRole: readonly(userRole),
    navigationItems: readonly(filteredNavigationItems),
    activeNavigationItem: readonly(activeNavigationItem),
    breadcrumbs: readonly(breadcrumbs),
    isActiveRoute,
    hasPermission,
    login,
    logout,
    switchRole
  }
}
