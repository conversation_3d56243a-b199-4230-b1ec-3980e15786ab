export interface Course {
  id: string
  code: string
  title: string
  description: string
  credits: number
  departmentId: string
  department: string
  instructorIds: string[]
  instructors: string[]
  prerequisites: string[]
  maxEnrollment: number
  currentEnrollment: number
  waitlistCount: number
  schedule: {
    days: string[]
    time: string
    room: string
    building: string
  }
  semester: string
  year: number
  status: 'active' | 'inactive' | 'archived' | 'cancelled'
  tuition: number
  fees: number
  createdAt: string
  updatedAt: string
}

export interface Registration {
  id: string
  studentId: string
  studentName: string
  studentEmail: string
  courseId: string
  courseCode: string
  courseName: string
  registrationDate: string
  status: 'enrolled' | 'waitlisted' | 'dropped' | 'completed'
  grade?: string
  credits: number
  semester: string
  year: number
  paymentStatus: 'paid' | 'pending' | 'overdue'
  registrationPeriod: string
}

export interface AcademicCalendar {
  id: string
  title: string
  description: string
  type: 'semester' | 'break' | 'deadline' | 'event' | 'holiday'
  startDate: string
  endDate: string
  isAllDay: boolean
  semester: string
  year: number
  isRecurring: boolean
  color: string
  location?: string
  createdAt: string
  updatedAt: string
}

export interface RegistrationPeriod {
  id: string
  name: string
  semester: string
  year: number
  startDate: string
  endDate: string
  status: 'upcoming' | 'active' | 'closed'
  priority: number
  eligibleStudentTypes: string[]
  maxCredits: number
  minCredits: number
}

export interface AcademicStats {
  totalCourses: number
  activeCourses: number
  totalEnrollments: number
  totalStudents: number
  averageEnrollment: number
  waitlistTotal: number
  upcomingDeadlines: number
  currentSemester: string
}

export const useAcademicData = () => {
  // Mock courses data
  const mockCourses = ref<Course[]>([
    {
      id: '1',
      code: 'CS 101',
      title: 'Introduction to Computer Science',
      description: 'Fundamental concepts of computer science including programming, algorithms, and data structures.',
      credits: 3,
      departmentId: 'cs',
      department: 'Computer Science',
      instructorIds: ['1'],
      instructors: ['Dr. Sarah Johnson'],
      prerequisites: [],
      maxEnrollment: 50,
      currentEnrollment: 45,
      waitlistCount: 8,
      schedule: {
        days: ['Monday', 'Wednesday', 'Friday'],
        time: '10:00 AM - 11:00 AM',
        room: '204',
        building: 'Computer Science Building'
      },
      semester: 'Fall',
      year: 2024,
      status: 'active',
      tuition: 1200,
      fees: 50,
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-08-20T00:00:00Z'
    },
    {
      id: '2',
      code: 'MATH 201',
      title: 'Calculus II',
      description: 'Advanced calculus including integration techniques, series, and differential equations.',
      credits: 4,
      departmentId: 'math',
      department: 'Mathematics',
      instructorIds: ['2'],
      instructors: ['Dr. Michael Chen'],
      prerequisites: ['MATH 101'],
      maxEnrollment: 35,
      currentEnrollment: 32,
      waitlistCount: 5,
      schedule: {
        days: ['Tuesday', 'Thursday'],
        time: '2:00 PM - 3:30 PM',
        room: '101',
        building: 'Mathematics Building'
      },
      semester: 'Fall',
      year: 2024,
      status: 'active',
      tuition: 1400,
      fees: 75,
      createdAt: '2024-01-10T00:00:00Z',
      updatedAt: '2024-08-18T00:00:00Z'
    },
    {
      id: '3',
      code: 'ENG 102',
      title: 'English Composition',
      description: 'Advanced writing and composition skills for academic and professional communication.',
      credits: 3,
      departmentId: 'eng',
      department: 'English',
      instructorIds: ['6'],
      instructors: ['Dr. James Anderson'],
      prerequisites: ['ENG 101'],
      maxEnrollment: 25,
      currentEnrollment: 23,
      waitlistCount: 2,
      schedule: {
        days: ['Monday', 'Wednesday'],
        time: '1:00 PM - 2:30 PM',
        room: '105',
        building: 'Humanities Building'
      },
      semester: 'Fall',
      year: 2024,
      status: 'active',
      tuition: 1100,
      fees: 40,
      createdAt: '2024-01-12T00:00:00Z',
      updatedAt: '2024-08-15T00:00:00Z'
    }
  ])

  // Mock registrations data
  const mockRegistrations = ref<Registration[]>([
    {
      id: '1',
      studentId: 'S001',
      studentName: 'Alice Johnson',
      studentEmail: '<EMAIL>',
      courseId: '1',
      courseCode: 'CS 101',
      courseName: 'Introduction to Computer Science',
      registrationDate: '2024-08-15T10:30:00Z',
      status: 'enrolled',
      credits: 3,
      semester: 'Fall',
      year: 2024,
      paymentStatus: 'paid',
      registrationPeriod: 'Fall 2024 - Priority Registration'
    },
    {
      id: '2',
      studentId: 'S002',
      studentName: 'Bob Smith',
      studentEmail: '<EMAIL>',
      courseId: '2',
      courseCode: 'MATH 201',
      courseName: 'Calculus II',
      registrationDate: '2024-08-16T14:20:00Z',
      status: 'enrolled',
      credits: 4,
      semester: 'Fall',
      year: 2024,
      paymentStatus: 'pending',
      registrationPeriod: 'Fall 2024 - General Registration'
    },
    {
      id: '3',
      studentId: 'S003',
      studentName: 'Carol Davis',
      studentEmail: '<EMAIL>',
      courseId: '1',
      courseCode: 'CS 101',
      courseName: 'Introduction to Computer Science',
      registrationDate: '2024-08-20T09:15:00Z',
      status: 'waitlisted',
      credits: 3,
      semester: 'Fall',
      year: 2024,
      paymentStatus: 'pending',
      registrationPeriod: 'Fall 2024 - Late Registration'
    }
  ])

  // Mock academic calendar data
  const mockCalendar = ref<AcademicCalendar[]>([
    {
      id: '1',
      title: 'Fall Semester Begins',
      description: 'First day of classes for Fall 2024 semester',
      type: 'semester',
      startDate: '2024-08-26T00:00:00Z',
      endDate: '2024-08-26T23:59:59Z',
      isAllDay: true,
      semester: 'Fall',
      year: 2024,
      isRecurring: false,
      color: 'blue',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    },
    {
      id: '2',
      title: 'Registration Deadline',
      description: 'Last day to register for Fall 2024 courses',
      type: 'deadline',
      startDate: '2024-09-05T23:59:59Z',
      endDate: '2024-09-05T23:59:59Z',
      isAllDay: false,
      semester: 'Fall',
      year: 2024,
      isRecurring: false,
      color: 'red',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    },
    {
      id: '3',
      title: 'Thanksgiving Break',
      description: 'University closed for Thanksgiving holiday',
      type: 'break',
      startDate: '2024-11-28T00:00:00Z',
      endDate: '2024-11-29T23:59:59Z',
      isAllDay: true,
      semester: 'Fall',
      year: 2024,
      isRecurring: true,
      color: 'orange',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    }
  ])

  // Reactive state
  const courses = ref<Course[]>(mockCourses.value)
  const registrations = ref<Registration[]>(mockRegistrations.value)
  const calendar = ref<AcademicCalendar[]>(mockCalendar.value)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed properties
  const academicStats = computed((): AcademicStats => {
    const totalCourses = courses.value.length
    const activeCourses = courses.value.filter(c => c.status === 'active').length
    const totalEnrollments = registrations.value.filter(r => r.status === 'enrolled').length
    const totalStudents = new Set(registrations.value.map(r => r.studentId)).size
    const averageEnrollment = activeCourses > 0 ? 
      Math.round(courses.value.reduce((sum, c) => sum + c.currentEnrollment, 0) / activeCourses) : 0
    const waitlistTotal = courses.value.reduce((sum, c) => sum + c.waitlistCount, 0)
    const upcomingDeadlines = calendar.value.filter(c => 
      c.type === 'deadline' && new Date(c.startDate) > new Date()
    ).length

    return {
      totalCourses,
      activeCourses,
      totalEnrollments,
      totalStudents,
      averageEnrollment,
      waitlistTotal,
      upcomingDeadlines,
      currentSemester: 'Fall 2024'
    }
  })

  // Course management methods
  const getCourseById = (id: string): Course | undefined => {
    return courses.value.find(c => c.id === id)
  }

  const searchCourses = (query: string, filters: any = {}): Course[] => {
    return courses.value.filter(course => {
      const matchesQuery = !query || 
        course.code.toLowerCase().includes(query.toLowerCase()) ||
        course.title.toLowerCase().includes(query.toLowerCase()) ||
        course.department.toLowerCase().includes(query.toLowerCase())

      const matchesDepartment = !filters.department || course.department === filters.department
      const matchesStatus = !filters.status || course.status === filters.status
      const matchesSemester = !filters.semester || course.semester === filters.semester

      return matchesQuery && matchesDepartment && matchesStatus && matchesSemester
    })
  }

  const addCourse = async (newCourse: Omit<Course, 'id' | 'createdAt' | 'updatedAt'>): Promise<Course> => {
    loading.value = true
    try {
      const course: Course = {
        ...newCourse,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      courses.value.push(course)
      return course
    } finally {
      loading.value = false
    }
  }

  const updateCourse = async (id: string, updates: Partial<Course>): Promise<Course | null> => {
    loading.value = true
    try {
      const index = courses.value.findIndex(c => c.id === id)
      if (index === -1) return null
      
      courses.value[index] = { 
        ...courses.value[index], 
        ...updates, 
        updatedAt: new Date().toISOString() 
      }
      return courses.value[index]
    } finally {
      loading.value = false
    }
  }

  const deleteCourse = async (id: string): Promise<boolean> => {
    loading.value = true
    try {
      const index = courses.value.findIndex(c => c.id === id)
      if (index === -1) return false
      
      courses.value.splice(index, 1)
      return true
    } finally {
      loading.value = false
    }
  }

  return {
    courses: readonly(courses),
    registrations: readonly(registrations),
    calendar: readonly(calendar),
    loading: readonly(loading),
    error: readonly(error),
    academicStats,
    getCourseById,
    searchCourses,
    addCourse,
    updateCourse,
    deleteCourse
  }
}
