export interface DashboardMetric {
  id: string
  title: string
  value: string | number
  change?: string
  changeType?: 'increase' | 'decrease' | 'neutral'
  icon: string
  color: string
  description?: string
}

export interface ActivityItem {
  id: string
  type: 'user' | 'course' | 'system' | 'grade' | 'assignment'
  title: string
  description: string
  timestamp: Date
  user?: {
    name: string
    avatar?: string
    role?: string
  }
  metadata?: Record<string, any>
}

export interface NotificationItem {
  id: string
  type: 'info' | 'warning' | 'success' | 'error'
  title: string
  description: string
  timestamp: Date
  read: boolean
  actionUrl?: string
}

export const useDashboardData = () => {
  // Admin Dashboard Data
  const adminMetrics = ref<DashboardMetric[]>([
    {
      id: 'total-students',
      title: 'Total Students',
      value: 2847,
      change: '+12%',
      changeType: 'increase',
      icon: 'i-heroicons-users',
      color: 'blue',
      description: 'Active enrolled students'
    },
    {
      id: 'faculty-members',
      title: 'Faculty Members',
      value: 156,
      change: '+3%',
      changeType: 'increase',
      icon: 'i-heroicons-user-group',
      color: 'green',
      description: 'Active faculty and staff'
    },
    {
      id: 'active-courses',
      title: 'Active Courses',
      value: 342,
      change: '+8%',
      changeType: 'increase',
      icon: 'i-heroicons-academic-cap',
      color: 'yellow',
      description: 'Courses this semester'
    },
    {
      id: 'revenue',
      title: 'Revenue',
      value: '$2.4M',
      change: '+15%',
      changeType: 'increase',
      icon: 'i-heroicons-currency-dollar',
      color: 'purple',
      description: 'Total revenue this quarter'
    }
  ])

  // Student Dashboard Data
  const studentMetrics = ref<DashboardMetric[]>([
    {
      id: 'current-gpa',
      title: 'Current GPA',
      value: '3.8',
      change: '+0.2',
      changeType: 'increase',
      icon: 'i-heroicons-chart-bar-square',
      color: 'green',
      description: 'Cumulative GPA'
    },
    {
      id: 'credits-completed',
      title: 'Credits Completed',
      value: '54/120',
      change: '+15',
      changeType: 'increase',
      icon: 'i-heroicons-book-open',
      color: 'blue',
      description: 'Toward degree completion'
    },
    {
      id: 'current-courses',
      title: 'Current Courses',
      value: 5,
      change: '0',
      changeType: 'neutral',
      icon: 'i-heroicons-academic-cap',
      color: 'purple',
      description: 'Enrolled this semester'
    },
    {
      id: 'upcoming-assignments',
      title: 'Due This Week',
      value: 3,
      change: '-2',
      changeType: 'decrease',
      icon: 'i-heroicons-clipboard-document-list',
      color: 'orange',
      description: 'Assignments and exams'
    }
  ])

  // Faculty Dashboard Data
  const facultyMetrics = ref<DashboardMetric[]>([
    {
      id: 'teaching-load',
      title: 'Teaching Load',
      value: '12 hrs',
      change: '0',
      changeType: 'neutral',
      icon: 'i-heroicons-clock',
      color: 'blue',
      description: 'Credit hours this semester'
    },
    {
      id: 'total-students',
      title: 'Total Students',
      value: 127,
      change: '+8',
      changeType: 'increase',
      icon: 'i-heroicons-users',
      color: 'green',
      description: 'Across all courses'
    },
    {
      id: 'pending-grades',
      title: 'Pending Grades',
      value: 23,
      change: '-5',
      changeType: 'decrease',
      icon: 'i-heroicons-document-text',
      color: 'orange',
      description: 'Assignments to grade'
    },
    {
      id: 'office-hours',
      title: 'Office Hours',
      value: '8 hrs/week',
      change: '0',
      changeType: 'neutral',
      icon: 'i-heroicons-calendar-days',
      color: 'purple',
      description: 'Scheduled availability'
    }
  ])

  // Recent Activities
  const generateActivities = (role: string, count: number = 10): ActivityItem[] => {
    const activities: ActivityItem[] = []
    const now = new Date()

    for (let i = 0; i < count; i++) {
      const timestamp = new Date(now.getTime() - (i * 2 * 60 * 60 * 1000)) // 2 hours apart

      if (role === 'admin') {
        activities.push({
          id: `activity-${i}`,
          type: i % 4 === 0 ? 'user' : i % 4 === 1 ? 'course' : i % 4 === 2 ? 'system' : 'grade',
          title: [
            'New student registration',
            'Course enrollment update',
            'System maintenance completed',
            'Grade report submitted'
          ][i % 4],
          description: [
            'John Doe registered for Fall 2024 semester',
            'CS 101 enrollment increased to 32 students',
            'Database backup completed successfully',
            'Dr. Smith submitted grades for MATH 201'
          ][i % 4],
          timestamp,
          user: {
            name: ['John Doe', 'Jane Smith', 'System', 'Dr. Smith'][i % 4],
            avatar: `https://images.unsplash.com/photo-${1472099645785 + i}?ixlib=rb-1.2.1&auto=format&fit=facepad&facepad=2&w=256&h=256&q=80`,
            role: ['student', 'admin', 'system', 'faculty'][i % 4]
          }
        })
      } else if (role === 'student') {
        activities.push({
          id: `activity-${i}`,
          type: i % 3 === 0 ? 'course' : i % 3 === 1 ? 'assignment' : 'grade',
          title: [
            'Enrolled in new course',
            'Assignment submitted',
            'Grade posted'
          ][i % 3],
          description: [
            'Successfully enrolled in CS 301 - Data Structures',
            'Programming Project 2 submitted for CS 101',
            'Received grade for Calculus II Midterm: B+'
          ][i % 3],
          timestamp
        })
      } else if (role === 'faculty') {
        activities.push({
          id: `activity-${i}`,
          type: i % 3 === 0 ? 'grade' : i % 3 === 1 ? 'course' : 'assignment',
          title: [
            'Grades submitted',
            'Course material updated',
            'Assignment created'
          ][i % 3],
          description: [
            'Submitted final grades for CS 101 midterm exam',
            'Updated lecture slides for Week 8 content',
            'Created new programming assignment for CS 301'
          ][i % 3],
          timestamp,
          user: {
            name: 'Dr. Smith',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facepad&facepad=2&w=256&h=256&q=80',
            role: 'faculty'
          }
        })
      }
    }

    return activities
  }

  // Notifications
  const generateNotifications = (role: string, count: number = 5): NotificationItem[] => {
    const notifications: NotificationItem[] = []
    const now = new Date()

    for (let i = 0; i < count; i++) {
      const timestamp = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000)) // 1 day apart

      if (role === 'admin') {
        notifications.push({
          id: `notification-${i}`,
          type: ['info', 'warning', 'success', 'error'][i % 4] as any,
          title: [
            'System Maintenance Scheduled',
            'High Server Load Detected',
            'Backup Completed Successfully',
            'Database Connection Error'
          ][i % 4],
          description: [
            'Scheduled maintenance on Sunday 2 AM - 4 AM',
            'Server load is above 85% threshold',
            'Daily backup completed without errors',
            'Temporary database connection issues resolved'
          ][i % 4],
          timestamp,
          read: i > 2,
          actionUrl: i === 0 ? '/admin/maintenance' : undefined
        })
      } else if (role === 'student') {
        notifications.push({
          id: `notification-${i}`,
          type: ['info', 'warning', 'success'][i % 3] as any,
          title: [
            'Course Registration Opens',
            'Assignment Due Tomorrow',
            'Grade Posted'
          ][i % 3],
          description: [
            'Spring 2025 course registration begins Monday',
            'Programming Project 2 is due tomorrow at 11:59 PM',
            'Your grade for Calculus II Midterm has been posted'
          ][i % 3],
          timestamp,
          read: i > 1,
          actionUrl: i === 1 ? '/student/assignments' : undefined
        })
      } else if (role === 'faculty') {
        notifications.push({
          id: `notification-${i}`,
          type: ['info', 'warning', 'success'][i % 3] as any,
          title: [
            'Grade Submission Deadline',
            'Student Absence Alert',
            'Course Evaluation Results'
          ][i % 3],
          description: [
            'Final grades due by Friday 5:00 PM',
            'Multiple absences detected for John Doe in CS 101',
            'Course evaluation results are now available'
          ][i % 3],
          timestamp,
          read: i > 1,
          actionUrl: i === 0 ? '/faculty/grading' : undefined
        })
      }
    }

    return notifications
  }

  return {
    adminMetrics: readonly(adminMetrics),
    studentMetrics: readonly(studentMetrics),
    facultyMetrics: readonly(facultyMetrics),
    generateActivities,
    generateNotifications
  }
}
