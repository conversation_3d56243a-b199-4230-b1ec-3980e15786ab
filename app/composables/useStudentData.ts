export interface Student {
  id: number
  firstName: string
  lastName: string
  studentId: string
  email: string
  phone?: string
  program: string
  year: number
  photo: string
  gpa: number
  status: 'active' | 'inactive' | 'graduated' | 'suspended'
  enrollmentDate: Date
  expectedGraduation: Date
  address?: {
    street: string
    city: string
    state: string
    zip: string
  }
  emergencyContact?: {
    name: string
    relationship: string
    phone: string
    email: string
  }
  academicAdvisor?: string
  credits: {
    earned: number
    attempted: number
    required: number
  }
  financialStatus: 'current' | 'hold' | 'delinquent'
}

export interface Grade {
  id: number
  studentId: number
  courseId: string
  courseName: string
  semester: string
  year: number
  grade: string
  credits: number
  gradePoints: number
  instructor: string
}

export interface TranscriptRecord {
  semester: string
  year: number
  courses: Grade[]
  semesterGPA: number
  cumulativeGPA: number
  creditsEarned: number
  creditsAttempted: number
}

export function useStudentData() {
  const students = ref<Student[]>([
    {
      id: 1,
      firstName: 'Jane',
      lastName: 'Doe',
      studentId: 'S10001',
      email: '<EMAIL>',
      phone: '(*************',
      program: 'Computer Science',
      year: 3,
      photo: 'https://github.com/janedoe.png',
      gpa: 3.8,
      status: 'active',
      enrollmentDate: new Date('2022-08-15'),
      expectedGraduation: new Date('2026-05-15'),
      address: {
        street: '123 College Ave',
        city: 'Springfield',
        state: 'IL',
        zip: '62701'
      },
      emergencyContact: {
        name: 'John Doe',
        relationship: 'Father',
        phone: '(*************',
        email: '<EMAIL>'
      },
      academicAdvisor: 'Dr. Sarah <PERSON>',
      credits: {
        earned: 87,
        attempted: 90,
        required: 120
      },
      financialStatus: 'current'
    },
    {
      id: 2,
      firstName: 'Michael',
      lastName: 'Smith',
      studentId: 'S10002',
      email: '<EMAIL>',
      phone: '(*************',
      program: 'Mathematics',
      year: 2,
      photo: 'https://github.com/michaelsmith.png',
      gpa: 3.6,
      status: 'active',
      enrollmentDate: new Date('2023-08-15'),
      expectedGraduation: new Date('2027-05-15'),
      academicAdvisor: 'Prof. Michael Chen',
      credits: {
        earned: 45,
        attempted: 48,
        required: 120
      },
      financialStatus: 'current'
    },
    {
      id: 3,
      firstName: 'Emily',
      lastName: 'Johnson',
      studentId: 'S10003',
      email: '<EMAIL>',
      phone: '(*************',
      program: 'English Literature',
      year: 4,
      photo: 'https://github.com/emilyjohnson.png',
      gpa: 3.9,
      status: 'active',
      enrollmentDate: new Date('2021-08-15'),
      expectedGraduation: new Date('2025-05-15'),
      academicAdvisor: 'Dr. Emily Rodriguez',
      credits: {
        earned: 105,
        attempted: 108,
        required: 120
      },
      financialStatus: 'current'
    },
    {
      id: 4,
      firstName: 'David',
      lastName: 'Brown',
      studentId: 'S10004',
      email: '<EMAIL>',
      program: 'Physics',
      year: 1,
      photo: 'https://github.com/davidbrown.png',
      gpa: 3.4,
      status: 'active',
      enrollmentDate: new Date('2024-08-15'),
      expectedGraduation: new Date('2028-05-15'),
      academicAdvisor: 'Dr. Robert Wilson',
      credits: {
        earned: 15,
        attempted: 18,
        required: 120
      },
      financialStatus: 'current'
    },
    {
      id: 5,
      firstName: 'Sarah',
      lastName: 'Wilson',
      studentId: 'S10005',
      email: '<EMAIL>',
      program: 'Biology',
      year: 3,
      photo: 'https://github.com/sarahwilson.png',
      gpa: 3.7,
      status: 'active',
      enrollmentDate: new Date('2022-08-15'),
      expectedGraduation: new Date('2026-05-15'),
      academicAdvisor: 'Dr. Lisa Anderson',
      credits: {
        earned: 78,
        attempted: 81,
        required: 120
      },
      financialStatus: 'hold'
    }
  ])

  // Add more mock students to reach 25+ records
  for (let i = 6; i <= 25; i++) {
    const programs = ['Computer Science', 'Mathematics', 'English Literature', 'Physics', 'Biology', 'Chemistry', 'History', 'Psychology']
    const statuses: Student['status'][] = ['active', 'inactive', 'graduated', 'suspended']
    const financialStatuses: Student['financialStatus'][] = ['current', 'hold', 'delinquent']
    
    students.value.push({
      id: i,
      firstName: `Student${i}`,
      lastName: `Last${i}`,
      studentId: `S${10000 + i}`,
      email: `student${i}@college.edu`,
      phone: `(555) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
      program: programs[Math.floor(Math.random() * programs.length)],
      year: Math.floor(Math.random() * 4) + 1,
      photo: `https://github.com/student${i}.png`,
      gpa: Math.round((Math.random() * 2 + 2) * 100) / 100, // GPA between 2.0 and 4.0
      status: statuses[Math.floor(Math.random() * statuses.length)],
      enrollmentDate: new Date(2020 + Math.floor(Math.random() * 5), 7, 15),
      expectedGraduation: new Date(2024 + Math.floor(Math.random() * 5), 4, 15),
      academicAdvisor: `Dr. Advisor ${i}`,
      credits: {
        earned: Math.floor(Math.random() * 100) + 20,
        attempted: Math.floor(Math.random() * 110) + 25,
        required: 120
      },
      financialStatus: financialStatuses[Math.floor(Math.random() * financialStatuses.length)]
    })
  }

  const getStudents = (page = 1, limit = 10, search = '', filters: any = {}) => {
    let filteredStudents = [...students.value]

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase()
      filteredStudents = filteredStudents.filter(student =>
        student.firstName.toLowerCase().includes(searchLower) ||
        student.lastName.toLowerCase().includes(searchLower) ||
        student.studentId.toLowerCase().includes(searchLower) ||
        student.email.toLowerCase().includes(searchLower) ||
        student.program.toLowerCase().includes(searchLower)
      )
    }

    // Apply filters
    if (filters.program) {
      filteredStudents = filteredStudents.filter(student => student.program === filters.program)
    }
    if (filters.year) {
      filteredStudents = filteredStudents.filter(student => student.year === parseInt(filters.year))
    }
    if (filters.status) {
      filteredStudents = filteredStudents.filter(student => student.status === filters.status)
    }

    // Calculate pagination
    const total = filteredStudents.length
    const totalPages = Math.ceil(total / limit)
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedStudents = filteredStudents.slice(startIndex, endIndex)

    return {
      students: paginatedStudents,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }

  const getStudentById = (id: number): Student | undefined => {
    return students.value.find(student => student.id === id)
  }

  const updateStudent = (id: number, updates: Partial<Student>): boolean => {
    const index = students.value.findIndex(student => student.id === id)
    if (index !== -1) {
      students.value[index] = { ...students.value[index], ...updates }
      return true
    }
    return false
  }

  const deleteStudent = (id: number): boolean => {
    const index = students.value.findIndex(student => student.id === id)
    if (index !== -1) {
      students.value.splice(index, 1)
      return true
    }
    return false
  }

  const getPrograms = (): string[] => {
    return [...new Set(students.value.map(student => student.program))].sort()
  }

  const getYears = (): number[] => {
    return [...new Set(students.value.map(student => student.year))].sort()
  }

  const getStatuses = (): Student['status'][] => {
    return ['active', 'inactive', 'graduated', 'suspended']
  }

  return {
    students: readonly(students),
    getStudents,
    getStudentById,
    updateStudent,
    deleteStudent,
    getPrograms,
    getYears,
    getStatuses
  }
}
