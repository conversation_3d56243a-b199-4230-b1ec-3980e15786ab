<template>
  <UDropdown :items="dropdownItems" :popper="{ placement: 'bottom-end' }">
    <UButton
      variant="ghost"
      color="gray"
      class="flex items-center space-x-2"
    >
      <UAvatar
        :src="currentUser?.avatar"
        :alt="currentUser?.name"
        size="sm"
      />
      <div class="hidden sm:block text-left">
        <div class="text-sm font-medium text-gray-900 dark:text-white">
          {{ currentUser?.name }}
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400 capitalize">
          {{ currentUser?.role }}
        </div>
      </div>
      <UIcon name="i-heroicons-chevron-down" class="w-3 h-3" />
    </UButton>
  </UDropdown>
</template>

<script setup>
const { currentUser, userRole, logout, switchRole } = useNavigation()

const availableRoles = [
  { label: 'Admin', value: 'admin' },
  { label: 'Student', value: 'student' },
  { label: 'Faculty', value: 'faculty' }
]

const handleProfileSettings = () => {
  navigateTo('/settings/profile')
}

const handlePreferences = () => {
  navigateTo('/settings/preferences')
}

const handleHelp = () => {
  navigateTo('/help')
}

const handleSwitchRole = (role) => {
  switchRole(role)
}

// Dropdown menu items structure for UDropdown
const dropdownItems = [
  [{
    label: currentUser?.value?.name || 'User',
    slot: 'account',
    disabled: true
  }],
  [{
    label: 'Profile Settings',
    icon: 'i-heroicons-user-circle',
    click: handleProfileSettings
  }, {
    label: 'Preferences',
    icon: 'i-heroicons-cog-6-tooth',
    click: handlePreferences
  }],
  [{
    label: 'Switch Role (Demo)',
    slot: 'role-header',
    disabled: true
  }, ...availableRoles.map(role => ({
    label: role.label,
    click: () => handleSwitchRole(role.value)
  }))],
  [{
    label: 'Help & Support',
    icon: 'i-heroicons-question-mark-circle',
    click: handleHelp
  }],
  [{
    label: 'Sign Out',
    icon: 'i-heroicons-arrow-right-on-rectangle',
    click: logout
  }]
]
</script>
