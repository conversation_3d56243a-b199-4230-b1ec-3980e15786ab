<template>
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-2">
      <li v-for="(crumb, index) in breadcrumbs" :key="crumb.path" class="flex items-center">
        <!-- Separator -->
        <UIcon
          v-if="index > 0"
          name="i-heroicons-chevron-right"
          class="w-4 h-4 text-gray-400 dark:text-gray-500 mx-2"
        />
        
        <!-- Breadcrumb item -->
        <div class="flex items-center">
          <!-- Home icon for first item -->
          <UIcon
            v-if="index === 0"
            name="i-heroicons-home"
            class="w-4 h-4 mr-1 text-gray-400 dark:text-gray-500"
          />
          
          <!-- Link for non-current items -->
          <NuxtLink
            v-if="index < breadcrumbs.length - 1"
            :to="crumb.path"
            class="text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          >
            {{ crumb.label }}
          </NuxtLink>
          
          <!-- Current page (no link) -->
          <span
            v-else
            class="text-sm font-medium text-gray-900 dark:text-white"
            aria-current="page"
          >
            {{ crumb.label }}
          </span>
        </div>
      </li>
    </ol>
  </nav>
</template>

<script setup>
const { breadcrumbs } = useNavigation()
</script>
