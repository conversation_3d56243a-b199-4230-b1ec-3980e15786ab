<template>
  <UCard class="hover:shadow-lg transition-shadow duration-200">
    <div class="space-y-4">
      <!-- Faculty Header -->
      <div class="flex items-start space-x-4">
        <UAvatar
          :src="props.faculty.avatar"
          :alt="`${props.faculty.firstName} ${props.faculty.lastName}`"
          size="lg"
          :ui="{ rounded: 'rounded-lg' }"
        />
        <div class="flex-1 min-w-0">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
            {{ props.faculty.firstName }} {{ props.faculty.lastName }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-300 truncate">
            {{ props.faculty.title }}
          </p>
          <div class="flex items-center space-x-2 mt-1">
            <UBadge
              :color="getRankColor(props.faculty.rank)"
              variant="soft"
              size="xs"
            >
              {{ props.faculty.rank }}
            </UBadge>
            <UBadge
              :color="getStatusColor(props.faculty.status)"
              variant="soft"
              size="xs"
            >
              {{ props.faculty.status }}
            </UBadge>
          </div>
        </div>
      </div>

      <!-- Department and Contact -->
      <div class="space-y-2">
        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
          <UIcon name="i-heroicons-building-office" class="w-4 h-4 mr-2 flex-shrink-0" />
          <span class="truncate">{{ props.faculty.department }}</span>
        </div>
        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
          <UIcon name="i-heroicons-envelope" class="w-4 h-4 mr-2 flex-shrink-0" />
          <span class="truncate">{{ props.faculty.email }}</span>
        </div>
        <div v-if="props.faculty.office" class="flex items-center text-sm text-gray-600 dark:text-gray-300">
          <UIcon name="i-heroicons-map-pin" class="w-4 h-4 mr-2 flex-shrink-0" />
          <span class="truncate">{{ props.faculty.office }}</span>
        </div>
      </div>

      <!-- Specializations -->
      <div v-if="props.faculty.specializations.length > 0">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Specializations</h4>
        <div class="flex flex-wrap gap-1">
          <UBadge
            v-for="spec in props.faculty.specializations.slice(0, 3)"
            :key="spec"
            color="blue"
            variant="soft"
            size="xs"
          >
            {{ spec }}
          </UBadge>
          <UBadge
            v-if="props.faculty.specializations.length > 3"
            color="gray"
            variant="soft"
            size="xs"
          >
            +{{ props.faculty.specializations.length - 3 }} more
          </UBadge>
        </div>
      </div>

      <!-- Course Load -->
      <div class="space-y-2">
        <div class="flex items-center justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-300">Course Load</span>
          <span class="font-medium text-gray-900 dark:text-white">
            {{ props.faculty.courses.length }}/{{ props.faculty.maxCourses }} courses
          </span>
        </div>
        <UProgress
          :value="(props.faculty.courses.length / props.faculty.maxCourses) * 100"
          :color="getWorkloadColor(props.faculty.courses.length, props.faculty.maxCourses)"
          size="sm"
        />
        <div class="flex items-center justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-300">Workload</span>
          <span class="font-medium text-gray-900 dark:text-white">{{ props.faculty.workload }}%</span>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-3 gap-4 pt-2 border-t border-gray-200 dark:border-gray-700">
        <div class="text-center">
          <div class="text-lg font-bold text-gray-900 dark:text-white">
            {{ props.faculty.courses.length }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">Courses</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-bold text-gray-900 dark:text-white">
            {{ props.faculty.publications.length }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">Publications</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-bold text-gray-900 dark:text-white">
            {{ getYearsOfService(props.faculty.hireDate) }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">Years</div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2">
          <UButton
            size="xs"
            variant="ghost"
            icon="i-heroicons-eye"
            @click="emit('view', props.faculty.id)"
          >
            View
          </UButton>
          <UButton
            size="xs"
            variant="ghost"
            icon="i-heroicons-pencil"
            @click="emit('edit', props.faculty.id)"
          >
            Edit
          </UButton>
        </div>
        
        <div class="flex items-center space-x-1">
          <!-- Tenure Status -->
          <UTooltip :text="props.faculty.tenure ? 'Tenured' : 'Non-tenured'">
            <UIcon
              :name="props.faculty.tenure ? 'i-heroicons-shield-check' : 'i-heroicons-shield-exclamation'"
              :class="props.faculty.tenure ? 'text-green-500' : 'text-yellow-500'"
              class="w-4 h-4"
            />
          </UTooltip>

          <!-- Office Hours Indicator -->
          <UTooltip :text="props.faculty.officeHours || 'No office hours set'">
            <UIcon
              name="i-heroicons-clock"
              :class="props.faculty.officeHours ? 'text-blue-500' : 'text-gray-400'"
              class="w-4 h-4"
            />
          </UTooltip>

          <!-- Contact Links -->
          <UTooltip v-if="props.faculty.website" text="Visit website">
            <UButton
              :to="props.faculty.website"
              target="_blank"
              size="xs"
              variant="ghost"
              icon="i-heroicons-globe-alt"
              class="p-1"
            />
          </UTooltip>

          <!-- Delete Button -->
          <UButton
            size="xs"
            variant="ghost"
            icon="i-heroicons-trash"
            class="p-1 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
            @click="emit('delete', props.faculty.id)"
          />
        </div>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
// Props
const props = defineProps<{
  faculty: {
    id: string
    firstName: string
    lastName: string
    email: string
    phone: string
    avatar?: string
    title: string
    rank: string
    department: string
    office: string
    officeHours: string
    specializations: string[]
    researchInterests: string[]
    courses: Array<{
      id: string
      courseCode: string
      courseName: string
      semester: string
      year: number
      credits: number
      enrollmentCount: number
      maxEnrollment: number
      status: string
    }>
    publications: Array<{
      id: string
      title: string
      type: string
      venue: string
      year: number
      authors: string[]
    }>
    bio: string
    website?: string
    hireDate: string
    tenure: boolean
    status: string
    workload: number
    maxCourses: number
  }
}>()

// Emits
const emit = defineEmits<{
  view: [id: string]
  edit: [id: string]
  delete: [id: string]
}>()

// Helper functions
const getRankColor = (rank: string) => {
  const colors = {
    'Professor': 'purple',
    'Associate Professor': 'blue',
    'Assistant Professor': 'green',
    'Lecturer': 'orange',
    'Adjunct Professor': 'gray'
  }
  return colors[rank] || 'gray'
}

const getStatusColor = (status: string) => {
  const colors = {
    'Active': 'green',
    'Sabbatical': 'blue',
    'Retired': 'gray',
    'On Leave': 'yellow'
  }
  return colors[status] || 'gray'
}

const getWorkloadColor = (current: number, max: number) => {
  const percentage = (current / max) * 100
  if (percentage >= 90) return 'red'
  if (percentage >= 75) return 'orange'
  if (percentage >= 50) return 'yellow'
  return 'green'
}

const getYearsOfService = (hireDate: string) => {
  const hire = new Date(hireDate)
  const now = new Date()
  const years = now.getFullYear() - hire.getFullYear()
  return years
}
</script>
