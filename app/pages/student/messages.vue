<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Messages</h1>
          <p class="text-gray-600 dark:text-gray-300">Communicate with faculty and staff</p>
        </div>
        <UButton color="primary" icon="i-heroicons-plus">
          New Message
        </UButton>
      </div>

      <!-- Message Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-inbox" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Unread</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">3</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-chat-bubble-left-right" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Messages</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">24</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-paper-airplane" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Sent</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">12</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Messages List -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Messages</h2>
            <div class="flex items-center space-x-2">
              <UButton variant="outline" size="sm" icon="i-heroicons-funnel">
                Filter
              </UButton>
              <UButton variant="outline" size="sm" icon="i-heroicons-arrow-path">
                Refresh
              </UButton>
            </div>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
            <UAvatar
              src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100"
              alt="Dr. Smith"
              size="sm"
            />
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Dr. Smith</h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">2 hours ago</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Assignment feedback for your recent submission...</p>
              <UBadge color="blue" variant="soft" size="xs" class="mt-2">Unread</UBadge>
            </div>
          </div>

          <div class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
            <UAvatar
              src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100"
              alt="Academic Advisor"
              size="sm"
            />
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Academic Advisor</h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">1 day ago</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Course registration reminder for next semester...</p>
            </div>
          </div>

          <div class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
            <UAvatar
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100"
              alt="Financial Aid Office"
              size="sm"
            />
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Financial Aid Office</h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">3 days ago</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Your scholarship application has been approved...</p>
              <UBadge color="green" variant="soft" size="xs" class="mt-2">Important</UBadge>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'student'
})
</script>
