<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">My Courses</h1>
        <p class="text-gray-600 dark:text-gray-300">Manage your enrolled courses and assignments</p>
      </div>
      <div class="flex items-center space-x-3">
        <USelect
          v-model="selectedSemester"
          :options="semesterOptions"
          @change="filterCourses"
        />
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshCourses"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton color="primary" to="/academic/registration" icon="i-heroicons-plus-circle">
          Add Courses
        </UButton>
      </div>
    </div>

    <!-- Course Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Enrolled Courses</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ enrolledCourses.length }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-clock" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Credits</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalCredits }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-document-text" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Assignments Due</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ upcomingAssignments }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Average Grade</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ averageGrade }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Course Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard
        v-for="course in filteredCourses"
        :key="course.id"
        class="hover:shadow-lg transition-shadow"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ course.code }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ course.title }}</p>
            </div>
            <UBadge :color="getStatusColor(course.status)" variant="subtle">
              {{ course.status }}
            </UBadge>
          </div>
        </template>

        <div class="space-y-4">
          <!-- Course Info -->
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium text-gray-600 dark:text-gray-400">Instructor:</span>
              <p class="text-gray-900 dark:text-white">{{ course.instructor }}</p>
            </div>
            <div>
              <span class="font-medium text-gray-600 dark:text-gray-400">Credits:</span>
              <p class="text-gray-900 dark:text-white">{{ course.credits }}</p>
            </div>
            <div>
              <span class="font-medium text-gray-600 dark:text-gray-400">Schedule:</span>
              <p class="text-gray-900 dark:text-white">{{ course.schedule }}</p>
            </div>
            <div>
              <span class="font-medium text-gray-600 dark:text-gray-400">Location:</span>
              <p class="text-gray-900 dark:text-white">{{ course.location }}</p>
            </div>
          </div>

          <!-- Current Grade -->
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Current Grade:</span>
            <div class="flex items-center space-x-2">
              <UBadge :color="getGradeColor(course.currentGrade)" variant="subtle">
                {{ course.currentGrade }}
              </UBadge>
              <span class="text-sm text-gray-500 dark:text-gray-400">({{ course.percentage }}%)</span>
            </div>
          </div>

          <!-- Recent Assignments -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Recent Assignments</h4>
            <div class="space-y-2">
              <div
                v-for="assignment in course.recentAssignments"
                :key="assignment.id"
                class="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-700 rounded"
              >
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ assignment.title }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Due: {{ formatDate(assignment.dueDate) }}
                  </p>
                </div>
                <div class="text-right">
                  <UBadge
                    :color="assignment.submitted ? 'green' : getDueDateColor(assignment.dueDate)"
                    variant="subtle"
                    size="sm"
                  >
                    {{ assignment.submitted ? 'Submitted' : 'Pending' }}
                  </UBadge>
                  <p v-if="assignment.grade" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Grade: {{ assignment.grade }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Course Actions -->
          <div class="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center space-x-2">
              <UButton
                size="sm"
                variant="outline"
                icon="i-heroicons-book-open"
                @click="viewCourse(course)"
              >
                View Course
              </UButton>
              <UButton
                size="sm"
                variant="outline"
                icon="i-heroicons-document-text"
                @click="viewAssignments(course)"
              >
                Assignments
              </UButton>
            </div>
            <UButton
              size="sm"
              variant="ghost"
              icon="i-heroicons-ellipsis-horizontal"
              @click="showCourseMenu(course)"
            />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Upcoming Deadlines -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Upcoming Deadlines</h2>
          <UButton variant="ghost" size="sm" icon="i-heroicons-calendar-days">
            View Calendar
          </UButton>
        </div>
      </template>
      <div class="space-y-3">
        <div
          v-for="deadline in upcomingDeadlines"
          :key="deadline.id"
          class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
        >
          <div class="flex-1">
            <h3 class="font-medium text-gray-900 dark:text-white">{{ deadline.title }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">{{ deadline.course }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">{{ deadline.type }}</p>
          </div>
          <div class="text-right">
            <div class="text-sm font-medium" :class="getDueDateColor(deadline.dueDate)">
              {{ formatDueDate(deadline.dueDate) }}
            </div>
            <UBadge :color="getPriorityColor(deadline.priority)" variant="subtle" size="sm">
              {{ deadline.priority }}
            </UBadge>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Course Details Modal -->
    <UModal v-model="showCourseDetails" :ui="{ width: 'sm:max-w-4xl' }">
      <UCard v-if="selectedCourse">
        <template #header>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold">{{ selectedCourse.code }} - {{ selectedCourse.title }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ selectedCourse.instructor }}</p>
            </div>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showCourseDetails = false"
            />
          </div>
        </template>

        <div class="space-y-6">
          <!-- Course Description -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-2">Course Description</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">{{ selectedCourse.description }}</p>
          </div>

          <!-- Syllabus -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-2">Course Materials</h4>
            <div class="space-y-2">
              <UButton
                v-for="material in selectedCourse.materials"
                :key="material.id"
                variant="outline"
                size="sm"
                :icon="material.icon"
                @click="downloadMaterial(material)"
              >
                {{ material.name }}
              </UButton>
            </div>
          </div>

          <!-- Grade Breakdown -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-2">Grade Breakdown</h4>
            <div class="space-y-2">
              <div
                v-for="category in selectedCourse.gradeBreakdown"
                :key="category.name"
                class="flex items-center justify-between"
              >
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ category.name }}</span>
                <span class="text-sm font-medium">{{ category.weight }}%</span>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton variant="outline" @click="showCourseDetails = false">
              Close
            </UButton>
            <UButton color="primary" @click="goToCourse(selectedCourse)">
              Go to Course Page
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'student'
})

// Reactive data
const isRefreshing = ref(false)
const showCourseDetails = ref(false)
const selectedCourse = ref(null)
const selectedSemester = ref('fall-2024')

// Mock data
const enrolledCourses = ref([
  {
    id: 1,
    code: 'CS 301',
    title: 'Data Structures and Algorithms',
    instructor: 'Dr. Sarah Johnson',
    credits: 3,
    schedule: 'MWF 10:00-11:00 AM',
    location: 'Room 201',
    status: 'Active',
    currentGrade: 'A-',
    percentage: 92,
    description: 'Advanced study of data structures and algorithms including trees, graphs, and dynamic programming.',
    materials: [
      { id: 1, name: 'Course Syllabus', icon: 'i-heroicons-document-text' },
      { id: 2, name: 'Lecture Notes', icon: 'i-heroicons-book-open' }
    ],
    gradeBreakdown: [
      { name: 'Homework', weight: 30 },
      { name: 'Exams', weight: 40 },
      { name: 'Projects', weight: 30 }
    ],
    recentAssignments: [
      {
        id: 1,
        title: 'Binary Tree Implementation',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        submitted: false,
        grade: null
      },
      {
        id: 2,
        title: 'Sorting Algorithms Quiz',
        dueDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        submitted: true,
        grade: 'A'
      }
    ]
  },
  {
    id: 2,
    code: 'MATH 201',
    title: 'Calculus II',
    instructor: 'Prof. Michael Chen',
    credits: 4,
    schedule: 'TTh 2:00-3:30 PM',
    location: 'Room 105',
    status: 'Active',
    currentGrade: 'B+',
    percentage: 87,
    description: 'Continuation of Calculus I covering integration techniques and infinite series.',
    materials: [
      { id: 3, name: 'Textbook PDF', icon: 'i-heroicons-document-text' },
      { id: 4, name: 'Problem Sets', icon: 'i-heroicons-calculator' }
    ],
    gradeBreakdown: [
      { name: 'Homework', weight: 25 },
      { name: 'Quizzes', weight: 25 },
      { name: 'Exams', weight: 50 }
    ],
    recentAssignments: [
      {
        id: 3,
        title: 'Integration Problems Set 3',
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        submitted: false,
        grade: null
      }
    ]
  },
  {
    id: 3,
    code: 'ENG 102',
    title: 'Composition and Literature',
    instructor: 'Dr. Emily Rodriguez',
    credits: 3,
    schedule: 'MWF 1:00-2:00 PM',
    location: 'Room 150',
    status: 'Active',
    currentGrade: 'A',
    percentage: 95,
    description: 'Advanced composition course focusing on critical reading and analytical writing.',
    materials: [
      { id: 5, name: 'Reading List', icon: 'i-heroicons-book-open' },
      { id: 6, name: 'Writing Guidelines', icon: 'i-heroicons-pencil' }
    ],
    gradeBreakdown: [
      { name: 'Essays', weight: 60 },
      { name: 'Participation', weight: 20 },
      { name: 'Final Project', weight: 20 }
    ],
    recentAssignments: [
      {
        id: 4,
        title: 'Literary Analysis Essay',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        submitted: false,
        grade: null
      }
    ]
  }
])

const semesterOptions = [
  { label: 'Fall 2024', value: 'fall-2024' },
  { label: 'Spring 2024', value: 'spring-2024' },
  { label: 'All Semesters', value: 'all' }
]

const filteredCourses = ref([...enrolledCourses.value])

// Computed properties
const totalCredits = computed(() => {
  return filteredCourses.value.reduce((sum, course) => sum + course.credits, 0)
})

const upcomingAssignments = computed(() => {
  return filteredCourses.value.reduce((count, course) => {
    return count + course.recentAssignments.filter(a => !a.submitted).length
  }, 0)
})

const averageGrade = computed(() => {
  const total = filteredCourses.value.reduce((sum, course) => sum + course.percentage, 0)
  return Math.round(total / filteredCourses.value.length) + '%'
})

const upcomingDeadlines = computed(() => {
  const deadlines = []
  filteredCourses.value.forEach(course => {
    course.recentAssignments.forEach(assignment => {
      if (!assignment.submitted) {
        deadlines.push({
          id: assignment.id,
          title: assignment.title,
          course: course.code,
          type: 'Assignment',
          dueDate: assignment.dueDate,
          priority: getDueDatePriority(assignment.dueDate)
        })
      }
    })
  })
  return deadlines.sort((a, b) => a.dueDate - b.dueDate).slice(0, 5)
})

// Methods
const filterCourses = () => {
  // In a real app, this would filter by semester
  filteredCourses.value = [...enrolledCourses.value]
}

const refreshCourses = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    useToast().add({
      title: 'Courses Refreshed',
      description: 'Your course data has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const viewCourse = (course) => {
  selectedCourse.value = course
  showCourseDetails.value = true
}

const viewAssignments = (course) => {
  useToast().add({
    title: 'View Assignments',
    description: `Opening assignments for ${course.code}`,
    icon: 'i-heroicons-document-text'
  })
}

const showCourseMenu = (course) => {
  useToast().add({
    title: 'Course Menu',
    description: `Options for ${course.code}`,
    icon: 'i-heroicons-ellipsis-horizontal'
  })
}

const downloadMaterial = (material) => {
  useToast().add({
    title: 'Download Started',
    description: `Downloading ${material.name}`,
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const goToCourse = (course) => {
  navigateTo(`/academic/courses/${course.id}`)
}

const getStatusColor = (status) => {
  const colors = {
    'Active': 'green',
    'Completed': 'blue',
    'Dropped': 'red'
  }
  return colors[status] || 'gray'
}

const getGradeColor = (grade) => {
  if (grade.startsWith('A')) return 'green'
  if (grade.startsWith('B')) return 'blue'
  if (grade.startsWith('C')) return 'yellow'
  return 'red'
}

const getDueDateColor = (date) => {
  const now = new Date()
  const diffTime = date.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays <= 1) return 'text-red-600 dark:text-red-400'
  if (diffDays <= 3) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-gray-600 dark:text-gray-400'
}

const getDueDatePriority = (date) => {
  const now = new Date()
  const diffTime = date.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays <= 1) return 'High'
  if (diffDays <= 3) return 'Medium'
  return 'Low'
}

const getPriorityColor = (priority) => {
  const colors = {
    'High': 'red',
    'Medium': 'yellow',
    'Low': 'green'
  }
  return colors[priority] || 'gray'
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const formatDueDate = (date) => {
  const now = new Date()
  const diffTime = date.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Tomorrow'
  if (diffDays < 7) return `in ${diffDays} days`
  return date.toLocaleDateString()
}

// Initialize
onMounted(() => {
  filterCourses()
})
</script>
