<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Official Transcript</h1>
        <p class="text-gray-600 dark:text-gray-300">Complete academic record and degree progress</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-printer"
          @click="printTranscript"
        >
          Print
        </UButton>
        <UButton color="primary" icon="i-heroicons-arrow-down-tray" @click="downloadTranscript">
          Download PDF
        </UButton>
      </div>
    </div>

    <!-- Student Information -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">Student Information</h2>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-3">
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Name:</span>
            <p class="text-sm">{{ studentInfo.name }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Student ID:</span>
            <p class="text-sm">{{ studentInfo.id }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Date of Birth:</span>
            <p class="text-sm">{{ studentInfo.dateOfBirth }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Admission Date:</span>
            <p class="text-sm">{{ studentInfo.admissionDate }}</p>
          </div>
        </div>
        <div class="space-y-3">
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Major:</span>
            <p class="text-sm">{{ studentInfo.major }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Minor:</span>
            <p class="text-sm">{{ studentInfo.minor || 'None' }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Expected Graduation:</span>
            <p class="text-sm">{{ studentInfo.expectedGraduation }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Academic Status:</span>
            <UBadge color="green" variant="subtle">{{ studentInfo.status }}</UBadge>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Academic Summary -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">Academic Summary</h2>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ academicSummary.cumulativeGPA }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Cumulative GPA</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ academicSummary.creditsEarned }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Credits Earned</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ academicSummary.creditsAttempted }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Credits Attempted</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ academicSummary.classRank }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Class Rank</div>
        </div>
      </div>
    </UCard>

    <!-- Degree Progress -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">Degree Requirements Progress</h2>
      </template>
      <div class="space-y-4">
        <div
          v-for="requirement in degreeRequirements"
          :key="requirement.category"
          class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex-1">
            <h3 class="font-medium text-gray-900 dark:text-white">{{ requirement.category }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              {{ requirement.completed }} / {{ requirement.required }} credits completed
            </p>
          </div>
          <div class="w-32">
            <UProgress 
              :value="(requirement.completed / requirement.required) * 100" 
              :color="requirement.completed >= requirement.required ? 'green' : 'blue'"
            />
          </div>
          <div class="ml-4">
            <UIcon 
              :name="requirement.completed >= requirement.required ? 'i-heroicons-check-circle' : 'i-heroicons-clock'"
              :class="requirement.completed >= requirement.required ? 'text-green-500' : 'text-yellow-500'"
              class="w-6 h-6"
            />
          </div>
        </div>
      </div>
    </UCard>

    <!-- Academic History by Semester -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">Academic History</h2>
      </template>
      <div class="space-y-6">
        <div
          v-for="semester in academicHistory"
          :key="semester.term"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <!-- Semester Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-base font-semibold text-gray-900 dark:text-white">{{ semester.term }}</h3>
            <div class="flex items-center space-x-4 text-sm">
              <span class="text-gray-600 dark:text-gray-400">
                Credits: {{ semester.credits }}
              </span>
              <span class="text-gray-600 dark:text-gray-400">
                GPA: {{ semester.gpa }}
              </span>
              <UBadge :color="getEnrollmentStatusColor(semester.status)" variant="subtle">
                {{ semester.status }}
              </UBadge>
            </div>
          </div>

          <!-- Course List -->
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead>
                <tr class="border-b border-gray-200 dark:border-gray-700">
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Course</th>
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Title</th>
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Credits</th>
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Grade</th>
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Points</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="course in semester.courses"
                  :key="course.id"
                  class="border-b border-gray-100 dark:border-gray-800"
                >
                  <td class="py-2 text-sm font-medium text-gray-900 dark:text-white">{{ course.code }}</td>
                  <td class="py-2 text-sm text-gray-600 dark:text-gray-300">{{ course.title }}</td>
                  <td class="py-2 text-sm text-gray-600 dark:text-gray-300">{{ course.credits }}</td>
                  <td class="py-2">
                    <UBadge :color="getGradeColor(course.grade)" variant="subtle" size="sm">
                      {{ course.grade }}
                    </UBadge>
                  </td>
                  <td class="py-2 text-sm text-gray-600 dark:text-gray-300">{{ course.gradePoints }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Honors and Awards -->
    <UCard v-if="honorsAndAwards.length > 0">
      <template #header>
        <h2 class="text-lg font-semibold">Honors and Awards</h2>
      </template>
      <div class="space-y-3">
        <div
          v-for="honor in honorsAndAwards"
          :key="honor.id"
          class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <UIcon name="i-heroicons-trophy" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">{{ honor.title }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">{{ honor.description }}</p>
            </div>
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">{{ honor.date }}</div>
        </div>
      </div>
    </UCard>

    <!-- Transfer Credits -->
    <UCard v-if="transferCredits.length > 0">
      <template #header>
        <h2 class="text-lg font-semibold">Transfer Credits</h2>
      </template>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Institution</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Course</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Credits</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Equivalent</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="credit in transferCredits" :key="credit.id">
              <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ credit.institution }}</td>
              <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ credit.course }}</td>
              <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ credit.credits }}</td>
              <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ credit.equivalent }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </UCard>

    <!-- Transcript Footer -->
    <UCard>
      <div class="text-center text-sm text-gray-600 dark:text-gray-400 space-y-2">
        <p>This is an official transcript issued by Springfield College</p>
        <p>Generated on: {{ new Date().toLocaleDateString() }}</p>
        <p>Transcript ID: {{ transcriptId }}</p>
      </div>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'student'
})

// Mock data
const transcriptId = ref('TR-2024-001234')

const studentInfo = ref({
  name: 'John Doe',
  id: 'STU123456',
  dateOfBirth: 'January 15, 2002',
  admissionDate: 'August 2022',
  major: 'Computer Science',
  minor: 'Mathematics',
  expectedGraduation: 'May 2026',
  status: 'Good Standing'
})

const academicSummary = ref({
  cumulativeGPA: '3.68',
  creditsEarned: 87,
  creditsAttempted: 90,
  classRank: '15/250'
})

const degreeRequirements = ref([
  { category: 'General Education', completed: 42, required: 45 },
  { category: 'Major Requirements', completed: 30, required: 48 },
  { category: 'Minor Requirements', completed: 12, required: 18 },
  { category: 'Electives', completed: 3, required: 9 }
])

const academicHistory = ref([
  {
    term: 'Fall 2024',
    credits: 15,
    gpa: '3.75',
    status: 'Enrolled',
    courses: [
      { id: 1, code: 'CS 301', title: 'Data Structures', credits: 3, grade: 'A-', gradePoints: 11.1 },
      { id: 2, code: 'MATH 201', title: 'Calculus II', credits: 4, grade: 'B+', gradePoints: 13.2 },
      { id: 3, code: 'ENG 102', title: 'Composition', credits: 3, grade: 'A', gradePoints: 12.0 },
      { id: 4, code: 'PHYS 101', title: 'Physics I', credits: 3, grade: 'B', gradePoints: 9.0 },
      { id: 5, code: 'HIST 101', title: 'World History', credits: 2, grade: 'A-', gradePoints: 7.4 }
    ]
  },
  {
    term: 'Spring 2024',
    credits: 16,
    gpa: '3.62',
    status: 'Completed',
    courses: [
      { id: 6, code: 'CS 201', title: 'Programming II', credits: 3, grade: 'A', gradePoints: 12.0 },
      { id: 7, code: 'MATH 101', title: 'Calculus I', credits: 4, grade: 'B+', gradePoints: 13.2 },
      { id: 8, code: 'ENG 101', title: 'Writing', credits: 3, grade: 'A-', gradePoints: 11.1 },
      { id: 9, code: 'CHEM 101', title: 'Chemistry I', credits: 4, grade: 'B', gradePoints: 12.0 },
      { id: 10, code: 'PE 101', title: 'Physical Education', credits: 2, grade: 'A', gradePoints: 8.0 }
    ]
  },
  {
    term: 'Fall 2023',
    credits: 15,
    gpa: '3.53',
    status: 'Completed',
    courses: [
      { id: 11, code: 'CS 101', title: 'Programming I', credits: 3, grade: 'A-', gradePoints: 11.1 },
      { id: 12, code: 'MATH 099', title: 'Pre-Calculus', credits: 3, grade: 'B+', gradePoints: 9.9 },
      { id: 13, code: 'BIO 101', title: 'Biology I', credits: 4, grade: 'B', gradePoints: 12.0 },
      { id: 14, code: 'SOC 101', title: 'Sociology', credits: 3, grade: 'A', gradePoints: 12.0 },
      { id: 15, code: 'ART 101', title: 'Art Appreciation', credits: 2, grade: 'A-', gradePoints: 7.4 }
    ]
  }
])

const honorsAndAwards = ref([
  {
    id: 1,
    title: 'Dean\'s List',
    description: 'Spring 2024 Semester',
    date: 'May 2024'
  },
  {
    id: 2,
    title: 'Academic Excellence Award',
    description: 'Computer Science Department',
    date: 'December 2023'
  }
])

const transferCredits = ref([
  {
    id: 1,
    institution: 'Community College',
    course: 'ENG 100',
    credits: 3,
    equivalent: 'ENG 101'
  }
])

// Methods
const printTranscript = () => {
  window.print()
}

const downloadTranscript = () => {
  useToast().add({
    title: 'Download Started',
    description: 'Your transcript PDF is being generated',
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const getGradeColor = (grade) => {
  if (grade.startsWith('A')) return 'green'
  if (grade.startsWith('B')) return 'blue'
  if (grade.startsWith('C')) return 'yellow'
  if (grade.startsWith('D')) return 'orange'
  return 'red'
}

const getEnrollmentStatusColor = (status) => {
  if (status === 'Completed') return 'green'
  if (status === 'Enrolled') return 'blue'
  if (status === 'Withdrawn') return 'red'
  return 'gray'
}
</script>
