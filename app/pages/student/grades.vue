<template>
  <UContainer class="py-6">
    <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">My Grades</h1>
        <p class="text-gray-600 dark:text-gray-300">View your academic performance and grades</p>
      </div>
      <div class="flex items-center space-x-3">
        <USelect
          v-model="selectedSemester"
          :options="semesterOptions"
          @change="filterGrades"
        />
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshGrades"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton color="primary" icon="i-heroicons-arrow-down-tray">
          Export
        </UButton>
      </div>
    </div>

    <!-- GPA Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <UCard>
        <div class="text-center">
          <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ currentGPA }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Current GPA</div>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ cumulativeGPA }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Cumulative GPA</div>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ creditsCompleted }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Credits Completed</div>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ classRank }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Class Rank</div>
        </div>
      </UCard>
    </div>

    <!-- Course Grades -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Course Grades</h2>
          <div class="flex items-center space-x-2">
            <UInput
              v-model="searchQuery"
              placeholder="Search courses..."
              icon="i-heroicons-magnifying-glass"
              size="sm"
              class="w-64"
            />
            <UButton variant="ghost" size="sm" icon="i-heroicons-funnel">
              Filter
            </UButton>
          </div>
        </div>
      </template>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Course
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Instructor
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Credits
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Grade
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Points
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="course in filteredCourses" :key="course.id" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ course.code }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ course.title }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ course.instructor }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ course.credits }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getGradeColor(course.grade)" variant="subtle">
                  {{ course.grade }}
                </UBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ course.gradePoints }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <UButton variant="ghost" size="sm" icon="i-heroicons-eye" @click="viewDetails(course)">
                  Details
                </UButton>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </UCard>

    <!-- Grade Breakdown Modal -->
    <UModal v-model="showDetails" :ui="{ width: 'sm:max-w-3xl' }">
      <UCard v-if="selectedCourse">
        <template #header>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold">{{ selectedCourse.code }} - Grade Breakdown</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ selectedCourse.title }}</p>
            </div>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showDetails = false"
            />
          </div>
        </template>

        <div class="space-y-6">
          <!-- Course Info -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Instructor:</span>
              <p class="text-sm">{{ selectedCourse.instructor }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Credits:</span>
              <p class="text-sm">{{ selectedCourse.credits }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Final Grade:</span>
              <UBadge :color="getGradeColor(selectedCourse.grade)" variant="subtle">
                {{ selectedCourse.grade }}
              </UBadge>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Grade Points:</span>
              <p class="text-sm">{{ selectedCourse.gradePoints }}</p>
            </div>
          </div>

          <!-- Assignments -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Assignment Breakdown</h4>
            <div class="space-y-2">
              <div
                v-for="assignment in selectedCourse.assignments"
                :key="assignment.id"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div class="flex-1">
                  <div class="font-medium text-gray-900 dark:text-white">{{ assignment.name }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-300">{{ assignment.category }}</div>
                </div>
                <div class="text-right">
                  <div class="font-medium" :class="getScoreColor(assignment.score)">
                    {{ assignment.score }}%
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{ assignment.points }} / {{ assignment.maxPoints }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Grade Distribution -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Category Weights</h4>
            <div class="space-y-3">
              <div
                v-for="category in selectedCourse.categories"
                :key="category.name"
                class="flex items-center justify-between"
              >
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ category.name }}</span>
                <div class="flex items-center space-x-2">
                  <div class="w-32">
                    <UProgress :value="category.average" :color="getProgressColor(category.average)" />
                  </div>
                  <span class="text-sm font-medium w-12">{{ category.average }}%</span>
                  <span class="text-xs text-gray-500 dark:text-gray-400 w-12">({{ category.weight }}%)</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end">
            <UButton @click="showDetails = false">
              Close
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'student'
})

// Reactive data
const isRefreshing = ref(false)
const showDetails = ref(false)
const selectedCourse = ref(null)
const searchQuery = ref('')
const selectedSemester = ref('fall-2024')

// Mock data
const currentGPA = ref('3.75')
const cumulativeGPA = ref('3.68')
const creditsCompleted = ref('87')
const classRank = ref('15/250')

const semesterOptions = [
  { label: 'Fall 2024', value: 'fall-2024' },
  { label: 'Spring 2024', value: 'spring-2024' },
  { label: 'Fall 2023', value: 'fall-2023' },
  { label: 'All Semesters', value: 'all' }
]

const courses = ref([
  {
    id: 1,
    code: 'CS 301',
    title: 'Data Structures and Algorithms',
    instructor: 'Dr. Sarah Johnson',
    credits: 3,
    grade: 'A-',
    gradePoints: 11.1,
    semester: 'fall-2024',
    assignments: [
      { id: 1, name: 'Homework 1', category: 'Homework', score: 95, points: 95, maxPoints: 100 },
      { id: 2, name: 'Midterm Exam', category: 'Exams', score: 88, points: 88, maxPoints: 100 },
      { id: 3, name: 'Project 1', category: 'Projects', score: 92, points: 92, maxPoints: 100 }
    ],
    categories: [
      { name: 'Homework', average: 94, weight: 30 },
      { name: 'Exams', average: 88, weight: 40 },
      { name: 'Projects', average: 92, weight: 30 }
    ]
  },
  {
    id: 2,
    code: 'MATH 201',
    title: 'Calculus II',
    instructor: 'Prof. Michael Chen',
    credits: 4,
    grade: 'B+',
    gradePoints: 13.2,
    semester: 'fall-2024',
    assignments: [
      { id: 1, name: 'Quiz 1', category: 'Quizzes', score: 85, points: 85, maxPoints: 100 },
      { id: 2, name: 'Midterm', category: 'Exams', score: 82, points: 82, maxPoints: 100 },
      { id: 3, name: 'Homework Set 1', category: 'Homework', score: 90, points: 90, maxPoints: 100 }
    ],
    categories: [
      { name: 'Quizzes', average: 85, weight: 20 },
      { name: 'Exams', average: 82, weight: 50 },
      { name: 'Homework', average: 90, weight: 30 }
    ]
  },
  {
    id: 3,
    code: 'ENG 102',
    title: 'Composition and Literature',
    instructor: 'Dr. Emily Rodriguez',
    credits: 3,
    grade: 'A',
    gradePoints: 12.0,
    semester: 'fall-2024',
    assignments: [
      { id: 1, name: 'Essay 1', category: 'Essays', score: 95, points: 95, maxPoints: 100 },
      { id: 2, name: 'Midterm Exam', category: 'Exams', score: 93, points: 93, maxPoints: 100 },
      { id: 3, name: 'Participation', category: 'Participation', score: 98, points: 98, maxPoints: 100 }
    ],
    categories: [
      { name: 'Essays', average: 95, weight: 50 },
      { name: 'Exams', average: 93, weight: 30 },
      { name: 'Participation', average: 98, weight: 20 }
    ]
  }
])

const filteredCourses = ref([...courses.value])

// Methods
const filterGrades = () => {
  let filtered = [...courses.value]
  
  if (selectedSemester.value !== 'all') {
    filtered = filtered.filter(course => course.semester === selectedSemester.value)
  }
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(course => 
      course.code.toLowerCase().includes(query) ||
      course.title.toLowerCase().includes(query) ||
      course.instructor.toLowerCase().includes(query)
    )
  }
  
  filteredCourses.value = filtered
}

const refreshGrades = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    useToast().add({
      title: 'Grades Refreshed',
      description: 'Your grades have been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const viewDetails = (course) => {
  selectedCourse.value = course
  showDetails.value = true
}

const getGradeColor = (grade) => {
  if (grade.startsWith('A')) return 'green'
  if (grade.startsWith('B')) return 'blue'
  if (grade.startsWith('C')) return 'yellow'
  if (grade.startsWith('D')) return 'orange'
  return 'red'
}

const getScoreColor = (score) => {
  if (score >= 90) return 'text-green-600 dark:text-green-400'
  if (score >= 80) return 'text-blue-600 dark:text-blue-400'
  if (score >= 70) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-red-600 dark:text-red-400'
}

const getProgressColor = (value) => {
  if (value >= 90) return 'green'
  if (value >= 80) return 'blue'
  if (value >= 70) return 'yellow'
  return 'red'
}

// Watch for changes
watch([searchQuery, selectedSemester], () => {
  filterGrades()
})

// Initialize
onMounted(() => {
  filterGrades()
})
</script>
