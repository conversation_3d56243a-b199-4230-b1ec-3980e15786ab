<template>
  <UContainer class="py-6">
    <div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg p-6 text-white relative overflow-hidden">
      <div class="relative z-10">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold mb-2">Welcome, {{ currentUser?.name || 'Dr. Faculty' }}!</h1>
            <p class="text-green-100">Manage your classes and connect with students</p>
          </div>
          <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <UButton
              color="white"
              variant="solid"
              to="/faculty/grading"
              icon="i-heroicons-chart-bar-square"
            >
              Grade Assignments
            </UButton>
          </div>
        </div>
      </div>
      <!-- Background decoration -->
      <div class="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-white/10 rounded-full"></div>
      <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-white/5 rounded-full"></div>
    </div>

    <!-- Faculty Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Loading Skeletons -->
      <UCard
        v-if="isLoading"
        v-for="n in 6"
        :key="`metric-skeleton-${n}`"
        class="animate-pulse"
      >
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <USkeleton class="w-12 h-12 rounded-lg" />
          </div>
          <div class="ml-4 space-y-2">
            <USkeleton class="h-3 w-24" />
            <USkeleton class="h-6 w-16" />
          </div>
        </div>
        <div class="mt-3">
          <USkeleton class="h-3 w-32" />
        </div>
      </UCard>

      <!-- Actual Metrics -->
      <UCard v-else v-for="metric in facultyMetrics" :key="metric.id" class="relative overflow-hidden">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center',
              getMetricBackgroundClass(metric.color)
            ]">
              <UIcon
                :name="metric.icon"
                :class="[
                  'w-6 h-6',
                  getMetricIconClass(metric.color)
                ]"
              />
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">{{ metric.title }}</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ metric.value }}</p>
            <div v-if="metric.change" class="flex items-center mt-1">
              <UIcon
                :name="metric.changeType === 'increase' ? 'i-heroicons-arrow-trending-up' :
                      metric.changeType === 'decrease' ? 'i-heroicons-arrow-trending-down' :
                      'i-heroicons-minus'"
                :class="[
                  'w-4 h-4 mr-1',
                  metric.changeType === 'increase' ? 'text-green-500' :
                  metric.changeType === 'decrease' ? 'text-red-500' :
                  'text-gray-500'
                ]"
              />
              <span :class="[
                'text-sm font-medium',
                metric.changeType === 'increase' ? 'text-green-600 dark:text-green-400' :
                metric.changeType === 'decrease' ? 'text-red-600 dark:text-red-400' :
                'text-gray-600 dark:text-gray-400'
              ]">
                {{ metric.change }}
              </span>
            </div>
          </div>
        </div>
        <div class="mt-3">
          <p class="text-xs text-gray-500 dark:text-gray-400">{{ metric.description }}</p>
        </div>
      </UCard>
    </div>

    <!-- Teaching Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Current Classes -->
      <div class="lg:col-span-2">
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold">Current Classes</h2>
              <UButton to="/faculty/classes/current" variant="ghost" size="sm">
                View All
              </UButton>
            </div>
          </template>
          <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-code-bracket" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">CS 101 - Intro to Programming</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">MWF 10:00 AM • Room 204 • 32 students</p>
                </div>
              </div>
              <UBadge color="green" variant="soft">Active</UBadge>
            </div>
            
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-cpu-chip" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">CS 301 - Data Structures</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">TTh 2:00 PM • Room 301 • 28 students</p>
                </div>
              </div>
              <UBadge color="green" variant="soft">Active</UBadge>
            </div>
            
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-beaker" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">CS 499 - Senior Capstone</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">F 3:00 PM • Room 401 • 15 students</p>
                </div>
              </div>
              <UBadge color="green" variant="soft">Active</UBadge>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Faculty Stats & Actions -->
      <div class="space-y-6">
        <UCard>
          <template #header>
            <h2 class="text-lg font-semibold">Teaching Load</h2>
          </template>
          <div class="space-y-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600 dark:text-green-400">12</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">Credit Hours</div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">75</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">Total Students</div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">23</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">Pending Grades</div>
            </div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h2 class="text-lg font-semibold">Quick Actions</h2>
          </template>
          <div class="space-y-2">
            <UButton
              to="/faculty/grading"
              block
              variant="outline"
              icon="i-heroicons-chart-bar-square"
            >
              Grade Assignments
            </UButton>
            <UButton
              to="/faculty/classes/roster"
              block
              variant="outline"
              icon="i-heroicons-users"
            >
              View Rosters
            </UButton>
            <UButton
              to="/faculty/office-hours"
              block
              variant="outline"
              icon="i-heroicons-clock"
            >
              Office Hours
            </UButton>
          </div>
        </UCard>
      </div>
    </div>

    <!-- Pending Tasks & Schedule -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">Pending Tasks</h2>
        </template>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">Grade Midterm Exams</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">CS 301 - 28 submissions</p>
            </div>
            <UBadge color="red" variant="soft">Urgent</UBadge>
          </div>
          
          <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">Review Project Proposals</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">CS 499 - 15 proposals</p>
            </div>
            <UBadge color="yellow" variant="soft">Due Soon</UBadge>
          </div>
          
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">Update Course Materials</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">CS 101 - Week 8 content</p>
            </div>
            <UBadge color="gray" variant="soft">Scheduled</UBadge>
          </div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">Today's Schedule</h2>
        </template>
        <div class="space-y-3">
          <div class="flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="text-center">
              <div class="text-sm font-medium text-blue-600 dark:text-blue-400">10:00</div>
              <div class="text-xs text-gray-500">AM</div>
            </div>
            <div class="flex-1">
              <h3 class="font-medium text-gray-900 dark:text-white">CS 101 Lecture</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">Room 204 • Introduction to Variables</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div class="text-center">
              <div class="text-sm font-medium text-green-600 dark:text-green-400">2:00</div>
              <div class="text-xs text-gray-500">PM</div>
            </div>
            <div class="flex-1">
              <h3 class="font-medium text-gray-900 dark:text-white">Office Hours</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">Office 305 • Student consultations</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div class="text-center">
              <div class="text-sm font-medium text-purple-600 dark:text-purple-400">4:00</div>
              <div class="text-xs text-gray-500">PM</div>
            </div>
            <div class="flex-1">
              <h3 class="font-medium text-gray-900 dark:text-white">Faculty Meeting</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">Conference Room A • Curriculum Review</p>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Research & Publications -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Research Activity</h2>
          <UButton to="/faculty/research" variant="ghost" size="sm">
            View Details
          </UButton>
        </div>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">3</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Active Projects</div>
        </div>
        <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">12</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Publications</div>
        </div>
        <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">$150K</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Grant Funding</div>
        </div>
      </div>
    </UCard>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'faculty'
})

const { currentUser } = useNavigation()
const { facultyMetrics, generateActivities, generateNotifications } = useDashboardData()

// Helper functions for metric colors
const getMetricBackgroundClass = (color) => {
  const classes = {
    blue: 'bg-blue-100 dark:bg-blue-900/20',
    green: 'bg-green-100 dark:bg-green-900/20',
    purple: 'bg-purple-100 dark:bg-purple-900/20',
    yellow: 'bg-yellow-100 dark:bg-yellow-900/20',
    red: 'bg-red-100 dark:bg-red-900/20',
    orange: 'bg-orange-100 dark:bg-orange-900/20',
    indigo: 'bg-indigo-100 dark:bg-indigo-900/20',
    pink: 'bg-pink-100 dark:bg-pink-900/20'
  }
  return classes[color] || 'bg-gray-100 dark:bg-gray-900/20'
}

const getMetricIconClass = (color) => {
  const classes = {
    blue: 'text-blue-600 dark:text-blue-400',
    green: 'text-green-600 dark:text-green-400',
    purple: 'text-purple-600 dark:text-purple-400',
    yellow: 'text-yellow-600 dark:text-yellow-400',
    red: 'text-red-600 dark:text-red-400',
    orange: 'text-orange-600 dark:text-orange-400',
    indigo: 'text-indigo-600 dark:text-indigo-400',
    pink: 'text-pink-600 dark:text-pink-400'
  }
  return classes[color] || 'text-gray-600 dark:text-gray-400'
}

// Reactive data
const isLoading = ref(true)
const recentActivities = ref(generateActivities('faculty', 6))
const recentNotifications = ref(generateNotifications('faculty', 3))

// Auto-refresh activities every 10 minutes
onMounted(async () => {
  // Simulate initial loading
  await new Promise(resolve => setTimeout(resolve, 1200))
  isLoading.value = false

  const interval = setInterval(() => {
    recentActivities.value = generateActivities('faculty', 6)
  }, 10 * 60 * 1000)

  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>
