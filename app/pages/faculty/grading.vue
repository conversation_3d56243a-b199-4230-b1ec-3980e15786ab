<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Grading Center</h1>
        <p class="text-gray-600 dark:text-gray-300">Manage grades and assignments for your courses</p>
      </div>
      <div class="flex items-center space-x-3">
        <USelect
          v-model="selectedCourse"
          :options="courseOptions"
          placeholder="Select Course"
          @change="loadAssignments"
        />
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshGrades"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton color="primary" icon="i-heroicons-plus" @click="showCreateAssignment = true">
          New Assignment
        </UButton>
      </div>
    </div>

    <!-- Grading Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-document-text" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Assignments</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalAssignments }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-clock" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Grades</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ pendingGrades }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Class Average</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ classAverage }}%</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Students</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalStudents }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Assignment List -->
    <UCard v-if="selectedCourse">
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Assignments - {{ getCourseName(selectedCourse) }}</h2>
          <div class="flex items-center space-x-2">
            <UInput
              v-model="searchQuery"
              placeholder="Search assignments..."
              icon="i-heroicons-magnifying-glass"
              size="sm"
              class="w-64"
            />
            <UButton variant="ghost" size="sm" icon="i-heroicons-funnel">
              Filter
            </UButton>
          </div>
        </div>
      </template>

      <div class="space-y-4">
        <div
          v-for="assignment in filteredAssignments"
          :key="assignment.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
        >
          <div class="flex items-center justify-between mb-3">
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">{{ assignment.title }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">{{ assignment.type }} • Due: {{ formatDate(assignment.dueDate) }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <UBadge :color="getStatusColor(assignment.status)" variant="subtle">
                {{ assignment.status }}
              </UBadge>
              <UButton
                size="sm"
                variant="outline"
                @click="gradeAssignment(assignment)"
              >
                Grade ({{ assignment.gradedCount }}/{{ assignment.totalSubmissions }})
              </UButton>
            </div>
          </div>

          <!-- Assignment Stats -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-600 dark:text-gray-400">Max Points:</span>
              <span class="font-medium ml-1">{{ assignment.maxPoints }}</span>
            </div>
            <div>
              <span class="text-gray-600 dark:text-gray-400">Submissions:</span>
              <span class="font-medium ml-1">{{ assignment.totalSubmissions }}</span>
            </div>
            <div>
              <span class="text-gray-600 dark:text-gray-400">Average:</span>
              <span class="font-medium ml-1">{{ assignment.average }}%</span>
            </div>
            <div>
              <span class="text-gray-600 dark:text-gray-400">Weight:</span>
              <span class="font-medium ml-1">{{ assignment.weight }}%</span>
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="mt-3">
            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
              <span>Grading Progress</span>
              <span>{{ Math.round((assignment.gradedCount / assignment.totalSubmissions) * 100) }}%</span>
            </div>
            <UProgress 
              :value="(assignment.gradedCount / assignment.totalSubmissions) * 100" 
              :color="assignment.gradedCount === assignment.totalSubmissions ? 'green' : 'blue'"
            />
          </div>
        </div>
      </div>
    </UCard>

    <!-- No Course Selected -->
    <UCard v-else>
      <div class="text-center py-12">
        <UIcon name="i-heroicons-academic-cap" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Select a Course</h3>
        <p class="text-gray-600 dark:text-gray-300">Choose a course from the dropdown to view and grade assignments.</p>
      </div>
    </UCard>

    <!-- Grading Modal -->
    <UModal v-model="showGradingModal" :ui="{ width: 'sm:max-w-4xl' }">
      <UCard v-if="selectedAssignment">
        <template #header>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold">Grade Assignment: {{ selectedAssignment.title }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ getCourseName(selectedCourse) }}</p>
            </div>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showGradingModal = false"
            />
          </div>
        </template>

        <div class="space-y-6">
          <!-- Student List -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Student</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Submitted</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Grade</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Feedback</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="student in selectedAssignment.students" :key="student.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <UAvatar :src="student.avatar" :alt="student.name" size="sm" />
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ student.name }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ student.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <UBadge :color="student.submitted ? 'green' : 'red'" variant="subtle">
                      {{ student.submitted ? 'Yes' : 'No' }}
                    </UBadge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <UInput
                      v-if="student.submitted"
                      v-model="student.grade"
                      type="number"
                      :max="selectedAssignment.maxPoints"
                      min="0"
                      size="sm"
                      class="w-20"
                      @change="updateGrade(student)"
                    />
                    <span v-else class="text-gray-400">-</span>
                  </td>
                  <td class="px-6 py-4">
                    <UTextarea
                      v-if="student.submitted"
                      v-model="student.feedback"
                      placeholder="Add feedback..."
                      rows="2"
                      class="w-48"
                      @change="updateFeedback(student)"
                    />
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-2">
                      <UButton
                        v-if="student.submitted"
                        size="sm"
                        variant="ghost"
                        icon="i-heroicons-eye"
                        @click="viewSubmission(student)"
                      >
                        View
                      </UButton>
                      <UButton
                        v-if="student.submitted"
                        size="sm"
                        variant="ghost"
                        icon="i-heroicons-arrow-down-tray"
                        @click="downloadSubmission(student)"
                      >
                        Download
                      </UButton>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-between">
            <div class="flex items-center space-x-2">
              <UButton variant="outline" @click="exportGrades">
                Export Grades
              </UButton>
              <UButton variant="outline" @click="bulkGrade">
                Bulk Grade
              </UButton>
            </div>
            <div class="flex items-center space-x-2">
              <UButton variant="outline" @click="showGradingModal = false">
                Close
              </UButton>
              <UButton color="primary" @click="saveAllGrades" :loading="isSaving">
                Save All Grades
              </UButton>
            </div>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Create Assignment Modal -->
    <UModal v-model="showCreateAssignment">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Create New Assignment</h3>
        </template>

        <form @submit.prevent="createAssignment" class="space-y-4">
          <UInput
            v-model="newAssignment.title"
            placeholder="Assignment Title"
            required
          />
          <USelect
            v-model="newAssignment.type"
            :options="assignmentTypes"
            placeholder="Assignment Type"
            required
          />
          <UInput
            v-model="newAssignment.maxPoints"
            type="number"
            placeholder="Max Points"
            required
          />
          <UInput
            v-model="newAssignment.weight"
            type="number"
            placeholder="Weight (%)"
            required
          />
          <UInput
            v-model="newAssignment.dueDate"
            type="datetime-local"
            required
          />
          <UTextarea
            v-model="newAssignment.description"
            placeholder="Assignment Description"
            rows="3"
          />
        </form>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton variant="outline" @click="showCreateAssignment = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="createAssignment">
              Create Assignment
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'faculty'
})

// Reactive data
const isRefreshing = ref(false)
const isSaving = ref(false)
const showGradingModal = ref(false)
const showCreateAssignment = ref(false)
const selectedCourse = ref('')
const selectedAssignment = ref(null)
const searchQuery = ref('')

// Mock data
const courses = ref([
  { id: 'cs301', code: 'CS 301', title: 'Data Structures', students: 25 },
  { id: 'cs201', code: 'CS 201', title: 'Programming II', students: 30 },
  { id: 'cs101', code: 'CS 101', title: 'Intro to CS', students: 35 }
])

const courseOptions = computed(() => [
  { label: 'Select Course', value: '' },
  ...courses.value.map(course => ({
    label: `${course.code} - ${course.title}`,
    value: course.id
  }))
])

const assignments = ref([])
const filteredAssignments = ref([])

const assignmentTypes = [
  { label: 'Homework', value: 'homework' },
  { label: 'Quiz', value: 'quiz' },
  { label: 'Exam', value: 'exam' },
  { label: 'Project', value: 'project' },
  { label: 'Lab', value: 'lab' }
]

const newAssignment = ref({
  title: '',
  type: '',
  maxPoints: '',
  weight: '',
  dueDate: '',
  description: ''
})

// Computed properties
const totalAssignments = computed(() => assignments.value.length)
const pendingGrades = computed(() => {
  return assignments.value.reduce((count, assignment) => {
    return count + (assignment.totalSubmissions - assignment.gradedCount)
  }, 0)
})
const classAverage = computed(() => {
  if (assignments.value.length === 0) return 0
  const total = assignments.value.reduce((sum, assignment) => sum + assignment.average, 0)
  return Math.round(total / assignments.value.length)
})
const totalStudents = computed(() => {
  const course = courses.value.find(c => c.id === selectedCourse.value)
  return course ? course.students : 0
})

// Methods
const loadAssignments = () => {
  if (!selectedCourse.value) {
    assignments.value = []
    return
  }

  // Mock assignment data
  assignments.value = [
    {
      id: 1,
      title: 'Binary Tree Implementation',
      type: 'Project',
      dueDate: new Date('2024-02-15'),
      maxPoints: 100,
      weight: 15,
      status: 'Active',
      totalSubmissions: 23,
      gradedCount: 18,
      average: 87,
      students: generateMockStudents(23)
    },
    {
      id: 2,
      title: 'Sorting Algorithms Quiz',
      type: 'Quiz',
      dueDate: new Date('2024-02-10'),
      maxPoints: 50,
      weight: 10,
      status: 'Completed',
      totalSubmissions: 25,
      gradedCount: 25,
      average: 82,
      students: generateMockStudents(25)
    }
  ]
  
  filterAssignments()
}

const filterAssignments = () => {
  let filtered = [...assignments.value]
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(assignment => 
      assignment.title.toLowerCase().includes(query) ||
      assignment.type.toLowerCase().includes(query)
    )
  }
  
  filteredAssignments.value = filtered
}

const generateMockStudents = (count) => {
  const students = []
  for (let i = 1; i <= count; i++) {
    students.push({
      id: i,
      name: `Student ${i}`,
      email: `student${i}@college.edu`,
      avatar: `https://github.com/student${i}.png`,
      submitted: Math.random() > 0.1, // 90% submission rate
      grade: Math.floor(Math.random() * 30) + 70, // Random grade 70-100
      feedback: ''
    })
  }
  return students
}

const refreshGrades = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    loadAssignments()
    useToast().add({
      title: 'Grades Refreshed',
      description: 'Grade data has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const gradeAssignment = (assignment) => {
  selectedAssignment.value = assignment
  showGradingModal.value = true
}

const createAssignment = () => {
  useToast().add({
    title: 'Assignment Created',
    description: `${newAssignment.value.title} has been created`,
    icon: 'i-heroicons-plus'
  })
  showCreateAssignment.value = false
  newAssignment.value = {
    title: '',
    type: '',
    maxPoints: '',
    weight: '',
    dueDate: '',
    description: ''
  }
}

const updateGrade = (student) => {
  useToast().add({
    title: 'Grade Updated',
    description: `Grade updated for ${student.name}`,
    icon: 'i-heroicons-check'
  })
}

const updateFeedback = (student) => {
  useToast().add({
    title: 'Feedback Updated',
    description: `Feedback updated for ${student.name}`,
    icon: 'i-heroicons-chat-bubble-left'
  })
}

const saveAllGrades = async () => {
  isSaving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    useToast().add({
      title: 'Grades Saved',
      description: 'All grades have been saved successfully',
      icon: 'i-heroicons-check-circle'
    })
    showGradingModal.value = false
  } finally {
    isSaving.value = false
  }
}

const viewSubmission = (student) => {
  useToast().add({
    title: 'View Submission',
    description: `Opening submission for ${student.name}`,
    icon: 'i-heroicons-eye'
  })
}

const downloadSubmission = (student) => {
  useToast().add({
    title: 'Download Started',
    description: `Downloading submission from ${student.name}`,
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const exportGrades = () => {
  useToast().add({
    title: 'Export Started',
    description: 'Exporting grades to CSV',
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const bulkGrade = () => {
  useToast().add({
    title: 'Bulk Grade',
    description: 'Opening bulk grading interface',
    icon: 'i-heroicons-squares-plus'
  })
}

const getCourseName = (courseId) => {
  const course = courses.value.find(c => c.id === courseId)
  return course ? `${course.code} - ${course.title}` : ''
}

const getStatusColor = (status) => {
  const colors = {
    'Active': 'blue',
    'Completed': 'green',
    'Draft': 'gray'
  }
  return colors[status] || 'gray'
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date)
}

// Watch for changes
watch([searchQuery], () => {
  filterAssignments()
})

watch(selectedCourse, () => {
  loadAssignments()
})
</script>
