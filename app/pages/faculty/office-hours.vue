<template>
  <UContainer class="py-6">
    <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Office Hours</h1>
        <p class="text-gray-600 dark:text-gray-300">Manage your office hours and student appointments</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshSchedule"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton color="primary" icon="i-heroicons-plus" @click="showAddOfficeHours = true">
          Add Office Hours
        </UButton>
      </div>
    </div>

    <!-- Office Hours Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-clock" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Weekly Hours</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ weeklyHours }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-calendar-days" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">This Week</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ thisWeekAppointments }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Requests</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ pendingRequests }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-map-pin" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Office Location</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ officeLocation }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Weekly Schedule -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Weekly Schedule</h2>
          <div class="flex items-center space-x-2">
            <UButton variant="ghost" size="sm" icon="i-heroicons-arrow-left" @click="previousWeek">
              Previous
            </UButton>
            <span class="text-sm text-gray-600 dark:text-gray-400">{{ currentWeekRange }}</span>
            <UButton variant="ghost" size="sm" icon="i-heroicons-arrow-right" @click="nextWeek">
              Next
            </UButton>
          </div>
        </div>
      </template>

      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-3 px-4 text-sm font-medium text-gray-600 dark:text-gray-400">Time</th>
              <th v-for="day in weekDays" :key="day" class="text-center py-3 px-4 text-sm font-medium text-gray-600 dark:text-gray-400">
                {{ day }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="timeSlot in timeSlots" :key="timeSlot" class="border-b border-gray-100 dark:border-gray-800">
              <td class="py-3 px-4 text-sm font-medium text-gray-900 dark:text-white">{{ timeSlot }}</td>
              <td v-for="day in weekDays" :key="`${day}-${timeSlot}`" class="py-3 px-4 text-center">
                <div
                  v-if="getScheduleItem(day, timeSlot)"
                  :class="getScheduleItemClass(getScheduleItem(day, timeSlot))"
                  class="p-2 rounded-lg text-xs cursor-pointer"
                  @click="editScheduleItem(getScheduleItem(day, timeSlot))"
                >
                  <div class="font-medium">{{ getScheduleItem(day, timeSlot).type }}</div>
                  <div v-if="getScheduleItem(day, timeSlot).student" class="text-xs opacity-75">
                    {{ getScheduleItem(day, timeSlot).student }}
                  </div>
                </div>
                <button
                  v-else
                  @click="addOfficeHour(day, timeSlot)"
                  class="w-full h-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
                >
                  <UIcon name="i-heroicons-plus" class="w-4 h-4 text-gray-400" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </UCard>

    <!-- Appointment Requests -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Appointment Requests</h2>
          <UBadge v-if="appointmentRequests.length > 0" color="yellow" variant="subtle">
            {{ appointmentRequests.length }} pending
          </UBadge>
        </div>
      </template>

      <div v-if="appointmentRequests.length === 0" class="text-center py-8">
        <UIcon name="i-heroicons-calendar-days" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Pending Requests</h3>
        <p class="text-gray-600 dark:text-gray-300">All appointment requests have been processed.</p>
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="request in appointmentRequests"
          :key="request.id"
          class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
        >
          <div class="flex items-center space-x-4">
            <UAvatar :src="request.student.avatar" :alt="request.student.name" size="sm" />
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">{{ request.student.name }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">{{ request.student.email }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ request.course }}</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ formatDateTime(request.requestedTime) }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">{{ request.reason }}</p>
          </div>
          <div class="flex items-center space-x-2">
            <UButton
              size="sm"
              color="green"
              @click="approveRequest(request)"
            >
              Approve
            </UButton>
            <UButton
              size="sm"
              variant="outline"
              color="red"
              @click="declineRequest(request)"
            >
              Decline
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Upcoming Appointments -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">Upcoming Appointments</h2>
      </template>

      <div class="space-y-3">
        <div
          v-for="appointment in upcomingAppointments"
          :key="appointment.id"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <UAvatar :src="appointment.student.avatar" :alt="appointment.student.name" size="sm" />
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">{{ appointment.student.name }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">{{ appointment.course }}</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ formatDateTime(appointment.time) }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">{{ appointment.duration }} minutes</p>
          </div>
          <div class="flex items-center space-x-2">
            <UButton
              size="sm"
              variant="outline"
              icon="i-heroicons-pencil"
              @click="editAppointment(appointment)"
            >
              Edit
            </UButton>
            <UButton
              size="sm"
              variant="outline"
              color="red"
              icon="i-heroicons-x-mark"
              @click="cancelAppointment(appointment)"
            >
              Cancel
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Add Office Hours Modal -->
    <UModal v-model="showAddOfficeHours">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Add Office Hours</h3>
        </template>

        <form @submit.prevent="addOfficeHours" class="space-y-4">
          <USelect
            v-model="newOfficeHour.day"
            :options="dayOptions"
            placeholder="Select Day"
            required
          />
          <UInput
            v-model="newOfficeHour.startTime"
            type="time"
            placeholder="Start Time"
            required
          />
          <UInput
            v-model="newOfficeHour.endTime"
            type="time"
            placeholder="End Time"
            required
          />
          <USelect
            v-model="newOfficeHour.type"
            :options="officeHourTypes"
            placeholder="Type"
            required
          />
          <UTextarea
            v-model="newOfficeHour.notes"
            placeholder="Notes (optional)"
            rows="2"
          />
        </form>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton variant="outline" @click="showAddOfficeHours = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addOfficeHours">
              Add Office Hours
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'faculty'
})

// Reactive data
const isRefreshing = ref(false)
const showAddOfficeHours = ref(false)
const currentWeek = ref(new Date())

// Mock data
const weeklyHours = ref('12')
const thisWeekAppointments = ref(8)
const pendingRequests = ref(3)
const officeLocation = ref('Room 204')

const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
const timeSlots = [
  '9:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
  '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM'
]

const dayOptions = weekDays.map(day => ({ label: day, value: day.toLowerCase() }))
const officeHourTypes = [
  { label: 'Office Hours', value: 'office-hours' },
  { label: 'Appointment', value: 'appointment' },
  { label: 'Meeting', value: 'meeting' },
  { label: 'Blocked', value: 'blocked' }
]

const newOfficeHour = ref({
  day: '',
  startTime: '',
  endTime: '',
  type: '',
  notes: ''
})

// Mock schedule data
const schedule = ref([
  {
    id: 1,
    day: 'Monday',
    time: '10:00 AM',
    type: 'Office Hours',
    student: null,
    duration: 60
  },
  {
    id: 2,
    day: 'Wednesday',
    time: '2:00 PM',
    type: 'Appointment',
    student: 'John Doe',
    duration: 30
  },
  {
    id: 3,
    day: 'Friday',
    time: '11:00 AM',
    type: 'Office Hours',
    student: null,
    duration: 60
  }
])

const appointmentRequests = ref([
  {
    id: 1,
    student: {
      name: 'Alice Johnson',
      email: '<EMAIL>',
      avatar: 'https://github.com/alice.png'
    },
    course: 'CS 301',
    requestedTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
    reason: 'Help with assignment'
  },
  {
    id: 2,
    student: {
      name: 'Bob Smith',
      email: '<EMAIL>',
      avatar: 'https://github.com/bob.png'
    },
    course: 'CS 201',
    requestedTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    reason: 'Discuss project'
  }
])

const upcomingAppointments = ref([
  {
    id: 1,
    student: {
      name: 'Charlie Brown',
      email: '<EMAIL>',
      avatar: 'https://github.com/charlie.png'
    },
    course: 'CS 301',
    time: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
    duration: 30
  },
  {
    id: 2,
    student: {
      name: 'Diana Prince',
      email: '<EMAIL>',
      avatar: 'https://github.com/diana.png'
    },
    course: 'CS 201',
    time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
    duration: 45
  }
])

// Computed properties
const currentWeekRange = computed(() => {
  const start = new Date(currentWeek.value)
  start.setDate(start.getDate() - start.getDay() + 1) // Monday
  const end = new Date(start)
  end.setDate(end.getDate() + 4) // Friday
  
  return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`
})

// Methods
const getScheduleItem = (day, time) => {
  return schedule.value.find(item => item.day === day && item.time === time)
}

const getScheduleItemClass = (item) => {
  const baseClass = 'text-white'
  const typeClasses = {
    'Office Hours': 'bg-blue-500',
    'Appointment': 'bg-green-500',
    'Meeting': 'bg-purple-500',
    'Blocked': 'bg-red-500'
  }
  return `${baseClass} ${typeClasses[item.type] || 'bg-gray-500'}`
}

const refreshSchedule = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    useToast().add({
      title: 'Schedule Refreshed',
      description: 'Office hours schedule has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const previousWeek = () => {
  currentWeek.value.setDate(currentWeek.value.getDate() - 7)
  currentWeek.value = new Date(currentWeek.value)
}

const nextWeek = () => {
  currentWeek.value.setDate(currentWeek.value.getDate() + 7)
  currentWeek.value = new Date(currentWeek.value)
}

const addOfficeHour = (day, time) => {
  newOfficeHour.value.day = day.toLowerCase()
  newOfficeHour.value.startTime = time.toLowerCase().replace(' ', '')
  showAddOfficeHours.value = true
}

const addOfficeHours = () => {
  useToast().add({
    title: 'Office Hours Added',
    description: 'New office hours have been scheduled',
    icon: 'i-heroicons-plus'
  })
  showAddOfficeHours.value = false
  newOfficeHour.value = {
    day: '',
    startTime: '',
    endTime: '',
    type: '',
    notes: ''
  }
}

const editScheduleItem = (item) => {
  useToast().add({
    title: 'Edit Schedule',
    description: `Editing ${item.type}`,
    icon: 'i-heroicons-pencil'
  })
}

const approveRequest = (request) => {
  const index = appointmentRequests.value.findIndex(r => r.id === request.id)
  if (index > -1) {
    appointmentRequests.value.splice(index, 1)
    useToast().add({
      title: 'Request Approved',
      description: `Appointment approved for ${request.student.name}`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }
}

const declineRequest = (request) => {
  const index = appointmentRequests.value.findIndex(r => r.id === request.id)
  if (index > -1) {
    appointmentRequests.value.splice(index, 1)
    useToast().add({
      title: 'Request Declined',
      description: `Appointment declined for ${request.student.name}`,
      icon: 'i-heroicons-x-circle',
      color: 'red'
    })
  }
}

const editAppointment = (appointment) => {
  useToast().add({
    title: 'Edit Appointment',
    description: `Editing appointment with ${appointment.student.name}`,
    icon: 'i-heroicons-pencil'
  })
}

const cancelAppointment = (appointment) => {
  useToast().add({
    title: 'Appointment Cancelled',
    description: `Appointment with ${appointment.student.name} has been cancelled`,
    icon: 'i-heroicons-x-circle',
    color: 'red'
  })
}

const formatDateTime = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}
</script>
