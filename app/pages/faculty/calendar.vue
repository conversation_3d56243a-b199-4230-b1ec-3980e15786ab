<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Faculty Calendar</h1>
          <p class="text-gray-600 dark:text-gray-300">Manage your schedule and appointments</p>
        </div>
        <UButton color="primary" icon="i-heroicons-plus">
          Add Event
        </UButton>
      </div>

      <!-- Calendar Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-calendar-days" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Events</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">5</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Classes</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">3</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-clock" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Office Hours</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">2</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-users" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Meetings</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">1</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Today's Schedule -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Today's Schedule</h2>
            <div class="flex items-center space-x-2">
              <UButton variant="outline" size="sm" icon="i-heroicons-arrow-left">
                Previous
              </UButton>
              <UButton variant="outline" size="sm" icon="i-heroicons-arrow-right">
                Next
              </UButton>
            </div>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-start space-x-4 p-4 border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Computer Science 101</h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">9:00 AM - 10:30 AM</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Introduction to Programming - Room 204</p>
              <div class="flex items-center space-x-2 mt-2">
                <UBadge color="blue" variant="soft" size="xs">Class</UBadge>
                <UBadge color="gray" variant="soft" size="xs">45 Students</UBadge>
              </div>
            </div>
          </div>

          <div class="flex items-start space-x-4 p-4 border-l-4 border-purple-500 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-clock" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Office Hours</h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">11:00 AM - 1:00 PM</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Student consultations - Office 312</p>
              <div class="flex items-center space-x-2 mt-2">
                <UBadge color="purple" variant="soft" size="xs">Office Hours</UBadge>
                <UBadge color="green" variant="soft" size="xs">Available</UBadge>
              </div>
            </div>
          </div>

          <div class="flex items-start space-x-4 p-4 border-l-4 border-green-500 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Data Structures & Algorithms</h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">2:00 PM - 3:30 PM</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Advanced Programming Concepts - Room 301</p>
              <div class="flex items-center space-x-2 mt-2">
                <UBadge color="green" variant="soft" size="xs">Class</UBadge>
                <UBadge color="gray" variant="soft" size="xs">32 Students</UBadge>
              </div>
            </div>
          </div>

          <div class="flex items-start space-x-4 p-4 border-l-4 border-orange-500 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-users" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Department Meeting</h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">4:00 PM - 5:00 PM</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Monthly faculty meeting - Conference Room A</p>
              <div class="flex items-center space-x-2 mt-2">
                <UBadge color="orange" variant="soft" size="xs">Meeting</UBadge>
                <UBadge color="red" variant="soft" size="xs">Required</UBadge>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'faculty'
})
</script>
