<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Research</h1>
          <p class="text-gray-600 dark:text-gray-300">Manage your research projects and publications</p>
        </div>
        <UButton color="primary" icon="i-heroicons-plus">
          New Project
        </UButton>
      </div>

      <!-- Research Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-beaker" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Projects</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">3</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-document-text" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Publications</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">12</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-currency-dollar" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Grant Funding</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">$125K</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-users" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Collaborators</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">8</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Active Projects -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Active Research Projects</h2>
        </template>

        <div class="space-y-4">
          <div class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-beaker" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Machine Learning in Education</h3>
                <UBadge color="green" variant="soft">Active</UBadge>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                Investigating the application of ML algorithms in personalized learning systems.
              </p>
              <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                <span>Funding: $45,000</span>
                <span>Duration: 2 years</span>
                <span>Team: 4 members</span>
              </div>
            </div>
          </div>

          <div class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Data Analytics in Higher Education</h3>
                <UBadge color="blue" variant="soft">In Progress</UBadge>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                Analyzing student performance data to improve retention rates.
              </p>
              <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                <span>Funding: $80,000</span>
                <span>Duration: 3 years</span>
                <span>Team: 6 members</span>
              </div>
            </div>
          </div>

          <div class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h3 class="font-medium text-gray-900 dark:text-white">Curriculum Innovation Study</h3>
                <UBadge color="yellow" variant="soft">Planning</UBadge>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                Developing innovative curriculum approaches for computer science education.
              </p>
              <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                <span>Funding: Pending</span>
                <span>Duration: 1 year</span>
                <span>Team: 3 members</span>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Recent Publications -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Publications</h2>
        </template>

        <div class="space-y-4">
          <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 class="font-medium text-gray-900 dark:text-white">
              "Adaptive Learning Systems: A Comprehensive Review"
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
              Journal of Educational Technology Research, 2024
            </p>
            <div class="flex items-center space-x-2 mt-2">
              <UBadge color="blue" variant="soft" size="xs">Peer Reviewed</UBadge>
              <UBadge color="green" variant="soft" size="xs">Published</UBadge>
            </div>
          </div>

          <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 class="font-medium text-gray-900 dark:text-white">
              "Machine Learning Applications in Student Assessment"
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
              International Conference on Educational Data Mining, 2024
            </p>
            <div class="flex items-center space-x-2 mt-2">
              <UBadge color="purple" variant="soft" size="xs">Conference</UBadge>
              <UBadge color="green" variant="soft" size="xs">Accepted</UBadge>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'faculty'
})
</script>
