<template>
  <UContainer class="py-8">
    <div class="space-y-8">
    <!-- Hero Section -->
    <div class="text-center">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        College Management System
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 mb-8">
        Streamline your educational institution with our comprehensive management platform
      </p>

      <!-- Role Demo Buttons -->
      <div class="flex flex-wrap justify-center gap-4 mb-8">
        <UButton
          to="/admin"
          color="primary"
          size="lg"
          icon="i-heroicons-shield-check"
        >
          Admin Dashboard
        </UButton>
        <UButton
          to="/student"
          color="blue"
          size="lg"
          icon="i-heroicons-academic-cap"
        >
          Student Portal
        </UButton>
        <UButton
          to="/faculty"
          color="green"
          size="lg"
          icon="i-heroicons-user-group"
        >
          Faculty Portal
        </UButton>
      </div>

      <UButton
        to="/auth/login"
        variant="outline"
        size="lg"
      >
        Sign In
      </UButton>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-users" class="w-6 h-6 text-primary-600" />
            <h3 class="text-lg font-semibold">User Management</h3>
          </div>
        </template>
        <p class="text-gray-600 dark:text-gray-300">
          Comprehensive user management for students, faculty, and staff with role-based access control.
        </p>
      </UCard>

      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-academic-cap" class="w-6 h-6 text-blue-600" />
            <h3 class="text-lg font-semibold">Academic Management</h3>
          </div>
        </template>
        <p class="text-gray-600 dark:text-gray-300">
          Manage courses, programs, schedules, and academic requirements with ease.
        </p>
      </UCard>

      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-chart-bar" class="w-6 h-6 text-green-600" />
            <h3 class="text-lg font-semibold">Analytics & Reporting</h3>
          </div>
        </template>
        <p class="text-gray-600 dark:text-gray-300">
          Powerful analytics and reporting tools for data-driven decision making.
        </p>
      </UCard>

      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-currency-dollar" class="w-6 h-6 text-yellow-600" />
            <h3 class="text-lg font-semibold">Financial Management</h3>
          </div>
        </template>
        <p class="text-gray-600 dark:text-gray-300">
          Handle billing, payments, financial aid, and budget management efficiently.
        </p>
      </UCard>

      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-chat-bubble-left-right" class="w-6 h-6 text-purple-600" />
            <h3 class="text-lg font-semibold">Communication</h3>
          </div>
        </template>
        <p class="text-gray-600 dark:text-gray-300">
          Integrated messaging, announcements, and discussion forums for seamless communication.
        </p>
      </UCard>

      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-device-phone-mobile" class="w-6 h-6 text-red-600" />
            <h3 class="text-lg font-semibold">Mobile Responsive</h3>
          </div>
        </template>
        <p class="text-gray-600 dark:text-gray-300">
          Fully responsive design that works perfectly on all devices and screen sizes.
        </p>
      </UCard>
    </div>
  </UContainer>
</template>
