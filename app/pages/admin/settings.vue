<template>
  <div class="space-y-4">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">System Settings</h1>
        <p class="text-gray-600 dark:text-gray-300">Configure system preferences and settings</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="resetToDefaults"
        >
          Reset to Defaults
        </UButton>
        <UButton color="primary" icon="i-heroicons-check" @click="saveSettings" :loading="isSaving">
          Save Changes
        </UButton>
      </div>
    </div>

    <!-- Settings Navigation -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- Settings Menu -->
      <div class="lg:col-span-1">
        <UCard>
          <nav class="space-y-1">
            <button
              v-for="section in settingSections"
              :key="section.id"
              @click="activeSection = section.id"
              :class="[
                'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                activeSection === section.id
                  ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
              ]"
            >
              <div class="flex items-center">
                <UIcon :name="section.icon" class="w-4 h-4 mr-3" />
                {{ section.name }}
              </div>
            </button>
          </nav>
        </UCard>
      </div>

      <!-- Settings Content -->
      <div class="lg:col-span-3">
        <!-- General Settings -->
        <UCard v-if="activeSection === 'general'">
          <template #header>
            <h3 class="text-lg font-semibold">General Settings</h3>
          </template>
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Institution Name
              </label>
              <UInput v-model="settings.general.institutionName" placeholder="Enter institution name" />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Institution Logo
              </label>
              <div class="flex items-center space-x-4">
                <UAvatar :src="settings.general.logoUrl" size="lg" />
                <UButton variant="outline" icon="i-heroicons-photo">
                  Upload Logo
                </UButton>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Time Zone
              </label>
              <USelect v-model="settings.general.timezone" :options="timezoneOptions" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Default Language
              </label>
              <USelect v-model="settings.general.language" :options="languageOptions" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Academic Year Format
              </label>
              <USelect v-model="settings.general.academicYearFormat" :options="academicYearOptions" />
            </div>
          </div>
        </UCard>

        <!-- Security Settings -->
        <UCard v-if="activeSection === 'security'">
          <template #header>
            <h3 class="text-lg font-semibold">Security Settings</h3>
          </template>
          <div class="space-y-6">
            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Two-Factor Authentication</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Require 2FA for all admin accounts</p>
                </div>
                <UToggle v-model="settings.security.requireTwoFactor" />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Password Minimum Length
              </label>
              <UInput v-model="settings.security.passwordMinLength" type="number" min="6" max="32" />
            </div>

            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Password Complexity</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Require special characters and numbers</p>
                </div>
                <UToggle v-model="settings.security.passwordComplexity" />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Session Timeout (minutes)
              </label>
              <UInput v-model="settings.security.sessionTimeout" type="number" min="15" max="480" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Max Login Attempts
              </label>
              <UInput v-model="settings.security.maxLoginAttempts" type="number" min="3" max="10" />
            </div>
          </div>
        </UCard>

        <!-- Email Settings -->
        <UCard v-if="activeSection === 'email'">
          <template #header>
            <h3 class="text-lg font-semibold">Email Settings</h3>
          </template>
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SMTP Server
              </label>
              <UInput v-model="settings.email.smtpServer" placeholder="smtp.example.com" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SMTP Port
              </label>
              <UInput v-model="settings.email.smtpPort" type="number" placeholder="587" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                From Email Address
              </label>
              <UInput v-model="settings.email.fromEmail" type="email" placeholder="<EMAIL>" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                From Name
              </label>
              <UInput v-model="settings.email.fromName" placeholder="College Management System" />
            </div>

            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Email Notifications</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Send system notifications via email</p>
                </div>
                <UToggle v-model="settings.email.enableNotifications" />
              </div>
            </div>

            <div class="flex space-x-3">
              <UButton variant="outline" icon="i-heroicons-envelope">
                Test Email
              </UButton>
              <UButton variant="outline" icon="i-heroicons-cog-6-tooth">
                Advanced Settings
              </UButton>
            </div>
          </div>
        </UCard>

        <!-- Backup Settings -->
        <UCard v-if="activeSection === 'backup'">
          <template #header>
            <h3 class="text-lg font-semibold">Backup & Recovery</h3>
          </template>
          <div class="space-y-6">
            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Automatic Backups</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Enable scheduled automatic backups</p>
                </div>
                <UToggle v-model="settings.backup.enableAutoBackup" />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Backup Frequency
              </label>
              <USelect v-model="settings.backup.frequency" :options="backupFrequencyOptions" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Retention Period (days)
              </label>
              <UInput v-model="settings.backup.retentionDays" type="number" min="7" max="365" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Backup Location
              </label>
              <USelect v-model="settings.backup.location" :options="backupLocationOptions" />
            </div>

            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Last Backup</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">January 15, 2024 at 3:00 AM</p>
              <p class="text-xs text-green-600 dark:text-green-400 mt-1">✓ Backup completed successfully</p>
            </div>

            <div class="flex space-x-3">
              <UButton color="primary" icon="i-heroicons-cloud-arrow-up">
                Create Backup Now
              </UButton>
              <UButton variant="outline" icon="i-heroicons-cloud-arrow-down">
                Restore from Backup
              </UButton>
            </div>
          </div>
        </UCard>

        <!-- System Maintenance -->
        <UCard v-if="activeSection === 'maintenance'">
          <template #header>
            <h3 class="text-lg font-semibold">System Maintenance</h3>
          </template>
          <div class="space-y-6">
            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Maintenance Mode</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Put system in maintenance mode</p>
                </div>
                <UToggle v-model="settings.maintenance.maintenanceMode" />
              </div>
            </div>

            <div v-if="settings.maintenance.maintenanceMode">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Maintenance Message
              </label>
              <UTextarea 
                v-model="settings.maintenance.message" 
                placeholder="System is currently under maintenance. Please try again later."
                rows="3"
              />
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">Database Optimization</h4>
                <p class="text-xs text-blue-700 dark:text-blue-400 mb-3">Last run: 2 days ago</p>
                <UButton size="sm" variant="outline" color="blue">
                  Optimize Now
                </UButton>
              </div>

              <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <h4 class="text-sm font-medium text-green-900 dark:text-green-300 mb-2">Clear Cache</h4>
                <p class="text-xs text-green-700 dark:text-green-400 mb-3">Cache size: 245 MB</p>
                <UButton size="sm" variant="outline" color="green">
                  Clear Cache
                </UButton>
              </div>

              <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                <h4 class="text-sm font-medium text-yellow-900 dark:text-yellow-300 mb-2">System Logs</h4>
                <p class="text-xs text-yellow-700 dark:text-yellow-400 mb-3">Log size: 1.2 GB</p>
                <UButton size="sm" variant="outline" color="yellow">
                  View Logs
                </UButton>
              </div>

              <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                <h4 class="text-sm font-medium text-purple-900 dark:text-purple-300 mb-2">System Health</h4>
                <p class="text-xs text-purple-700 dark:text-purple-400 mb-3">Status: Healthy</p>
                <UButton size="sm" variant="outline" color="purple">
                  Run Diagnostics
                </UButton>
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// Reactive data
const isSaving = ref(false)
const activeSection = ref('general')

// Settings sections
const settingSections = [
  { id: 'general', name: 'General', icon: 'i-heroicons-cog-6-tooth' },
  { id: 'security', name: 'Security', icon: 'i-heroicons-shield-check' },
  { id: 'email', name: 'Email', icon: 'i-heroicons-envelope' },
  { id: 'backup', name: 'Backup', icon: 'i-heroicons-cloud-arrow-up' },
  { id: 'maintenance', name: 'Maintenance', icon: 'i-heroicons-wrench-screwdriver' }
]

// Settings data
const settings = ref({
  general: {
    institutionName: 'Springfield College',
    logoUrl: '/logo.png',
    timezone: 'America/New_York',
    language: 'en',
    academicYearFormat: 'fall-spring'
  },
  security: {
    requireTwoFactor: true,
    passwordMinLength: 8,
    passwordComplexity: true,
    sessionTimeout: 60,
    maxLoginAttempts: 5
  },
  email: {
    smtpServer: 'smtp.college.edu',
    smtpPort: 587,
    fromEmail: '<EMAIL>',
    fromName: 'College Management System',
    enableNotifications: true
  },
  backup: {
    enableAutoBackup: true,
    frequency: 'daily',
    retentionDays: 30,
    location: 'cloud'
  },
  maintenance: {
    maintenanceMode: false,
    message: 'System is currently under maintenance. Please try again later.'
  }
})

// Options
const timezoneOptions = [
  { label: 'Eastern Time (UTC-5)', value: 'America/New_York' },
  { label: 'Central Time (UTC-6)', value: 'America/Chicago' },
  { label: 'Mountain Time (UTC-7)', value: 'America/Denver' },
  { label: 'Pacific Time (UTC-8)', value: 'America/Los_Angeles' }
]

const languageOptions = [
  { label: 'English', value: 'en' },
  { label: 'Spanish', value: 'es' },
  { label: 'French', value: 'fr' },
  { label: 'German', value: 'de' }
]

const academicYearOptions = [
  { label: 'Fall-Spring', value: 'fall-spring' },
  { label: 'Spring-Fall', value: 'spring-fall' },
  { label: 'Calendar Year', value: 'calendar' }
]

const backupFrequencyOptions = [
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Monthly', value: 'monthly' }
]

const backupLocationOptions = [
  { label: 'Cloud Storage', value: 'cloud' },
  { label: 'Local Server', value: 'local' },
  { label: 'External Drive', value: 'external' }
]

// Methods
const saveSettings = async () => {
  isSaving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    useToast().add({
      title: 'Settings Saved',
      description: 'System settings have been updated successfully',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isSaving.value = false
  }
}

const resetToDefaults = () => {
  useToast().add({
    title: 'Reset to Defaults',
    description: 'Settings have been reset to default values',
    icon: 'i-heroicons-arrow-path'
  })
}
</script>
