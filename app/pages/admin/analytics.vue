<template>
  <UContainer class="py-6">
    <div class="space-y-4">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Analytics Dashboard</h1>
        <p class="text-gray-600 dark:text-gray-300">System performance and usage analytics</p>
      </div>
      <div class="flex items-center space-x-3">
        <USelect
          v-model="selectedPeriod"
          :options="periodOptions"
          @change="refreshData"
        />
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshData"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton color="primary" icon="i-heroicons-arrow-down-tray">
          Export Report
        </UButton>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ metrics.totalUsers }}</p>
            <p class="text-xs text-green-600 dark:text-green-400">+12% from last month</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Courses</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ metrics.activeCourses }}</p>
            <p class="text-xs text-green-600 dark:text-green-400">+8% from last month</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg. Grade</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ metrics.averageGrade }}</p>
            <p class="text-xs text-red-600 dark:text-red-400">-2% from last month</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-clock" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg. Session</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ metrics.averageSession }}</p>
            <p class="text-xs text-green-600 dark:text-green-400">+5% from last month</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <!-- User Activity Chart -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">User Activity</h3>
            <UButton variant="ghost" size="sm" icon="i-heroicons-ellipsis-horizontal">
              Options
            </UButton>
          </div>
        </template>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="text-center">
            <UIcon name="i-heroicons-chart-bar" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 dark:text-gray-400">Chart placeholder</p>
            <p class="text-sm text-gray-400">User activity over time</p>
          </div>
        </div>
      </UCard>

      <!-- Course Enrollment Chart -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">Course Enrollment</h3>
            <UButton variant="ghost" size="sm" icon="i-heroicons-ellipsis-horizontal">
              Options
            </UButton>
          </div>
        </template>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="text-center">
            <UIcon name="i-heroicons-chart-pie" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 dark:text-gray-400">Chart placeholder</p>
            <p class="text-sm text-gray-400">Enrollment by department</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- System Performance -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">System Performance</h3>
        </template>
        <div class="space-y-4">
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-400">CPU Usage</span>
              <span class="font-medium">{{ systemMetrics.cpu }}%</span>
            </div>
            <UProgress :value="systemMetrics.cpu" color="blue" class="mt-1" />
          </div>
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-400">Memory Usage</span>
              <span class="font-medium">{{ systemMetrics.memory }}%</span>
            </div>
            <UProgress :value="systemMetrics.memory" color="green" class="mt-1" />
          </div>
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-400">Storage Usage</span>
              <span class="font-medium">{{ systemMetrics.storage }}%</span>
            </div>
            <UProgress :value="systemMetrics.storage" color="yellow" class="mt-1" />
          </div>
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-400">Network I/O</span>
              <span class="font-medium">{{ systemMetrics.network }}%</span>
            </div>
            <UProgress :value="systemMetrics.network" color="purple" class="mt-1" />
          </div>
        </div>
      </UCard>

      <!-- Top Courses -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Top Courses</h3>
        </template>
        <div class="space-y-3">
          <div v-for="course in topCourses" :key="course.id" class="flex items-center justify-between">
            <div>
              <p class="font-medium text-gray-900 dark:text-white">{{ course.name }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ course.students }} students</p>
            </div>
            <div class="text-right">
              <div class="flex items-center">
                <UIcon name="i-heroicons-star" class="w-4 h-4 text-yellow-400 mr-1" />
                <span class="text-sm font-medium">{{ course.rating }}</span>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Recent Activity -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Recent Activity</h3>
        </template>
        <div class="space-y-3">
          <div v-for="activity in recentActivity" :key="activity.id" class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <div :class="getActivityColor(activity.type)" class="w-8 h-8 rounded-full flex items-center justify-center">
                <UIcon :name="getActivityIcon(activity.type)" class="w-4 h-4 text-white" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ formatTimeAgo(activity.timestamp) }}</p>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Detailed Analytics Table -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">Detailed Analytics</h3>
          <div class="flex items-center space-x-2">
            <UInput
              v-model="searchQuery"
              placeholder="Search..."
              icon="i-heroicons-magnifying-glass"
              size="sm"
            />
            <UButton variant="ghost" size="sm" icon="i-heroicons-funnel">
              Filter
            </UButton>
          </div>
        </div>
      </template>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Metric
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Current Value
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Previous Period
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Change
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Trend
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="metric in detailedMetrics" :key="metric.name" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                {{ metric.name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ metric.current }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ metric.previous }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                <span :class="metric.change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ metric.change >= 0 ? '+' : '' }}{{ metric.change }}%
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UIcon 
                  :name="metric.change >= 0 ? 'i-heroicons-arrow-trending-up' : 'i-heroicons-arrow-trending-down'"
                  :class="metric.change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'"
                  class="w-5 h-5"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </UCard>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// Reactive data
const isRefreshing = ref(false)
const selectedPeriod = ref('last-30-days')
const searchQuery = ref('')

// Period options
const periodOptions = [
  { label: 'Last 7 days', value: 'last-7-days' },
  { label: 'Last 30 days', value: 'last-30-days' },
  { label: 'Last 3 months', value: 'last-3-months' },
  { label: 'Last year', value: 'last-year' }
]

// Mock data
const metrics = ref({
  totalUsers: '2,847',
  activeCourses: '156',
  averageGrade: '85.2',
  averageSession: '24m'
})

const systemMetrics = ref({
  cpu: 45,
  memory: 62,
  storage: 78,
  network: 23
})

const topCourses = ref([
  { id: 1, name: 'CS 101', students: 45, rating: 4.8 },
  { id: 2, name: 'MATH 201', students: 38, rating: 4.6 },
  { id: 3, name: 'ENG 102', students: 42, rating: 4.5 },
  { id: 4, name: 'PHYS 101', students: 35, rating: 4.3 },
  { id: 5, name: 'CHEM 101', students: 40, rating: 4.2 }
])

const recentActivity = ref([
  {
    id: 1,
    type: 'user',
    description: 'New user registration: John Smith',
    timestamp: new Date(Date.now() - 5 * 60 * 1000)
  },
  {
    id: 2,
    type: 'course',
    description: 'Course "Advanced Mathematics" was updated',
    timestamp: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: 3,
    type: 'system',
    description: 'System backup completed successfully',
    timestamp: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: 4,
    type: 'grade',
    description: 'Grades published for CS 101',
    timestamp: new Date(Date.now() - 45 * 60 * 1000)
  }
])

const detailedMetrics = ref([
  { name: 'Page Views', current: '45,231', previous: '42,156', change: 7.3 },
  { name: 'Unique Visitors', current: '12,847', previous: '11,923', change: 7.8 },
  { name: 'Session Duration', current: '24m 15s', previous: '22m 45s', change: 6.6 },
  { name: 'Bounce Rate', current: '32.4%', previous: '35.1%', change: -7.7 },
  { name: 'Course Completions', current: '1,234', previous: '1,156', change: 6.7 },
  { name: 'Assignment Submissions', current: '8,945', previous: '8,234', change: 8.6 }
])

// Methods
const refreshData = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    useToast().add({
      title: 'Data Refreshed',
      description: 'Analytics data has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const getActivityColor = (type) => {
  const colors = {
    user: 'bg-blue-500',
    course: 'bg-green-500',
    system: 'bg-purple-500',
    grade: 'bg-yellow-500'
  }
  return colors[type] || 'bg-gray-500'
}

const getActivityIcon = (type) => {
  const icons = {
    user: 'i-heroicons-user-plus',
    course: 'i-heroicons-academic-cap',
    system: 'i-heroicons-cog-6-tooth',
    grade: 'i-heroicons-chart-bar-square'
  }
  return icons[type] || 'i-heroicons-information-circle'
}

const formatTimeAgo = (date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
  return `${Math.floor(diffInMinutes / 1440)}d ago`
}

// Initialize
onMounted(() => {
  refreshData()
})
</script>
