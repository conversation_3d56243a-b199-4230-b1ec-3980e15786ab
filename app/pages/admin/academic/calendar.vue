<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Academic Calendar</h1>
          <p class="text-gray-600 dark:text-gray-300">Manage academic calendar, semesters, and important dates</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showEventModal = true"
          >
            Add Event
          </UButton>
        </div>
      </div>

      <!-- Calendar Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-calendar-days" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Events</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ calendarStats.totalEvents }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-clock" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Upcoming Events</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ calendarStats.upcomingEvents }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Deadlines</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ calendarStats.deadlines }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-sun" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Holidays</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ calendarStats.holidays }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Calendar View Toggle -->
      <UCard>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Calendar View</h2>
            <div class="flex items-center space-x-2">
              <UButton
                :variant="viewMode === 'month' ? 'solid' : 'outline'"
                size="sm"
                @click="viewMode = 'month'"
              >
                Month
              </UButton>
              <UButton
                :variant="viewMode === 'list' ? 'solid' : 'outline'"
                size="sm"
                @click="viewMode = 'list'"
              >
                List
              </UButton>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <USelect
              v-model="selectedSemester"
              :options="semesterOptions"
              size="sm"
            />
            <USelect
              v-model="selectedYear"
              :options="yearOptions"
              size="sm"
            />
          </div>
        </div>
      </UCard>

      <!-- Calendar Content -->
      <div v-if="viewMode === 'month'">
        <!-- Month Calendar View -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ currentMonthYear }}
              </h3>
              <div class="flex items-center space-x-2">
                <UButton
                  size="sm"
                  variant="outline"
                  icon="i-heroicons-chevron-left"
                  @click="previousMonth"
                />
                <UButton
                  size="sm"
                  variant="outline"
                  @click="goToToday"
                >
                  Today
                </UButton>
                <UButton
                  size="sm"
                  variant="outline"
                  icon="i-heroicons-chevron-right"
                  @click="nextMonth"
                />
              </div>
            </div>
          </template>

          <!-- Calendar Grid -->
          <div class="grid grid-cols-7 gap-1">
            <!-- Day Headers -->
            <div
              v-for="day in dayHeaders"
              :key="day"
              class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400"
            >
              {{ day }}
            </div>

            <!-- Calendar Days -->
            <div
              v-for="day in calendarDays"
              :key="day.date"
              class="min-h-24 p-1 border border-gray-200 dark:border-gray-700 rounded"
              :class="{
                'bg-gray-50 dark:bg-gray-800': !day.isCurrentMonth,
                'bg-blue-50 dark:bg-blue-900/20': day.isToday
              }"
            >
              <div class="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {{ day.day }}
              </div>
              <div class="space-y-1">
                <div
                  v-for="event in day.events"
                  :key="event.id"
                  class="text-xs p-1 rounded truncate cursor-pointer"
                  :class="getEventClasses(event.type)"
                  @click="viewEvent(event.id)"
                >
                  {{ event.title }}
                </div>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <div v-else>
        <!-- List View -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Events List</h3>
              <div class="flex items-center space-x-2">
                <USelect
                  v-model="eventTypeFilter"
                  :options="eventTypeOptions"
                  placeholder="All Types"
                  size="sm"
                />
              </div>
            </div>
          </template>

          <div v-if="loading" class="space-y-4">
            <USkeleton v-for="i in 5" :key="i" class="h-16" />
          </div>

          <div v-else-if="filteredEvents.length === 0" class="text-center py-12">
            <UIcon name="i-heroicons-calendar-days" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Events Found</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              No events found for the selected criteria.
            </p>
            <UButton color="primary" @click="showEventModal = true">
              Add Event
            </UButton>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="event in filteredEvents"
              :key="event.id"
              class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
              @click="viewEvent(event.id)"
            >
              <div class="flex-shrink-0">
                <div
                  class="w-10 h-10 rounded-lg flex items-center justify-center"
                  :class="getEventIconClasses(event.type)"
                >
                  <UIcon :name="getEventIcon(event.type)" class="w-5 h-5" />
                </div>
              </div>
              
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <h4 class="font-medium text-gray-900 dark:text-white truncate">{{ event.title }}</h4>
                  <UBadge
                    :color="getEventTypeColor(event.type)"
                    variant="soft"
                    size="xs"
                  >
                    {{ event.type }}
                  </UBadge>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ event.description }}</p>
                <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                  <span>{{ formatEventDate(event) }}</span>
                  <span v-if="event.location">{{ event.location }}</span>
                  <span>{{ event.semester }} {{ event.year }}</span>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-pencil"
                  @click.stop="editEvent(event.id)"
                />
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-trash"
                  class="text-red-600 dark:text-red-400"
                  @click.stop="deleteEvent(event.id)"
                />
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- Add Event Modal -->
    <UModal v-model="showEventModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Calendar Event</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newEvent.title"
            label="Event Title"
            placeholder="Enter event title"
            required
          />

          <UTextarea
            v-model="newEvent.description"
            label="Description"
            placeholder="Enter event description..."
            :rows="3"
          />

          <USelect
            v-model="newEvent.type"
            label="Event Type"
            :options="eventTypeOptions"
            required
          />

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newEvent.startDate"
              label="Start Date"
              type="datetime-local"
              required
            />
            <UInput
              v-model="newEvent.endDate"
              label="End Date"
              type="datetime-local"
              required
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newEvent.semester"
              label="Semester"
              placeholder="Fall, Spring, Summer"
              required
            />
            <UInput
              v-model.number="newEvent.year"
              label="Year"
              type="number"
              required
            />
          </div>

          <UInput
            v-model="newEvent.location"
            label="Location (Optional)"
            placeholder="Enter location"
          />

          <div class="flex items-center space-x-2">
            <UCheckbox
              v-model="newEvent.isAllDay"
              label="All Day Event"
            />
            <UCheckbox
              v-model="newEvent.isRecurring"
              label="Recurring Event"
            />
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showEventModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addEvent" :loading="adding">
              Add Event
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { calendar, loading } = useAcademicData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Academic Calendar'
})

// Reactive state
const showEventModal = ref(false)
const adding = ref(false)
const viewMode = ref('month')
const selectedSemester = ref('Fall')
const selectedYear = ref(2024)
const eventTypeFilter = ref('')
const currentDate = ref(new Date())

// New event form
const newEvent = ref({
  title: '',
  description: '',
  type: 'event',
  startDate: '',
  endDate: '',
  semester: 'Fall',
  year: 2024,
  location: '',
  isAllDay: false,
  isRecurring: false,
  color: 'blue'
})

// Computed properties
const calendarStats = computed(() => {
  const totalEvents = calendar.value.length
  const upcomingEvents = calendar.value.filter(e => new Date(e.startDate) > new Date()).length
  const deadlines = calendar.value.filter(e => e.type === 'deadline').length
  const holidays = calendar.value.filter(e => e.type === 'holiday').length

  return { totalEvents, upcomingEvents, deadlines, holidays }
})

const filteredEvents = computed(() => {
  return calendar.value.filter(event => {
    const matchesType = !eventTypeFilter.value || event.type === eventTypeFilter.value
    const matchesSemester = event.semester === selectedSemester.value
    const matchesYear = event.year === selectedYear.value
    
    return matchesType && matchesSemester && matchesYear
  }).sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime())
})

const currentMonthYear = computed(() => {
  return currentDate.value.toLocaleDateString('en-US', { 
    month: 'long', 
    year: 'numeric' 
  })
})

const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  
  const days = []
  const today = new Date()
  
  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    
    const dayEvents = calendar.value.filter(event => {
      const eventDate = new Date(event.startDate)
      return eventDate.toDateString() === date.toDateString()
    })
    
    days.push({
      date: date.toISOString(),
      day: date.getDate(),
      isCurrentMonth: date.getMonth() === month,
      isToday: date.toDateString() === today.toDateString(),
      events: dayEvents
    })
  }
  
  return days
})

// Options for dropdowns
const semesterOptions = [
  { label: 'Fall', value: 'Fall' },
  { label: 'Spring', value: 'Spring' },
  { label: 'Summer', value: 'Summer' }
]

const yearOptions = [
  { label: '2023', value: 2023 },
  { label: '2024', value: 2024 },
  { label: '2025', value: 2025 }
]

const eventTypeOptions = [
  { label: 'Semester', value: 'semester' },
  { label: 'Break', value: 'break' },
  { label: 'Deadline', value: 'deadline' },
  { label: 'Event', value: 'event' },
  { label: 'Holiday', value: 'holiday' }
]

// Helper functions
const getEventClasses = (type: string) => {
  const classes = {
    'semester': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
    'break': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
    'deadline': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
    'event': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
    'holiday': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
  }
  return classes[type] || classes.event
}

const getEventIconClasses = (type: string) => {
  const classes = {
    'semester': 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
    'break': 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400',
    'deadline': 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
    'event': 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400',
    'holiday': 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'
  }
  return classes[type] || classes.event
}

const getEventIcon = (type: string) => {
  const icons = {
    'semester': 'i-heroicons-academic-cap',
    'break': 'i-heroicons-sun',
    'deadline': 'i-heroicons-exclamation-triangle',
    'event': 'i-heroicons-calendar-days',
    'holiday': 'i-heroicons-gift'
  }
  return icons[type] || icons.event
}

const getEventTypeColor = (type: string) => {
  const colors = {
    'semester': 'blue',
    'break': 'orange',
    'deadline': 'red',
    'event': 'green',
    'holiday': 'purple'
  }
  return colors[type] || 'green'
}

const formatEventDate = (event: any) => {
  const start = new Date(event.startDate)
  const end = new Date(event.endDate)
  
  if (event.isAllDay) {
    if (start.toDateString() === end.toDateString()) {
      return start.toLocaleDateString()
    } else {
      return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`
    }
  } else {
    return `${start.toLocaleDateString()} ${start.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
  }
}

// Methods
const refreshData = () => {
  console.log('Refreshing calendar data...')
}

const previousMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

const nextMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

const goToToday = () => {
  currentDate.value = new Date()
}

const viewEvent = (eventId: string) => {
  console.log('View event:', eventId)
}

const editEvent = (eventId: string) => {
  console.log('Edit event:', eventId)
}

const deleteEvent = (eventId: string) => {
  if (confirm('Are you sure you want to delete this event?')) {
    console.log('Delete event:', eventId)
  }
}

const addEvent = () => {
  console.log('Add event:', newEvent.value)
  showEventModal.value = false
  // Reset form
  Object.assign(newEvent.value, {
    title: '',
    description: '',
    type: 'event',
    startDate: '',
    endDate: '',
    semester: 'Fall',
    year: 2024,
    location: '',
    isAllDay: false,
    isRecurring: false,
    color: 'blue'
  })
}
</script>
