<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Registration Management</h1>
          <p class="text-gray-600 dark:text-gray-300">Oversee student course registrations and enrollment</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showRegistrationModal = true"
          >
            Manual Registration
          </UButton>
        </div>
      </div>

      <!-- Registration Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-users" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Registrations</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ registrationStats.total }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-check-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Enrolled</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ registrationStats.enrolled }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-queue-list" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Waitlisted</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ registrationStats.waitlisted }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-600 dark:text-red-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Payment Pending</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ registrationStats.paymentPending }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Search and Filters -->
      <UCard>
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Search Input -->
            <div class="lg:col-span-2">
              <UInput
                v-model="filters.search"
                placeholder="Search by student name, email, or course..."
                icon="i-heroicons-magnifying-glass"
                size="sm"
              />
            </div>

            <!-- Status Filter -->
            <USelect
              v-model="filters.status"
              :options="statusOptions"
              placeholder="All Status"
              size="sm"
            />

            <!-- Payment Status Filter -->
            <USelect
              v-model="filters.paymentStatus"
              :options="paymentStatusOptions"
              placeholder="All Payment Status"
              size="sm"
            />
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-300">
              Showing {{ filteredRegistrations.length }} of {{ registrations.length }} registrations
            </span>
            <UButton
              variant="ghost"
              size="sm"
              @click="clearFilters"
              v-if="hasActiveFilters"
            >
              Clear Filters
            </UButton>
          </div>
        </div>
      </UCard>

      <!-- Registrations Table -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Student Registrations</h2>
        </template>

        <div v-if="loading" class="space-y-4">
          <USkeleton v-for="i in 5" :key="i" class="h-16" />
        </div>

        <div v-else-if="filteredRegistrations.length === 0" class="text-center py-12">
          <UIcon name="i-heroicons-clipboard-document-list" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Registrations Found</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            {{ hasActiveFilters ? 'Try adjusting your search criteria.' : 'No student registrations found.' }}
          </p>
        </div>

        <UTable
          v-else
          :rows="paginatedRegistrations"
          :columns="registrationColumns"
          :loading="loading"
        >
          <template #student-data="{ row }">
            <div class="flex items-center space-x-3">
              <UAvatar
                :alt="row.studentName"
                size="sm"
              />
              <div>
                <div class="font-medium text-gray-900 dark:text-white">{{ row.studentName }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.studentEmail }}</div>
              </div>
            </div>
          </template>

          <template #course-data="{ row }">
            <div>
              <div class="font-medium text-gray-900 dark:text-white">{{ row.courseCode }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.courseName }}</div>
            </div>
          </template>

          <template #status-data="{ row }">
            <UBadge
              :color="getStatusColor(row.status)"
              variant="soft"
            >
              {{ row.status }}
            </UBadge>
          </template>

          <template #paymentStatus-data="{ row }">
            <UBadge
              :color="getPaymentStatusColor(row.paymentStatus)"
              variant="soft"
            >
              {{ row.paymentStatus }}
            </UBadge>
          </template>

          <template #registrationDate-data="{ row }">
            <div class="text-sm">
              {{ formatDate(row.registrationDate) }}
            </div>
          </template>

          <template #actions-data="{ row }">
            <div class="flex items-center space-x-2">
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-eye"
                @click="viewRegistration(row.id)"
              />
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-pencil"
                @click="editRegistration(row.id)"
              />
              <UButton
                v-if="row.status === 'waitlisted'"
                size="xs"
                variant="ghost"
                icon="i-heroicons-arrow-up"
                class="text-green-600 dark:text-green-400"
                @click="promoteFromWaitlist(row.id)"
              />
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-trash"
                class="text-red-600 dark:text-red-400"
                @click="cancelRegistration(row.id)"
              />
            </div>
          </template>
        </UTable>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-center mt-6">
          <UPagination
            v-model="currentPage"
            :page-count="itemsPerPage"
            :total="filteredRegistrations.length"
            :max="5"
          />
        </div>
      </UCard>

      <!-- Registration Periods -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Registration Periods</h2>
            <UButton
              color="primary"
              size="sm"
              icon="i-heroicons-plus"
              @click="showPeriodModal = true"
            >
              Add Period
            </UButton>
          </div>
        </template>

        <div class="space-y-4">
          <div
            v-for="period in registrationPeriods"
            :key="period.id"
            class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
          >
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">{{ period.name }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">
                {{ formatDate(period.startDate) }} - {{ formatDate(period.endDate) }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ period.semester }} {{ period.year }} • Priority {{ period.priority }}
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <UBadge
                :color="getPeriodStatusColor(period.status)"
                variant="soft"
              >
                {{ period.status }}
              </UBadge>
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-pencil"
                @click="editPeriod(period.id)"
              />
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Manual Registration Modal -->
    <UModal v-model="showRegistrationModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Manual Registration</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newRegistration.studentId"
            label="Student ID"
            placeholder="Enter student ID"
            required
          />
          
          <UInput
            v-model="newRegistration.courseId"
            label="Course ID"
            placeholder="Enter course ID"
            required
          />

          <USelect
            v-model="newRegistration.status"
            label="Registration Status"
            :options="statusOptions"
            required
          />

          <USelect
            v-model="newRegistration.paymentStatus"
            label="Payment Status"
            :options="paymentStatusOptions"
            required
          />
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showRegistrationModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addRegistration" :loading="adding">
              Register Student
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Registration Period Modal -->
    <UModal v-model="showPeriodModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Registration Period</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newPeriod.name"
            label="Period Name"
            placeholder="e.g., Fall 2024 - Priority Registration"
            required
          />
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newPeriod.startDate"
              label="Start Date"
              type="datetime-local"
              required
            />
            <UInput
              v-model="newPeriod.endDate"
              label="End Date"
              type="datetime-local"
              required
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newPeriod.semester"
              label="Semester"
              placeholder="Fall, Spring, Summer"
              required
            />
            <UInput
              v-model.number="newPeriod.year"
              label="Year"
              type="number"
              required
            />
          </div>

          <UInput
            v-model.number="newPeriod.priority"
            label="Priority Level"
            type="number"
            min="1"
            max="10"
            required
          />
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showPeriodModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addPeriod" :loading="adding">
              Add Period
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { registrations, loading } = useAcademicData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Registration Management'
})

// Reactive state
const showRegistrationModal = ref(false)
const showPeriodModal = ref(false)
const adding = ref(false)
const currentPage = ref(1)
const itemsPerPage = 10

// Filters
const filters = ref({
  search: '',
  status: '',
  paymentStatus: ''
})

// Mock registration periods
const registrationPeriods = ref([
  {
    id: '1',
    name: 'Fall 2024 - Priority Registration',
    semester: 'Fall',
    year: 2024,
    startDate: '2024-08-01T00:00:00Z',
    endDate: '2024-08-15T23:59:59Z',
    status: 'closed',
    priority: 1
  },
  {
    id: '2',
    name: 'Fall 2024 - General Registration',
    semester: 'Fall',
    year: 2024,
    startDate: '2024-08-16T00:00:00Z',
    endDate: '2024-08-25T23:59:59Z',
    status: 'active',
    priority: 2
  }
])

// New registration form
const newRegistration = ref({
  studentId: '',
  courseId: '',
  status: 'enrolled',
  paymentStatus: 'pending'
})

// New period form
const newPeriod = ref({
  name: '',
  semester: '',
  year: new Date().getFullYear(),
  startDate: '',
  endDate: '',
  priority: 1
})

// Table columns
const registrationColumns = [
  { key: 'student', label: 'Student', sortable: true },
  { key: 'course', label: 'Course', sortable: true },
  { key: 'credits', label: 'Credits', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'paymentStatus', label: 'Payment', sortable: true },
  { key: 'registrationDate', label: 'Registered', sortable: true },
  { key: 'actions', label: 'Actions' }
]

// Computed properties
const registrationStats = computed(() => {
  const total = registrations.value.length
  const enrolled = registrations.value.filter(r => r.status === 'enrolled').length
  const waitlisted = registrations.value.filter(r => r.status === 'waitlisted').length
  const paymentPending = registrations.value.filter(r => r.paymentStatus === 'pending').length

  return { total, enrolled, waitlisted, paymentPending }
})

const filteredRegistrations = computed(() => {
  return registrations.value.filter(registration => {
    const matchesSearch = !filters.value.search || 
      registration.studentName.toLowerCase().includes(filters.value.search.toLowerCase()) ||
      registration.studentEmail.toLowerCase().includes(filters.value.search.toLowerCase()) ||
      registration.courseCode.toLowerCase().includes(filters.value.search.toLowerCase()) ||
      registration.courseName.toLowerCase().includes(filters.value.search.toLowerCase())

    const matchesStatus = !filters.value.status || registration.status === filters.value.status
    const matchesPaymentStatus = !filters.value.paymentStatus || registration.paymentStatus === filters.value.paymentStatus

    return matchesSearch && matchesStatus && matchesPaymentStatus
  })
})

const paginatedRegistrations = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredRegistrations.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredRegistrations.value.length / itemsPerPage)
})

const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(filter => filter !== '')
})

// Options for dropdowns
const statusOptions = [
  { label: 'Enrolled', value: 'enrolled' },
  { label: 'Waitlisted', value: 'waitlisted' },
  { label: 'Dropped', value: 'dropped' },
  { label: 'Completed', value: 'completed' }
]

const paymentStatusOptions = [
  { label: 'Paid', value: 'paid' },
  { label: 'Pending', value: 'pending' },
  { label: 'Overdue', value: 'overdue' }
]

// Helper functions
const getStatusColor = (status: string) => {
  const colors = {
    'enrolled': 'green',
    'waitlisted': 'orange',
    'dropped': 'red',
    'completed': 'blue'
  }
  return colors[status] || 'gray'
}

const getPaymentStatusColor = (status: string) => {
  const colors = {
    'paid': 'green',
    'pending': 'yellow',
    'overdue': 'red'
  }
  return colors[status] || 'gray'
}

const getPeriodStatusColor = (status: string) => {
  const colors = {
    'upcoming': 'blue',
    'active': 'green',
    'closed': 'gray'
  }
  return colors[status] || 'gray'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Methods
const refreshData = () => {
  console.log('Refreshing registration data...')
}

const clearFilters = () => {
  filters.value = {
    search: '',
    status: '',
    paymentStatus: ''
  }
  currentPage.value = 1
}

const viewRegistration = (registrationId: string) => {
  console.log('View registration:', registrationId)
}

const editRegistration = (registrationId: string) => {
  console.log('Edit registration:', registrationId)
}

const promoteFromWaitlist = (registrationId: string) => {
  console.log('Promote from waitlist:', registrationId)
}

const cancelRegistration = (registrationId: string) => {
  if (confirm('Are you sure you want to cancel this registration?')) {
    console.log('Cancel registration:', registrationId)
  }
}

const addRegistration = () => {
  console.log('Add registration:', newRegistration.value)
  showRegistrationModal.value = false
}

const addPeriod = () => {
  console.log('Add period:', newPeriod.value)
  showPeriodModal.value = false
}

const editPeriod = (periodId: string) => {
  console.log('Edit period:', periodId)
}

// Watch for filter changes to reset pagination
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })
</script>
