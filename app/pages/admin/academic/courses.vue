<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Course Management</h1>
          <p class="text-gray-600 dark:text-gray-300">Manage course catalog, schedules, and enrollments</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showAddModal = true"
          >
            Add Course
          </UButton>
        </div>
      </div>

      <!-- Academic Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Courses</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ academicStats.totalCourses }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-users" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Enrollments</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ academicStats.totalEnrollments }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Enrollment</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ academicStats.averageEnrollment }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-queue-list" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Waitlisted</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ academicStats.waitlistTotal }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Search and Filters -->
      <UCard>
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Search Input -->
            <div class="lg:col-span-2">
              <UInput
                v-model="filters.search"
                placeholder="Search courses by code, title, or department..."
                icon="i-heroicons-magnifying-glass"
                size="sm"
              />
            </div>

            <!-- Department Filter -->
            <USelect
              v-model="filters.department"
              :options="departmentOptions"
              placeholder="All Departments"
              size="sm"
            />

            <!-- Status Filter -->
            <USelect
              v-model="filters.status"
              :options="statusOptions"
              placeholder="All Status"
              size="sm"
            />
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-300">
              Showing {{ filteredCourses.length }} of {{ courses.length }} courses
            </span>
            <UButton
              variant="ghost"
              size="sm"
              @click="clearFilters"
              v-if="hasActiveFilters"
            >
              Clear Filters
            </UButton>
          </div>
        </div>
      </UCard>

      <!-- Courses Table -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Course Catalog</h2>
        </template>

        <div v-if="loading" class="space-y-4">
          <USkeleton v-for="i in 5" :key="i" class="h-16" />
        </div>

        <div v-else-if="filteredCourses.length === 0" class="text-center py-12">
          <UIcon name="i-heroicons-academic-cap" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Courses Found</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            {{ hasActiveFilters ? 'Try adjusting your search criteria.' : 'Get started by adding your first course.' }}
          </p>
          <UButton v-if="!hasActiveFilters" color="primary" @click="showAddModal = true">
            Add Course
          </UButton>
        </div>

        <UTable
          v-else
          :rows="paginatedCourses"
          :columns="courseColumns"
          :loading="loading"
        >
          <template #code-data="{ row }">
            <div class="flex items-center space-x-2">
              <span class="font-medium text-gray-900 dark:text-white">{{ row.code }}</span>
              <UBadge
                :color="getStatusColor(row.status)"
                variant="soft"
                size="xs"
              >
                {{ row.status }}
              </UBadge>
            </div>
          </template>

          <template #enrollment-data="{ row }">
            <div class="space-y-1">
              <div class="flex items-center justify-between text-sm">
                <span>{{ row.currentEnrollment }}/{{ row.maxEnrollment }}</span>
                <span class="text-gray-500">{{ Math.round((row.currentEnrollment / row.maxEnrollment) * 100) }}%</span>
              </div>
              <UProgress
                :value="(row.currentEnrollment / row.maxEnrollment) * 100"
                :color="getEnrollmentColor(row.currentEnrollment, row.maxEnrollment)"
                size="xs"
              />
              <div v-if="row.waitlistCount > 0" class="text-xs text-orange-600 dark:text-orange-400">
                {{ row.waitlistCount }} waitlisted
              </div>
            </div>
          </template>

          <template #schedule-data="{ row }">
            <div class="text-sm">
              <div class="font-medium">{{ row.schedule.days.join(', ') }}</div>
              <div class="text-gray-500">{{ row.schedule.time }}</div>
              <div class="text-gray-500">{{ row.schedule.room }}, {{ row.schedule.building }}</div>
            </div>
          </template>

          <template #actions-data="{ row }">
            <div class="flex items-center space-x-2">
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-eye"
                @click="viewCourse(row.id)"
              />
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-pencil"
                @click="editCourse(row.id)"
              />
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-trash"
                class="text-red-600 dark:text-red-400"
                @click="deleteCourse(row.id)"
              />
            </div>
          </template>
        </UTable>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-center mt-6">
          <UPagination
            v-model="currentPage"
            :page-count="itemsPerPage"
            :total="filteredCourses.length"
            :max="5"
          />
        </div>
      </UCard>
    </div>

    <!-- Add Course Modal -->
    <UModal v-model="showAddModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add New Course</h3>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newCourse.code"
              label="Course Code"
              placeholder="e.g., CS 101"
              required
            />
            <UInput
              v-model="newCourse.title"
              label="Course Title"
              placeholder="e.g., Introduction to Computer Science"
              required
            />
          </div>

          <UTextarea
            v-model="newCourse.description"
            label="Description"
            placeholder="Enter course description..."
            :rows="3"
          />

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <UInput
              v-model.number="newCourse.credits"
              label="Credits"
              type="number"
              min="1"
              max="6"
              required
            />
            <USelect
              v-model="newCourse.department"
              label="Department"
              :options="departmentOptions"
              placeholder="Select department"
              required
            />
            <UInput
              v-model.number="newCourse.maxEnrollment"
              label="Max Enrollment"
              type="number"
              min="1"
              required
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model.number="newCourse.tuition"
              label="Tuition"
              type="number"
              min="0"
              step="0.01"
            />
            <UInput
              v-model.number="newCourse.fees"
              label="Fees"
              type="number"
              min="0"
              step="0.01"
            />
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showAddModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addNewCourse" :loading="adding">
              Add Course
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { courses, loading, academicStats, searchCourses, addCourse, deleteCourse: removeCourse } = useAcademicData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Course Management'
})

// Reactive state
const showAddModal = ref(false)
const adding = ref(false)
const currentPage = ref(1)
const itemsPerPage = 10

// Filters
const filters = ref({
  search: '',
  department: '',
  status: ''
})

// New course form
const newCourse = ref({
  code: '',
  title: '',
  description: '',
  credits: 3,
  department: '',
  departmentId: '',
  instructorIds: [],
  instructors: [],
  prerequisites: [],
  maxEnrollment: 30,
  currentEnrollment: 0,
  waitlistCount: 0,
  schedule: {
    days: [],
    time: '',
    room: '',
    building: ''
  },
  semester: 'Fall',
  year: 2024,
  status: 'active' as const,
  tuition: 1200,
  fees: 50
})

// Table columns
const courseColumns = [
  { key: 'code', label: 'Course Code' },
  { key: 'title', label: 'Title' },
  { key: 'department', label: 'Department' },
  { key: 'credits', label: 'Credits' },
  { key: 'enrollment', label: 'Enrollment' },
  { key: 'schedule', label: 'Schedule' },
  { key: 'actions', label: 'Actions' }
]

// Computed properties
const filteredCourses = computed(() => {
  return searchCourses(filters.value.search, filters.value)
})

const paginatedCourses = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredCourses.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredCourses.value.length / itemsPerPage)
})

const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(filter => filter !== '')
})

// Options for dropdowns
const departmentOptions = computed(() => {
  const departments = [...new Set(courses.value.map(c => c.department))]
  return departments.map(dept => ({ label: dept, value: dept }))
})

const statusOptions = [
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
  { label: 'Archived', value: 'archived' },
  { label: 'Cancelled', value: 'cancelled' }
]

// Helper functions
const getStatusColor = (status: string) => {
  const colors = {
    'active': 'green',
    'inactive': 'yellow',
    'archived': 'gray',
    'cancelled': 'red'
  }
  return colors[status] || 'gray'
}

const getEnrollmentColor = (current: number, max: number) => {
  const percentage = (current / max) * 100
  if (percentage >= 95) return 'red'
  if (percentage >= 80) return 'orange'
  if (percentage >= 60) return 'yellow'
  return 'green'
}

// Methods
const refreshData = () => {
  console.log('Refreshing course data...')
}

const clearFilters = () => {
  filters.value = {
    search: '',
    department: '',
    status: ''
  }
  currentPage.value = 1
}

const viewCourse = (courseId: string) => {
  navigateTo(`/admin/academic/courses/${courseId}`)
}

const editCourse = (courseId: string) => {
  navigateTo(`/admin/academic/courses/${courseId}/edit`)
}

const deleteCourse = async (courseId: string) => {
  if (confirm('Are you sure you want to delete this course?')) {
    await removeCourse(courseId)
  }
}

const addNewCourse = async () => {
  adding.value = true
  try {
    await addCourse(newCourse.value)
    showAddModal.value = false
    // Reset form
    Object.assign(newCourse.value, {
      code: '',
      title: '',
      description: '',
      credits: 3,
      department: '',
      departmentId: '',
      instructorIds: [],
      instructors: [],
      prerequisites: [],
      maxEnrollment: 30,
      currentEnrollment: 0,
      waitlistCount: 0,
      schedule: {
        days: [],
        time: '',
        room: '',
        building: ''
      },
      semester: 'Fall',
      year: 2024,
      status: 'active' as const,
      tuition: 1200,
      fees: 50
    })
  } finally {
    adding.value = false
  }
}

// Watch for filter changes to reset pagination
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })
</script>
