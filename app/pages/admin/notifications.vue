<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- Page Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Notification Management</h1>
          <p class="text-gray-600 dark:text-gray-300">Create and manage system-wide notifications</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showNotificationModal = true"
          >
            Create Notification
          </UButton>
        </div>
      </div>

      <!-- Notification Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-bell" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Notifications</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ notifications.length }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-check-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Notifications</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.activeNotifications }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-clock" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Scheduled</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ scheduledNotifications }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-users" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Recipients</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalRecipients }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Quick Actions -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Actions</h2>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <UButton
            color="blue"
            variant="outline"
            class="justify-start"
            @click="createQuickNotification('maintenance')"
          >
            <UIcon name="i-heroicons-wrench-screwdriver" class="w-4 h-4 mr-2" />
            Maintenance Alert
          </UButton>
          
          <UButton
            color="green"
            variant="outline"
            class="justify-start"
            @click="createQuickNotification('info')"
          >
            <UIcon name="i-heroicons-information-circle" class="w-4 h-4 mr-2" />
            General Info
          </UButton>
          
          <UButton
            color="orange"
            variant="outline"
            class="justify-start"
            @click="createQuickNotification('warning')"
          >
            <UIcon name="i-heroicons-exclamation-triangle" class="w-4 h-4 mr-2" />
            Warning Notice
          </UButton>
          
          <UButton
            color="red"
            variant="outline"
            class="justify-start"
            @click="createQuickNotification('error')"
          >
            <UIcon name="i-heroicons-x-circle" class="w-4 h-4 mr-2" />
            Emergency Alert
          </UButton>
        </div>
      </UCard>

      <!-- Notifications List -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h2>
            <div class="flex items-center space-x-2">
              <USelect
                v-model="filters.type"
                :options="typeOptions"
                placeholder="All Types"
                size="sm"
              />
              <USelect
                v-model="filters.status"
                :options="statusOptions"
                placeholder="All Status"
                size="sm"
              />
              <USelect
                v-model="filters.audience"
                :options="audienceOptions"
                placeholder="All Audiences"
                size="sm"
              />
            </div>
          </div>
        </template>

        <div v-if="loading" class="space-y-4">
          <USkeleton v-for="i in 5" :key="i" class="h-20" />
        </div>

        <div v-else-if="filteredNotifications.length === 0" class="text-center py-12">
          <UIcon name="i-heroicons-bell" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Notifications Found</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Create your first notification to get started.
          </p>
          <UButton color="primary" @click="showNotificationModal = true">
            Create Notification
          </UButton>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="notification in filteredNotifications"
            :key="notification.id"
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <h3 class="font-medium text-gray-900 dark:text-white">{{ notification.title }}</h3>
                  <UBadge
                    :color="getTypeColor(notification.type)"
                    variant="soft"
                  >
                    {{ notification.type }}
                  </UBadge>
                  <UBadge
                    :color="getStatusColor(notification.status)"
                    variant="soft"
                  >
                    {{ notification.status }}
                  </UBadge>
                  <UBadge
                    :color="getPriorityColor(notification.priority)"
                    variant="soft"
                    size="xs"
                  >
                    {{ notification.priority }}
                  </UBadge>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">{{ notification.message }}</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Audience:</span>
                    <span class="ml-1 text-gray-900 dark:text-white">{{ notification.targetAudience }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Channels:</span>
                    <span class="ml-1 text-gray-900 dark:text-white">{{ notification.channels.join(', ') }}</span>
                  </div>
                  <div v-if="notification.scheduledDate">
                    <span class="text-gray-500 dark:text-gray-400">Scheduled:</span>
                    <span class="ml-1 text-gray-900 dark:text-white">{{ formatDateTime(notification.scheduledDate) }}</span>
                  </div>
                  <div v-if="notification.sentAt">
                    <span class="text-gray-500 dark:text-gray-400">Sent:</span>
                    <span class="ml-1 text-gray-900 dark:text-white">{{ formatDateTime(notification.sentAt) }}</span>
                  </div>
                </div>

                <div v-if="notification.recipients" class="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Delivery Statistics</h4>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span class="text-gray-500 dark:text-gray-400">Total:</span>
                      <span class="ml-1 font-medium text-gray-900 dark:text-white">{{ notification.recipients.total }}</span>
                    </div>
                    <div>
                      <span class="text-gray-500 dark:text-gray-400">Sent:</span>
                      <span class="ml-1 font-medium text-blue-600 dark:text-blue-400">{{ notification.recipients.sent }}</span>
                    </div>
                    <div>
                      <span class="text-gray-500 dark:text-gray-400">Delivered:</span>
                      <span class="ml-1 font-medium text-green-600 dark:text-green-400">{{ notification.recipients.delivered }}</span>
                    </div>
                    <div>
                      <span class="text-gray-500 dark:text-gray-400">Failed:</span>
                      <span class="ml-1 font-medium text-red-600 dark:text-red-400">{{ notification.recipients.failed }}</span>
                    </div>
                  </div>
                  
                  <div class="mt-2">
                    <div class="flex items-center justify-between text-xs mb-1">
                      <span>Delivery Rate</span>
                      <span>{{ Math.round((notification.recipients.delivered / notification.recipients.total) * 100) }}%</span>
                    </div>
                    <UProgress
                      :value="(notification.recipients.delivered / notification.recipients.total) * 100"
                      :color="(notification.recipients.delivered / notification.recipients.total) >= 0.95 ? 'green' : 'orange'"
                      size="xs"
                    />
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-2 ml-4">
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-eye"
                  @click="viewNotification(notification.id)"
                />
                <UButton
                  v-if="notification.status === 'draft'"
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-pencil"
                  @click="editNotification(notification.id)"
                />
                <UButton
                  v-if="notification.status === 'draft' || notification.status === 'scheduled'"
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-paper-airplane"
                  class="text-green-600 dark:text-green-400"
                  @click="sendNotificationNow(notification.id)"
                />
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-trash"
                  class="text-red-600 dark:text-red-400"
                  @click="deleteNotification(notification.id)"
                />
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Create Notification Modal -->
    <UModal v-model="showNotificationModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Create Notification</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newNotification.title"
            label="Title"
            placeholder="e.g., System Maintenance Scheduled"
            required
          />

          <UTextarea
            v-model="newNotification.message"
            label="Message"
            placeholder="Enter notification message..."
            :rows="4"
            required
          />

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <USelect
              v-model="newNotification.type"
              label="Type"
              :options="typeOptions"
              required
            />
            <USelect
              v-model="newNotification.priority"
              label="Priority"
              :options="priorityOptions"
              required
            />
          </div>

          <USelect
            v-model="newNotification.targetAudience"
            label="Target Audience"
            :options="audienceOptions"
            required
          />

          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Delivery Channels</label>
            <div class="grid grid-cols-2 gap-2">
              <div
                v-for="channel in availableChannels"
                :key="channel.value"
                class="flex items-center space-x-2"
              >
                <UCheckbox
                  v-model="selectedChannels"
                  :value="channel.value"
                  :label="channel.label"
                />
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newNotification.scheduledDate"
              label="Schedule Date (Optional)"
              type="datetime-local"
            />
            <UInput
              v-model="newNotification.expiresAt"
              label="Expires At (Optional)"
              type="datetime-local"
            />
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showNotificationModal = false">
              Cancel
            </UButton>
            <UButton color="gray" @click="saveAsDraft" :loading="saving">
              Save as Draft
            </UButton>
            <UButton color="primary" @click="createAndSendNotification" :loading="sending">
              Create & Send
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { notifications, loading, systemStats, addNotification, sendNotification } = useSystemData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Notification Management'
})

// Reactive state
const showNotificationModal = ref(false)
const saving = ref(false)
const sending = ref(false)
const selectedChannels = ref([])

// Filters
const filters = ref({
  type: '',
  status: '',
  audience: ''
})

// New notification form
const newNotification = ref({
  title: '',
  message: '',
  type: 'info',
  priority: 'medium',
  targetAudience: 'all',
  channels: [],
  scheduledDate: '',
  expiresAt: '',
  createdBy: 'Admin User'
})

// Computed properties
const filteredNotifications = computed(() => {
  return notifications.value.filter(notification => {
    const matchesType = !filters.value.type || notification.type === filters.value.type
    const matchesStatus = !filters.value.status || notification.status === filters.value.status
    const matchesAudience = !filters.value.audience || notification.targetAudience === filters.value.audience
    return matchesType && matchesStatus && matchesAudience
  }).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
})

const scheduledNotifications = computed(() => {
  return notifications.value.filter(n => n.status === 'scheduled').length
})

const totalRecipients = computed(() => {
  return notifications.value.reduce((total, n) => {
    return total + (n.recipients?.total || 0)
  }, 0)
})

// Options for dropdowns
const typeOptions = [
  { label: 'Info', value: 'info' },
  { label: 'Warning', value: 'warning' },
  { label: 'Error', value: 'error' },
  { label: 'Success', value: 'success' },
  { label: 'Maintenance', value: 'maintenance' }
]

const statusOptions = [
  { label: 'Draft', value: 'draft' },
  { label: 'Scheduled', value: 'scheduled' },
  { label: 'Sent', value: 'sent' },
  { label: 'Failed', value: 'failed' },
  { label: 'Cancelled', value: 'cancelled' }
]

const priorityOptions = [
  { label: 'Low', value: 'low' },
  { label: 'Medium', value: 'medium' },
  { label: 'High', value: 'high' },
  { label: 'Urgent', value: 'urgent' }
]

const audienceOptions = [
  { label: 'All Users', value: 'all' },
  { label: 'Students', value: 'students' },
  { label: 'Faculty', value: 'faculty' },
  { label: 'Staff', value: 'staff' },
  { label: 'Admins', value: 'admins' }
]

const availableChannels = [
  { label: 'Email', value: 'email' },
  { label: 'SMS', value: 'sms' },
  { label: 'Push Notification', value: 'push' },
  { label: 'Banner', value: 'banner' },
  { label: 'Popup', value: 'popup' }
]

// Helper functions
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getTypeColor = (type: string) => {
  const colors = {
    'info': 'blue',
    'warning': 'orange',
    'error': 'red',
    'success': 'green',
    'maintenance': 'purple'
  }
  return colors[type] || 'gray'
}

const getStatusColor = (status: string) => {
  const colors = {
    'draft': 'gray',
    'scheduled': 'blue',
    'sent': 'green',
    'failed': 'red',
    'cancelled': 'orange'
  }
  return colors[status] || 'gray'
}

const getPriorityColor = (priority: string) => {
  const colors = {
    'low': 'blue',
    'medium': 'yellow',
    'high': 'orange',
    'urgent': 'red'
  }
  return colors[priority] || 'gray'
}

// Methods
const refreshData = () => {
  console.log('Refreshing notifications data...')
}

const createQuickNotification = (type: string) => {
  const templates = {
    maintenance: {
      title: 'Scheduled System Maintenance',
      message: 'The system will undergo scheduled maintenance. Some services may be temporarily unavailable.',
      type: 'maintenance',
      priority: 'medium'
    },
    info: {
      title: 'General Information',
      message: 'We have important information to share with you.',
      type: 'info',
      priority: 'low'
    },
    warning: {
      title: 'Important Notice',
      message: 'Please be aware of the following important information.',
      type: 'warning',
      priority: 'high'
    },
    error: {
      title: 'Emergency Alert',
      message: 'Immediate attention required. Please take necessary action.',
      type: 'error',
      priority: 'urgent'
    }
  }

  const template = templates[type]
  if (template) {
    Object.assign(newNotification.value, template)
    selectedChannels.value = ['email', 'banner']
    showNotificationModal.value = true
  }
}

const viewNotification = (notificationId: string) => {
  console.log('View notification:', notificationId)
}

const editNotification = (notificationId: string) => {
  console.log('Edit notification:', notificationId)
}

const sendNotificationNow = async (notificationId: string) => {
  if (confirm('Are you sure you want to send this notification now?')) {
    await sendNotification(notificationId)
  }
}

const deleteNotification = (notificationId: string) => {
  if (confirm('Are you sure you want to delete this notification?')) {
    console.log('Delete notification:', notificationId)
  }
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    await addNotification({
      ...newNotification.value,
      channels: selectedChannels.value,
      status: 'draft'
    })
    showNotificationModal.value = false
    resetForm()
  } finally {
    saving.value = false
  }
}

const createAndSendNotification = async () => {
  sending.value = true
  try {
    const notification = await addNotification({
      ...newNotification.value,
      channels: selectedChannels.value,
      status: newNotification.value.scheduledDate ? 'scheduled' : 'sent',
      sentAt: newNotification.value.scheduledDate ? undefined : new Date().toISOString()
    })
    
    if (!newNotification.value.scheduledDate) {
      await sendNotification(notification.id)
    }
    
    showNotificationModal.value = false
    resetForm()
  } finally {
    sending.value = false
  }
}

const resetForm = () => {
  Object.assign(newNotification.value, {
    title: '',
    message: '',
    type: 'info',
    priority: 'medium',
    targetAudience: 'all',
    channels: [],
    scheduledDate: '',
    expiresAt: '',
    createdBy: 'Admin User'
  })
  selectedChannels.value = []
}
</script>
