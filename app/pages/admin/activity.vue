<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- Page Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">System Activity</h1>
          <p class="text-gray-600 dark:text-gray-300">Monitor system activity logs and audit trails</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-down-tray"
            @click="exportLogs"
          >
            Export Logs
          </UButton>
        </div>
      </div>

      <!-- System Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-document-text" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Logs</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.totalLogs }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-clock" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Activity</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.todayLogs }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-600 dark:text-red-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Error Logs</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.errorLogs }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-shield-exclamation" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Warning Logs</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.warningLogs }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Search and Filters -->
      <UCard>
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <!-- Search Input -->
            <div class="lg:col-span-2">
              <UInput
                v-model="filters.search"
                placeholder="Search by user, action, or resource..."
                icon="i-heroicons-magnifying-glass"
                size="sm"
              />
            </div>

            <!-- Category Filter -->
            <USelect
              v-model="filters.category"
              :options="categoryOptions"
              placeholder="All Categories"
              size="sm"
            />

            <!-- Status Filter -->
            <USelect
              v-model="filters.status"
              :options="statusOptions"
              placeholder="All Status"
              size="sm"
            />

            <!-- Severity Filter -->
            <USelect
              v-model="filters.severity"
              :options="severityOptions"
              placeholder="All Severity"
              size="sm"
            />
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-300">
              Showing {{ filteredLogs.length }} of {{ activityLogs.length }} logs
            </span>
            <UButton
              variant="ghost"
              size="sm"
              @click="clearFilters"
              v-if="hasActiveFilters"
            >
              Clear Filters
            </UButton>
          </div>
        </div>
      </UCard>

      <!-- Activity Logs Table -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Logs</h2>
        </template>

        <div v-if="loading" class="space-y-4">
          <USkeleton v-for="i in 10" :key="i" class="h-16" />
        </div>

        <div v-else-if="filteredLogs.length === 0" class="text-center py-12">
          <UIcon name="i-heroicons-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Activity Logs Found</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            {{ hasActiveFilters ? 'Try adjusting your search criteria.' : 'No activity logs available.' }}
          </p>
        </div>

        <UTable
          v-else
          :rows="paginatedLogs"
          :columns="logColumns"
          :loading="loading"
        >
          <template #timestamp-data="{ row }">
            <div class="text-sm">
              {{ formatDateTime(row.timestamp) }}
            </div>
          </template>

          <template #user-data="{ row }">
            <div class="flex items-center space-x-3">
              <UAvatar
                :alt="row.userName"
                size="sm"
              />
              <div>
                <div class="font-medium text-gray-900 dark:text-white">{{ row.userName }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.userRole }}</div>
              </div>
            </div>
          </template>

          <template #action-data="{ row }">
            <div>
              <div class="font-medium text-gray-900 dark:text-white">{{ row.action }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.resource }}</div>
            </div>
          </template>

          <template #status-data="{ row }">
            <UBadge
              :color="getStatusColor(row.status)"
              variant="soft"
            >
              {{ row.status }}
            </UBadge>
          </template>

          <template #severity-data="{ row }">
            <UBadge
              :color="getSeverityColor(row.severity)"
              variant="soft"
              size="xs"
            >
              {{ row.severity }}
            </UBadge>
          </template>

          <template #category-data="{ row }">
            <UBadge
              :color="getCategoryColor(row.category)"
              variant="soft"
              size="xs"
            >
              {{ row.category.replace('_', ' ') }}
            </UBadge>
          </template>

          <template #details-data="{ row }">
            <div class="max-w-xs truncate text-sm text-gray-600 dark:text-gray-300">
              {{ row.details }}
            </div>
          </template>

          <template #actions-data="{ row }">
            <div class="flex items-center space-x-2">
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-eye"
                @click="viewLogDetails(row)"
              />
              <UButton
                v-if="row.severity === 'high' || row.severity === 'critical'"
                size="xs"
                variant="ghost"
                icon="i-heroicons-flag"
                class="text-red-600 dark:text-red-400"
                @click="flagForReview(row.id)"
              />
            </div>
          </template>
        </UTable>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-center mt-6">
          <UPagination
            v-model="currentPage"
            :page-count="itemsPerPage"
            :total="filteredLogs.length"
            :max="5"
          />
        </div>
      </UCard>
    </div>

    <!-- Log Details Modal -->
    <UModal v-model="showDetailsModal">
      <UCard v-if="selectedLog">
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activity Log Details</h3>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Timestamp</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatDateTime(selectedLog.timestamp) }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">User</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedLog.userName }} ({{ selectedLog.userRole }})</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Action</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedLog.action }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Resource</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedLog.resource }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
              <UBadge :color="getStatusColor(selectedLog.status)" variant="soft">
                {{ selectedLog.status }}
              </UBadge>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Severity</label>
              <UBadge :color="getSeverityColor(selectedLog.severity)" variant="soft">
                {{ selectedLog.severity }}
              </UBadge>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
              <UBadge :color="getCategoryColor(selectedLog.category)" variant="soft">
                {{ selectedLog.category.replace('_', ' ') }}
              </UBadge>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">IP Address</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedLog.ipAddress }}</p>
            </div>
          </div>

          <div>
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Details</label>
            <p class="text-sm text-gray-900 dark:text-white mt-1">{{ selectedLog.details }}</p>
          </div>

          <div>
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">User Agent</label>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1 break-all">{{ selectedLog.userAgent }}</p>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end">
            <UButton color="gray" variant="outline" @click="showDetailsModal = false">
              Close
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { activityLogs, loading, systemStats, searchActivityLogs } = useSystemData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'System Activity'
})

// Reactive state
const showDetailsModal = ref(false)
const selectedLog = ref(null)
const currentPage = ref(1)
const itemsPerPage = 20

// Filters
const filters = ref({
  search: '',
  category: '',
  status: '',
  severity: ''
})

// Table columns
const logColumns = [
  { key: 'timestamp', label: 'Time', sortable: true },
  { key: 'user', label: 'User', sortable: true },
  { key: 'action', label: 'Action', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'severity', label: 'Severity', sortable: true },
  { key: 'category', label: 'Category', sortable: true },
  { key: 'details', label: 'Details' },
  { key: 'actions', label: 'Actions' }
]

// Computed properties
const filteredLogs = computed(() => {
  return searchActivityLogs(filters.value.search, filters.value)
})

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredLogs.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredLogs.value.length / itemsPerPage)
})

const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(filter => filter !== '')
})

// Options for dropdowns
const categoryOptions = [
  { label: 'Authentication', value: 'authentication' },
  { label: 'Authorization', value: 'authorization' },
  { label: 'Data Access', value: 'data_access' },
  { label: 'Data Modification', value: 'data_modification' },
  { label: 'System', value: 'system' },
  { label: 'Security', value: 'security' }
]

const statusOptions = [
  { label: 'Success', value: 'success' },
  { label: 'Warning', value: 'warning' },
  { label: 'Error', value: 'error' }
]

const severityOptions = [
  { label: 'Low', value: 'low' },
  { label: 'Medium', value: 'medium' },
  { label: 'High', value: 'high' },
  { label: 'Critical', value: 'critical' }
]

// Helper functions
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getStatusColor = (status: string) => {
  const colors = {
    'success': 'green',
    'warning': 'orange',
    'error': 'red'
  }
  return colors[status] || 'gray'
}

const getSeverityColor = (severity: string) => {
  const colors = {
    'low': 'blue',
    'medium': 'yellow',
    'high': 'orange',
    'critical': 'red'
  }
  return colors[severity] || 'gray'
}

const getCategoryColor = (category: string) => {
  const colors = {
    'authentication': 'blue',
    'authorization': 'purple',
    'data_access': 'green',
    'data_modification': 'orange',
    'system': 'gray',
    'security': 'red'
  }
  return colors[category] || 'gray'
}

// Methods
const refreshData = () => {
  console.log('Refreshing activity logs...')
}

const clearFilters = () => {
  filters.value = {
    search: '',
    category: '',
    status: '',
    severity: ''
  }
  currentPage.value = 1
}

const viewLogDetails = (log: any) => {
  selectedLog.value = log
  showDetailsModal.value = true
}

const flagForReview = (logId: string) => {
  console.log('Flag log for review:', logId)
}

const exportLogs = () => {
  console.log('Exporting activity logs...')
  // In a real app, this would generate and download a CSV/Excel file
}

// Watch for filter changes to reset pagination
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })
</script>
