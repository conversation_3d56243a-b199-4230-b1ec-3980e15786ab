<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- Page Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">System Maintenance</h1>
          <p class="text-gray-600 dark:text-gray-300">Manage system maintenance tasks and configurations</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showMaintenanceModal = true"
          >
            Schedule Maintenance
          </UButton>
        </div>
      </div>

      <!-- System Health Overview -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-signal" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">System Uptime</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.systemUptime }}%</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-cpu-chip" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Memory Usage</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.memoryUsage }}%</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-circle-stack" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Disk Usage</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.diskUsage }}%</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-wrench-screwdriver" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Maintenance</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStats.activeMaintenance }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- System Health Indicators -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">System Health</h2>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Memory Usage -->
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-300">Memory Usage</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ systemStats.memoryUsage }}%</span>
            </div>
            <UProgress
              :value="systemStats.memoryUsage"
              :color="systemStats.memoryUsage >= 90 ? 'red' : systemStats.memoryUsage >= 75 ? 'orange' : 'green'"
            />
          </div>

          <!-- Disk Usage -->
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-300">Disk Usage</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ systemStats.diskUsage }}%</span>
            </div>
            <UProgress
              :value="systemStats.diskUsage"
              :color="systemStats.diskUsage >= 90 ? 'red' : systemStats.diskUsage >= 75 ? 'orange' : 'green'"
            />
          </div>

          <!-- Last Backup -->
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-300">Last Backup</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ formatDate(systemStats.lastBackup) }}</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Automated daily backups are running successfully
            </div>
          </div>

          <!-- System Uptime -->
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-300">System Uptime</span>
              <span class="font-medium text-green-600 dark:text-green-400">{{ systemStats.systemUptime }}%</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Excellent system reliability
            </div>
          </div>
        </div>
      </UCard>

      <!-- Maintenance Tasks -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Maintenance Tasks</h2>
            <div class="flex items-center space-x-2">
              <USelect
                v-model="filters.status"
                :options="statusOptions"
                placeholder="All Status"
                size="sm"
              />
              <USelect
                v-model="filters.type"
                :options="typeOptions"
                placeholder="All Types"
                size="sm"
              />
            </div>
          </div>
        </template>

        <div v-if="loading" class="space-y-4">
          <USkeleton v-for="i in 5" :key="i" class="h-20" />
        </div>

        <div v-else-if="filteredMaintenanceTasks.length === 0" class="text-center py-12">
          <UIcon name="i-heroicons-wrench-screwdriver" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Maintenance Tasks</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Schedule your first maintenance task to get started.
          </p>
          <UButton color="primary" @click="showMaintenanceModal = true">
            Schedule Maintenance
          </UButton>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="task in filteredMaintenanceTasks"
            :key="task.id"
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <h3 class="font-medium text-gray-900 dark:text-white">{{ task.title }}</h3>
                  <UBadge
                    :color="getStatusColor(task.status)"
                    variant="soft"
                  >
                    {{ task.status.replace('_', ' ') }}
                  </UBadge>
                  <UBadge
                    :color="getPriorityColor(task.priority)"
                    variant="soft"
                    size="xs"
                  >
                    {{ task.priority }}
                  </UBadge>
                  <UBadge
                    :color="getTypeColor(task.type)"
                    variant="soft"
                    size="xs"
                  >
                    {{ task.type }}
                  </UBadge>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">{{ task.description }}</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Scheduled:</span>
                    <span class="ml-1 text-gray-900 dark:text-white">{{ formatDateTime(task.scheduledDate) }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Duration:</span>
                    <span class="ml-1 text-gray-900 dark:text-white">{{ task.estimatedDuration }} min</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Performed by:</span>
                    <span class="ml-1 text-gray-900 dark:text-white">{{ task.performedBy }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Downtime:</span>
                    <span class="ml-1" :class="task.downtime ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'">
                      {{ task.downtime ? 'Yes' : 'No' }}
                    </span>
                  </div>
                </div>

                <div v-if="task.affectedSystems.length > 0" class="mt-3">
                  <span class="text-sm text-gray-500 dark:text-gray-400">Affected Systems:</span>
                  <div class="flex flex-wrap gap-1 mt-1">
                    <UBadge
                      v-for="system in task.affectedSystems"
                      :key="system"
                      color="gray"
                      variant="soft"
                      size="xs"
                    >
                      {{ system }}
                    </UBadge>
                  </div>
                </div>

                <div v-if="task.results" class="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <h4 class="text-sm font-medium text-green-800 dark:text-green-300 mb-1">Results</h4>
                  <p class="text-sm text-green-700 dark:text-green-400">{{ task.results }}</p>
                </div>

                <div v-if="task.errors && task.errors.length > 0" class="mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <h4 class="text-sm font-medium text-red-800 dark:text-red-300 mb-1">Errors</h4>
                  <ul class="text-sm text-red-700 dark:text-red-400 space-y-1">
                    <li v-for="error in task.errors" :key="error">• {{ error }}</li>
                  </ul>
                </div>
              </div>

              <div class="flex items-center space-x-2 ml-4">
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-eye"
                  @click="viewMaintenanceTask(task.id)"
                />
                <UButton
                  v-if="task.status === 'scheduled'"
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-pencil"
                  @click="editMaintenanceTask(task.id)"
                />
                <UButton
                  v-if="task.status === 'scheduled'"
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-play"
                  class="text-green-600 dark:text-green-400"
                  @click="startMaintenanceTask(task.id)"
                />
                <UButton
                  v-if="task.status === 'in_progress'"
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-check"
                  class="text-blue-600 dark:text-blue-400"
                  @click="completeMaintenanceTask(task.id)"
                />
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-trash"
                  class="text-red-600 dark:text-red-400"
                  @click="deleteMaintenanceTask(task.id)"
                />
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Schedule Maintenance Modal -->
    <UModal v-model="showMaintenanceModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Schedule Maintenance Task</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newTask.title"
            label="Task Title"
            placeholder="e.g., Database Performance Optimization"
            required
          />

          <UTextarea
            v-model="newTask.description"
            label="Description"
            placeholder="Describe the maintenance task..."
            :rows="3"
          />

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <USelect
              v-model="newTask.type"
              label="Type"
              :options="typeOptions"
              required
            />
            <USelect
              v-model="newTask.priority"
              label="Priority"
              :options="priorityOptions"
              required
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newTask.scheduledDate"
              label="Scheduled Date"
              type="datetime-local"
              required
            />
            <UInput
              v-model.number="newTask.estimatedDuration"
              label="Estimated Duration (minutes)"
              type="number"
              min="1"
              required
            />
          </div>

          <UInput
            v-model="newTask.performedBy"
            label="Performed By"
            placeholder="e.g., System Administrator"
            required
          />

          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Affected Systems</label>
            <div class="grid grid-cols-2 gap-2">
              <div
                v-for="system in availableSystems"
                :key="system"
                class="flex items-center space-x-2"
              >
                <UCheckbox
                  v-model="selectedSystems"
                  :value="system"
                  :label="system"
                />
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <UCheckbox
              v-model="newTask.downtime"
              label="This maintenance will cause system downtime"
            />
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showMaintenanceModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="scheduleMaintenanceTask" :loading="scheduling">
              Schedule Task
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { maintenanceTasks, loading, systemStats, addMaintenanceTask, updateMaintenanceTask } = useSystemData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'System Maintenance'
})

// Reactive state
const showMaintenanceModal = ref(false)
const scheduling = ref(false)
const selectedSystems = ref([])

// Filters
const filters = ref({
  status: '',
  type: ''
})

// New task form
const newTask = ref({
  title: '',
  description: '',
  type: 'database',
  priority: 'medium',
  scheduledDate: '',
  estimatedDuration: 60,
  performedBy: '',
  affectedSystems: [],
  downtime: false,
  status: 'scheduled'
})

// Available systems
const availableSystems = [
  'Database Server',
  'Web Servers',
  'Application Servers',
  'Cache Servers',
  'CDN',
  'Load Balancers',
  'File Storage',
  'Backup Systems'
]

// Computed properties
const filteredMaintenanceTasks = computed(() => {
  return maintenanceTasks.value.filter(task => {
    const matchesStatus = !filters.value.status || task.status === filters.value.status
    const matchesType = !filters.value.type || task.type === filters.value.type
    return matchesStatus && matchesType
  }).sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime())
})

// Options for dropdowns
const statusOptions = [
  { label: 'Scheduled', value: 'scheduled' },
  { label: 'In Progress', value: 'in_progress' },
  { label: 'Completed', value: 'completed' },
  { label: 'Failed', value: 'failed' },
  { label: 'Cancelled', value: 'cancelled' }
]

const typeOptions = [
  { label: 'Database', value: 'database' },
  { label: 'Cache', value: 'cache' },
  { label: 'Backup', value: 'backup' },
  { label: 'Update', value: 'update' },
  { label: 'Security', value: 'security' },
  { label: 'Performance', value: 'performance' }
]

const priorityOptions = [
  { label: 'Low', value: 'low' },
  { label: 'Medium', value: 'medium' },
  { label: 'High', value: 'high' },
  { label: 'Critical', value: 'critical' }
]

// Helper functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusColor = (status: string) => {
  const colors = {
    'scheduled': 'blue',
    'in_progress': 'orange',
    'completed': 'green',
    'failed': 'red',
    'cancelled': 'gray'
  }
  return colors[status] || 'gray'
}

const getPriorityColor = (priority: string) => {
  const colors = {
    'low': 'blue',
    'medium': 'yellow',
    'high': 'orange',
    'critical': 'red'
  }
  return colors[priority] || 'gray'
}

const getTypeColor = (type: string) => {
  const colors = {
    'database': 'purple',
    'cache': 'blue',
    'backup': 'green',
    'update': 'orange',
    'security': 'red',
    'performance': 'yellow'
  }
  return colors[type] || 'gray'
}

// Methods
const refreshData = () => {
  console.log('Refreshing maintenance data...')
}

const viewMaintenanceTask = (taskId: string) => {
  console.log('View maintenance task:', taskId)
}

const editMaintenanceTask = (taskId: string) => {
  console.log('Edit maintenance task:', taskId)
}

const startMaintenanceTask = async (taskId: string) => {
  await updateMaintenanceTask(taskId, {
    status: 'in_progress',
    startedAt: new Date().toISOString()
  })
}

const completeMaintenanceTask = async (taskId: string) => {
  await updateMaintenanceTask(taskId, {
    status: 'completed',
    completedAt: new Date().toISOString()
  })
}

const deleteMaintenanceTask = (taskId: string) => {
  if (confirm('Are you sure you want to delete this maintenance task?')) {
    console.log('Delete maintenance task:', taskId)
  }
}

const scheduleMaintenanceTask = async () => {
  scheduling.value = true
  try {
    await addMaintenanceTask({
      ...newTask.value,
      affectedSystems: selectedSystems.value
    })
    showMaintenanceModal.value = false
    // Reset form
    Object.assign(newTask.value, {
      title: '',
      description: '',
      type: 'database',
      priority: 'medium',
      scheduledDate: '',
      estimatedDuration: 60,
      performedBy: '',
      affectedSystems: [],
      downtime: false,
      status: 'scheduled'
    })
    selectedSystems.value = []
  } finally {
    scheduling.value = false
  }
}
</script>
