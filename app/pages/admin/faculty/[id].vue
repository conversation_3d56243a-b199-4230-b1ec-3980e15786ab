<template>
  <UContainer class="py-6">
    <div v-if="loading" class="space-y-6">
      <USkeleton class="h-8 w-64" />
      <USkeleton class="h-64 w-full" />
      <USkeleton class="h-96 w-full" />
    </div>

    <div v-else-if="!faculty" class="flex items-center justify-center min-h-96">
      <div class="text-center">
        <UIcon name="i-heroicons-user-circle" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Faculty Not Found</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">The requested faculty member could not be found.</p>
        <UButton @click="$router.back()">
          Go Back
        </UButton>
      </div>
    </div>

    <div v-else class="space-y-6">
      <!-- Faculty Header -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex flex-col lg:flex-row lg:items-start lg:space-x-6 space-y-4 lg:space-y-0">
          <!-- Faculty Photo and Basic Info -->
          <div class="flex-shrink-0">
            <UAvatar
              :src="faculty.avatar"
              :alt="`${faculty.firstName} ${faculty.lastName}`"
              size="2xl"
              :ui="{ rounded: 'rounded-lg' }"
            />
          </div>

          <!-- Faculty Details -->
          <div class="flex-1 min-w-0">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ faculty.firstName }} {{ faculty.lastName }}
                </h1>
                <p class="text-lg text-gray-600 dark:text-gray-300 mt-1">
                  {{ faculty.title }}
                </p>
                <div class="flex items-center space-x-3 mt-2">
                  <UBadge
                    :color="getRankColor(faculty.rank)"
                    variant="soft"
                  >
                    {{ faculty.rank }}
                  </UBadge>
                  <UBadge
                    :color="getStatusColor(faculty.status)"
                    variant="soft"
                  >
                    {{ faculty.status }}
                  </UBadge>
                  <UBadge
                    v-if="faculty.tenure"
                    color="green"
                    variant="soft"
                  >
                    Tenured
                  </UBadge>
                </div>
              </div>

              <!-- Quick Actions -->
              <div class="flex items-center space-x-2 mt-4 sm:mt-0">
                <UButton
                  color="primary"
                  icon="i-heroicons-pencil"
                  @click="editFaculty"
                >
                  Edit Profile
                </UButton>
                <UButton
                  color="gray"
                  variant="outline"
                  icon="i-heroicons-envelope"
                  :to="`mailto:${faculty.email}`"
                >
                  Email
                </UButton>
                <UButton
                  v-if="faculty.website"
                  color="gray"
                  variant="outline"
                  icon="i-heroicons-globe-alt"
                  :to="faculty.website"
                  target="_blank"
                >
                  Website
                </UButton>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <UIcon name="i-heroicons-building-office" class="w-4 h-4 mr-2 flex-shrink-0" />
                <span>{{ faculty.department }}</span>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <UIcon name="i-heroicons-map-pin" class="w-4 h-4 mr-2 flex-shrink-0" />
                <span>{{ faculty.office }}</span>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <UIcon name="i-heroicons-phone" class="w-4 h-4 mr-2 flex-shrink-0" />
                <span>{{ faculty.phone }}</span>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <UIcon name="i-heroicons-envelope" class="w-4 h-4 mr-2 flex-shrink-0" />
                <span>{{ faculty.email }}</span>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <UIcon name="i-heroicons-clock" class="w-4 h-4 mr-2 flex-shrink-0" />
                <span>{{ faculty.officeHours || 'No office hours set' }}</span>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <UIcon name="i-heroicons-calendar-days" class="w-4 h-4 mr-2 flex-shrink-0" />
                <span>Hired {{ formatDate(faculty.hireDate) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Faculty Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Current Courses</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ faculty.courses.length }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-document-text" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Publications</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ faculty.publications.length }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Workload</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ faculty.workload }}%</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-clock" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Years of Service</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ getYearsOfService(faculty.hireDate) }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Tabbed Content -->
      <UTabs :items="tabs" class="w-full">
        <!-- Personal Information Tab -->
        <template #personal="{ item }">
          <div class="space-y-6">
            <!-- Biography -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Biography</h3>
              </template>
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                {{ faculty.bio }}
              </p>
            </UCard>

            <!-- Education -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Education</h3>
              </template>
              <div class="space-y-4">
                <div
                  v-for="edu in faculty.education"
                  :key="`${edu.degree}-${edu.institution}`"
                  class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                >
                  <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white">{{ edu.degree }} in {{ edu.field }}</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300">{{ edu.institution }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ edu.year }}</p>
                  </div>
                </div>
              </div>
            </UCard>

            <!-- Specializations -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Specializations</h3>
              </template>
              <div class="flex flex-wrap gap-2">
                <UBadge
                  v-for="spec in faculty.specializations"
                  :key="spec"
                  color="blue"
                  variant="soft"
                >
                  {{ spec }}
                </UBadge>
              </div>
            </UCard>
          </div>
        </template>

        <!-- Research Tab -->
        <template #research="{ item }">
          <div class="space-y-6">
            <!-- Research Interests -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Research Interests</h3>
              </template>
              <div class="flex flex-wrap gap-2">
                <UBadge
                  v-for="interest in faculty.researchInterests"
                  :key="interest"
                  color="purple"
                  variant="soft"
                >
                  {{ interest }}
                </UBadge>
              </div>
            </UCard>

            <!-- Publications -->
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Publications</h3>
                  <UBadge color="gray" variant="soft">{{ faculty.publications.length }} total</UBadge>
                </div>
              </template>
              <div v-if="faculty.publications.length === 0" class="text-center py-8">
                <UIcon name="i-heroicons-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-600 dark:text-gray-300">No publications listed</p>
              </div>
              <div v-else class="space-y-4">
                <div
                  v-for="pub in faculty.publications"
                  :key="pub.id"
                  class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                >
                  <h4 class="font-medium text-gray-900 dark:text-white mb-2">{{ pub.title }}</h4>
                  <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300">
                    <span>{{ pub.venue }}</span>
                    <span>{{ pub.year }}</span>
                    <UBadge color="green" variant="soft" size="xs">{{ pub.type }}</UBadge>
                  </div>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                    Authors: <AUTHORS>
                  </p>
                </div>
              </div>
            </UCard>
          </div>
        </template>

        <!-- Teaching Tab -->
        <template #teaching="{ item }">
          <div class="space-y-6">
            <!-- Current Courses -->
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Current Courses</h3>
                  <UBadge color="blue" variant="soft">{{ faculty.courses.length }}/{{ faculty.maxCourses }} courses</UBadge>
                </div>
              </template>
              <div v-if="faculty.courses.length === 0" class="text-center py-8">
                <UIcon name="i-heroicons-academic-cap" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-600 dark:text-gray-300">No courses assigned</p>
              </div>
              <div v-else class="space-y-4">
                <div
                  v-for="course in faculty.courses"
                  :key="course.id"
                  class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                >
                  <div class="flex items-start justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900 dark:text-white">
                        {{ course.courseCode }}: {{ course.courseName }}
                      </h4>
                      <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        {{ course.semester }} {{ course.year }} • {{ course.credits }} credits
                      </p>
                      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {{ course.schedule.days.join(', ') }} • {{ course.schedule.time }} • {{ course.schedule.room }}
                      </p>
                    </div>
                    <div class="text-right">
                      <UBadge
                        :color="course.status === 'Assigned' ? 'green' : 'yellow'"
                        variant="soft"
                        size="xs"
                      >
                        {{ course.status }}
                      </UBadge>
                      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {{ course.enrollmentCount }}/{{ course.maxEnrollment }} students
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </UCard>

            <!-- Teaching Load -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Teaching Load</h3>
              </template>
              <div class="space-y-4">
                <div>
                  <div class="flex items-center justify-between text-sm mb-2">
                    <span class="text-gray-600 dark:text-gray-300">Course Load</span>
                    <span class="font-medium text-gray-900 dark:text-white">
                      {{ faculty.courses.length }}/{{ faculty.maxCourses }} courses
                    </span>
                  </div>
                  <UProgress
                    :value="(faculty.courses.length / faculty.maxCourses) * 100"
                    :color="getWorkloadColor(faculty.courses.length, faculty.maxCourses)"
                  />
                </div>
                <div>
                  <div class="flex items-center justify-between text-sm mb-2">
                    <span class="text-gray-600 dark:text-gray-300">Overall Workload</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ faculty.workload }}%</span>
                  </div>
                  <UProgress
                    :value="faculty.workload"
                    :color="faculty.workload >= 90 ? 'red' : faculty.workload >= 75 ? 'orange' : 'green'"
                  />
                </div>
              </div>
            </UCard>
          </div>
        </template>
      </UTabs>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const { getFacultyById } = useFacultyData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Faculty Profile'
})

// Reactive state
const loading = ref(true)
const faculty = ref(null)

// Tab configuration
const tabs = [
  {
    key: 'personal',
    label: 'Personal',
    icon: 'i-heroicons-user'
  },
  {
    key: 'research',
    label: 'Research',
    icon: 'i-heroicons-beaker'
  },
  {
    key: 'teaching',
    label: 'Teaching',
    icon: 'i-heroicons-academic-cap'
  }
]

// Load faculty data
onMounted(async () => {
  try {
    const facultyId = route.params.id as string
    faculty.value = getFacultyById(facultyId)
  } finally {
    loading.value = false
  }
})

// Helper functions
const getRankColor = (rank: string) => {
  const colors = {
    'Professor': 'purple',
    'Associate Professor': 'blue',
    'Assistant Professor': 'green',
    'Lecturer': 'orange',
    'Adjunct Professor': 'gray'
  }
  return colors[rank] || 'gray'
}

const getStatusColor = (status: string) => {
  const colors = {
    'Active': 'green',
    'Sabbatical': 'blue',
    'Retired': 'gray',
    'On Leave': 'yellow'
  }
  return colors[status] || 'gray'
}

const getWorkloadColor = (current: number, max: number) => {
  const percentage = (current / max) * 100
  if (percentage >= 90) return 'red'
  if (percentage >= 75) return 'orange'
  if (percentage >= 50) return 'yellow'
  return 'green'
}

const getYearsOfService = (hireDate: string) => {
  const hire = new Date(hireDate)
  const now = new Date()
  return now.getFullYear() - hire.getFullYear()
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const editFaculty = () => {
  router.push(`/admin/faculty/${route.params.id}/edit`)
}
</script>
