<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Faculty Management</h1>
          <p class="text-gray-600 dark:text-gray-300">Manage faculty members, assignments, and profiles</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showAddModal = true"
          >
            Add Faculty
          </UButton>
        </div>
      </div>

      <!-- Faculty Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-users" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Faculty</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ facultyStats.total }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Courses</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ facultyStats.totalCourses }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Workload</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ facultyStats.averageWorkload }}%</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-building-office" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Departments</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ Object.keys(facultyStats.byDepartment).length }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Search and Filters -->
      <UCard>
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <!-- Search Input -->
            <div class="lg:col-span-2">
              <UInput
                v-model="filters.search"
                placeholder="Search faculty by name, email, or specialization..."
                icon="i-heroicons-magnifying-glass"
                size="sm"
              />
            </div>

            <!-- Department Filter -->
            <USelect
              v-model="filters.department"
              :options="departmentOptions"
              placeholder="All Departments"
              size="sm"
            />

            <!-- Rank Filter -->
            <USelect
              v-model="filters.rank"
              :options="rankOptions"
              placeholder="All Ranks"
              size="sm"
            />

            <!-- Status Filter -->
            <USelect
              v-model="filters.status"
              :options="statusOptions"
              placeholder="All Status"
              size="sm"
            />
          </div>

          <!-- Research Area Filter -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="filters.researchArea"
              placeholder="Filter by research area..."
              icon="i-heroicons-beaker"
              size="sm"
            />
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-300">
                Showing {{ filteredFaculty.length }} of {{ faculty.length }} faculty members
              </span>
              <UButton
                variant="ghost"
                size="sm"
                @click="clearFilters"
                v-if="hasActiveFilters"
              >
                Clear Filters
              </UButton>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Faculty Grid -->
      <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <USkeleton v-for="i in 6" :key="i" class="h-64" />
      </div>

      <div v-else-if="filteredFaculty.length === 0" class="text-center py-12">
        <UIcon name="i-heroicons-users" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Faculty Found</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          {{ hasActiveFilters ? 'Try adjusting your search criteria.' : 'Get started by adding your first faculty member.' }}
        </p>
        <UButton v-if="!hasActiveFilters" color="primary" @click="showAddModal = true">
          Add Faculty Member
        </UButton>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <FacultyCard
          v-for="faculty_member in paginatedFaculty"
          :key="faculty_member.id"
          :faculty="faculty_member"
          @view="viewFaculty"
          @edit="editFaculty"
          @delete="deleteFaculty"
        />
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex justify-center">
        <UPagination
          v-model="currentPage"
          :page-count="itemsPerPage"
          :total="filteredFaculty.length"
          :max="5"
        />
      </div>
    </div>

    <!-- Add Faculty Modal -->
    <UModal v-model="showAddModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add New Faculty Member</h3>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newFaculty.firstName"
              label="First Name"
              placeholder="Enter first name"
              required
            />
            <UInput
              v-model="newFaculty.lastName"
              label="Last Name"
              placeholder="Enter last name"
              required
            />
          </div>

          <UInput
            v-model="newFaculty.email"
            label="Email"
            type="email"
            placeholder="Enter email address"
            required
          />

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <USelect
              v-model="newFaculty.department"
              label="Department"
              :options="departmentOptions"
              placeholder="Select department"
              required
            />
            <USelect
              v-model="newFaculty.rank"
              label="Rank"
              :options="rankOptions"
              placeholder="Select rank"
              required
            />
          </div>

          <UInput
            v-model="newFaculty.title"
            label="Title"
            placeholder="e.g., Professor of Computer Science"
            required
          />

          <UTextarea
            v-model="newFaculty.bio"
            label="Biography"
            placeholder="Enter faculty biography..."
            :rows="3"
          />
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showAddModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addFacultyMember" :loading="adding">
              Add Faculty
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup>
const { faculty, loading, facultyStats, searchFaculty, addFaculty, deleteFaculty: removeFaculty } = useFacultyData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Faculty Management'
})

// Reactive state
const showAddModal = ref(false)
const adding = ref(false)
const currentPage = ref(1)
const itemsPerPage = 12

// Filters
const filters = ref({
  search: '',
  department: '',
  rank: '',
  status: '',
  researchArea: ''
})

// New faculty form
const newFaculty = ref({
  firstName: '',
  lastName: '',
  email: '',
  department: '',
  rank: '',
  title: '',
  bio: '',
  phone: '',
  office: '',
  officeHours: '',
  specializations: [],
  researchInterests: [],
  education: [],
  publications: [],
  courses: [],
  website: '',
  linkedIn: '',
  orcid: '',
  hireDate: new Date().toISOString().split('T')[0],
  tenure: false,
  status: 'Active',
  workload: 75,
  maxCourses: 3,
  preferredSchedule: ['Morning', 'Afternoon']
})

// Computed properties
const filteredFaculty = computed(() => {
  return searchFaculty(filters.value)
})

const paginatedFaculty = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredFaculty.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredFaculty.value.length / itemsPerPage)
})

const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(filter => filter !== '')
})

// Options for dropdowns
const departmentOptions = computed(() => {
  const departments = [...new Set(faculty.value.map(f => f.department))]
  return departments.map(dept => ({ label: dept, value: dept }))
})

const rankOptions = [
  { label: 'Professor', value: 'Professor' },
  { label: 'Associate Professor', value: 'Associate Professor' },
  { label: 'Assistant Professor', value: 'Assistant Professor' },
  { label: 'Lecturer', value: 'Lecturer' },
  { label: 'Adjunct Professor', value: 'Adjunct Professor' }
]

const statusOptions = [
  { label: 'Active', value: 'Active' },
  { label: 'Sabbatical', value: 'Sabbatical' },
  { label: 'Retired', value: 'Retired' },
  { label: 'On Leave', value: 'On Leave' }
]

// Methods
const refreshData = () => {
  // In a real app, this would refetch data from the API
  console.log('Refreshing faculty data...')
}

const clearFilters = () => {
  filters.value = {
    search: '',
    department: '',
    rank: '',
    status: '',
    researchArea: ''
  }
  currentPage.value = 1
}

const viewFaculty = (facultyId) => {
  navigateTo(`/admin/faculty/${facultyId}`)
}

const editFaculty = (facultyId) => {
  navigateTo(`/admin/faculty/${facultyId}/edit`)
}

const deleteFaculty = async (facultyId) => {
  if (confirm('Are you sure you want to delete this faculty member?')) {
    await removeFaculty(facultyId)
  }
}

const addFacultyMember = async () => {
  adding.value = true
  try {
    await addFaculty(newFaculty.value)
    showAddModal.value = false
    // Reset form
    Object.keys(newFaculty.value).forEach(key => {
      if (Array.isArray(newFaculty.value[key])) {
        newFaculty.value[key] = []
      } else if (typeof newFaculty.value[key] === 'boolean') {
        newFaculty.value[key] = false
      } else if (typeof newFaculty.value[key] === 'number') {
        newFaculty.value[key] = key === 'workload' ? 75 : key === 'maxCourses' ? 3 : 0
      } else {
        newFaculty.value[key] = key === 'hireDate' ? new Date().toISOString().split('T')[0] : 
                                key === 'status' ? 'Active' : ''
      }
    })
  } finally {
    adding.value = false
  }
}

// Watch for filter changes to reset pagination
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })
</script>
