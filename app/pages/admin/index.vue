<template>
  <UContainer class="py-6">
    <div class="space-y-4">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Admin Dashboard</h1>
        <p class="text-gray-600 dark:text-gray-300">Welcome back, Administrator</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshData"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UDropdownMenu :items="quickActions">
          <UButton color="primary" icon="i-heroicons-plus">
            Quick Actions
          </UButton>
        </UDropdownMenu>
      </div>
    </div>

    <!-- Key Metrics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Loading Skeletons -->
      <UCard
        v-if="isLoading"
        v-for="n in 8"
        :key="`metric-skeleton-${n}`"
        class="animate-pulse"
      >
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <USkeleton class="w-12 h-12 rounded-lg" />
          </div>
          <div class="ml-4 space-y-2">
            <USkeleton class="h-3 w-24" />
            <USkeleton class="h-6 w-16" />
          </div>
        </div>
      </UCard>

      <!-- Actual Metrics -->
      <UCard v-else v-for="metric in adminMetrics" :key="metric.id" class="relative overflow-hidden">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center',
              getMetricBackgroundClass(metric.color)
            ]">
              <UIcon
                :name="metric.icon"
                :class="[
                  'w-6 h-6',
                  getMetricIconClass(metric.color)
                ]"
              />
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">{{ metric.title }}</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ metric.value }}</p>
            <div v-if="metric.change" class="flex items-center mt-1">
              <UIcon
                :name="metric.changeType === 'increase' ? 'i-heroicons-arrow-trending-up' :
                      metric.changeType === 'decrease' ? 'i-heroicons-arrow-trending-down' :
                      'i-heroicons-minus'"
                :class="[
                  'w-4 h-4 mr-1',
                  metric.changeType === 'increase' ? 'text-green-500' :
                  metric.changeType === 'decrease' ? 'text-red-500' :
                  'text-gray-500'
                ]"
              />
              <span :class="[
                'text-sm font-medium',
                metric.changeType === 'increase' ? 'text-green-600 dark:text-green-400' :
                metric.changeType === 'decrease' ? 'text-red-600 dark:text-red-400' :
                'text-gray-600 dark:text-gray-400'
              ]">
                {{ metric.change }}
              </span>
            </div>
          </div>
        </div>
        <div class="mt-3">
          <p class="text-xs text-gray-500 dark:text-gray-400">{{ metric.description }}</p>
        </div>
      </UCard>
    </div>

    <!-- Quick Actions Grid -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Quick Actions</h2>
          <UButton
            variant="ghost"
            size="sm"
            icon="i-heroicons-cog-6-tooth"
            to="/admin/settings"
          >
            Settings
          </UButton>
        </div>
      </template>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <UButton
          to="/admin/users"
          variant="outline"
          class="justify-start h-auto py-4"
          icon="i-heroicons-users"
        >
          <div class="text-left">
            <div class="font-medium">Manage Users</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Add, edit, or remove users</div>
          </div>
        </UButton>
        <UButton
          to="/admin/academic/courses"
          variant="outline"
          class="justify-start h-auto py-4"
          icon="i-heroicons-book-open"
        >
          <div class="text-left">
            <div class="font-medium">Course Catalog</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Manage courses and programs</div>
          </div>
        </UButton>
        <UButton
          to="/admin/financial/budget"
          variant="outline"
          class="justify-start h-auto py-4"
          icon="i-heroicons-chart-pie"
        >
          <div class="text-left">
            <div class="font-medium">Budget Overview</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Financial management</div>
          </div>
        </UButton>
        <UButton
          to="/admin/analytics"
          variant="outline"
          class="justify-start h-auto py-4"
          icon="i-heroicons-chart-bar"
        >
          <div class="text-left">
            <div class="font-medium">Analytics</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Reports and insights</div>
          </div>
        </UButton>
      </div>
    </UCard>

    <!-- Recent Activity and System Status -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <!-- Recent Activity -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold">Recent Activity</h2>
            <UButton
              variant="ghost"
              size="sm"
              icon="i-heroicons-arrow-path"
              @click="refreshActivities"
              :loading="isRefreshingActivities"
            >
              Refresh
            </UButton>
          </div>
        </template>
        <div class="space-y-4 max-h-96 overflow-y-auto">
          <!-- Loading Skeletons -->
          <div
            v-if="isLoading"
            v-for="n in 5"
            :key="`activity-skeleton-${n}`"
            class="flex items-start space-x-3 p-3 rounded-lg animate-pulse"
          >
            <USkeleton class="w-8 h-8 rounded-full" />
            <div class="flex-1 space-y-2">
              <USkeleton class="h-4 w-3/4" />
              <USkeleton class="h-3 w-1/2" />
            </div>
          </div>

          <!-- Actual Activities -->
          <div
            v-else
            v-for="activity in recentActivities"
            :key="activity.id"
            class="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <UAvatar
              :src="activity.user?.avatar"
              :alt="activity.user?.name"
              size="sm"
              :ui="{ background: getActivityColor(activity.type) }"
            >
              <UIcon
                :name="getActivityIcon(activity.type)"
                class="w-4 h-4"
              />
            </UAvatar>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ activity.title }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ activity.description }}</p>
              <div class="flex items-center mt-1 space-x-2">
                <span v-if="activity.user" class="text-xs text-gray-400">by {{ activity.user.name }}</span>
                <span class="text-xs text-gray-400">{{ formatTimeAgo(activity.timestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <UButton
            variant="ghost"
            size="sm"
            block
            to="/admin/activity"
          >
            View All Activity
          </UButton>
        </template>
      </UCard>

      <!-- System Status and Notifications -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold">System Status</h2>
            <UBadge
              :color="systemStatus.color"
              variant="soft"
            >
              {{ systemStatus.label }}
            </UBadge>
          </div>
        </template>
        <div class="space-y-4">
          <!-- System Health Indicators -->
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-lg font-bold text-green-600 dark:text-green-400">99.9%</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Uptime</div>
            </div>
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-lg font-bold text-blue-600 dark:text-blue-400">45ms</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Response Time</div>
            </div>
          </div>

          <!-- Recent Notifications -->
          <div class="space-y-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white">Recent Notifications</h3>
            <div
              v-for="notification in recentNotifications"
              :key="notification.id"
              class="space-y-2"
            >
              <UAlert
                :icon="getNotificationIcon(notification.type)"
                :color="notification.type === 'error' ? 'red' :
                        notification.type === 'warning' ? 'yellow' :
                        notification.type === 'success' ? 'green' : 'blue'"
                variant="soft"
                :title="notification.title"
                :description="notification.description"
              >
                <template #actions>
                  <UButton
                    v-if="notification.actionUrl"
                    :to="notification.actionUrl"
                    variant="ghost"
                    size="xs"
                  >
                    View
                  </UButton>
                </template>
              </UAlert>
            </div>
          </div>
        </div>
        <template #footer>
          <UButton
            variant="ghost"
            size="sm"
            block
            to="/admin/notifications"
          >
            View All Notifications
          </UButton>
        </template>
      </UCard>
    </div>
    <!-- Charts and Analytics Preview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- Enrollment Trends -->
      <UCard class="lg:col-span-2">
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold">Enrollment Trends</h2>
            <USelect
              v-model="selectedPeriod"
              :options="periodOptions"
              size="sm"
            />
          </div>
        </template>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="text-center">
            <UIcon name="i-heroicons-chart-bar" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 dark:text-gray-400">Chart visualization will be implemented</p>
            <p class="text-sm text-gray-400">with Chart.js or similar library</p>
          </div>
        </div>
      </UCard>

      <!-- Top Performing Courses -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">Top Courses</h2>
        </template>
        <div class="space-y-3">
          <div
            v-for="(course, index) in topCourses"
            :key="course.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center">
                <span class="text-sm font-bold text-primary-600 dark:text-primary-400">{{ index + 1 }}</span>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ course.name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ course.students }} students</p>
              </div>
            </div>
            <UBadge :color="course.rating >= 4.5 ? 'green' : course.rating >= 4.0 ? 'yellow' : 'gray'" variant="soft">
              {{ course.rating }}
            </UBadge>
          </div>
        </div>
      </UCard>
    </div>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

const { adminMetrics, generateActivities, generateNotifications } = useDashboardData()

// Reactive data
const isLoading = ref(true)
const isRefreshing = ref(false)
const isRefreshingActivities = ref(false)
const selectedPeriod = ref('last-30-days')
const recentActivities = ref(generateActivities('admin', 8))
const recentNotifications = ref(generateNotifications('admin', 3))

// Options
const periodOptions = [
  { label: 'Last 7 days', value: 'last-7-days' },
  { label: 'Last 30 days', value: 'last-30-days' },
  { label: 'Last 3 months', value: 'last-3-months' },
  { label: 'Last year', value: 'last-year' }
]

const quickActions = [
  [{
    label: 'Add New User',
    icon: 'i-heroicons-user-plus',
    onSelect: () => navigateTo('/admin/users/new')
  }, {
    label: 'Create Course',
    icon: 'i-heroicons-plus-circle',
    onSelect: () => navigateTo('/admin/academic/courses/new')
  }, {
    label: 'Generate Report',
    icon: 'i-heroicons-document-chart-bar',
    onSelect: () => navigateTo('/admin/reports/generate')
  }], [{
    label: 'System Settings',
    icon: 'i-heroicons-cog-6-tooth',
    onSelect: () => navigateTo('/admin/settings')
  }, {
    label: 'Backup Data',
    icon: 'i-heroicons-cloud-arrow-up',
    onSelect: () => initiateBackup()
  }]
]

// Mock data
const systemStatus = ref({
  label: 'All Systems Operational',
  color: 'green'
})

const topCourses = ref([
  { id: 1, name: 'CS 101', students: 45, rating: 4.8 },
  { id: 2, name: 'MATH 201', students: 38, rating: 4.6 },
  { id: 3, name: 'ENG 102', students: 42, rating: 4.5 },
  { id: 4, name: 'PHYS 101', students: 35, rating: 4.3 },
  { id: 5, name: 'CHEM 101', students: 40, rating: 4.2 }
])

// Helper functions
const getMetricBackgroundClass = (color) => {
  const classes = {
    blue: 'bg-blue-100 dark:bg-blue-900/20',
    green: 'bg-green-100 dark:bg-green-900/20',
    purple: 'bg-purple-100 dark:bg-purple-900/20',
    yellow: 'bg-yellow-100 dark:bg-yellow-900/20',
    red: 'bg-red-100 dark:bg-red-900/20',
    orange: 'bg-orange-100 dark:bg-orange-900/20',
    indigo: 'bg-indigo-100 dark:bg-indigo-900/20',
    pink: 'bg-pink-100 dark:bg-pink-900/20'
  }
  return classes[color] || 'bg-gray-100 dark:bg-gray-900/20'
}

const getMetricIconClass = (color) => {
  const classes = {
    blue: 'text-blue-600 dark:text-blue-400',
    green: 'text-green-600 dark:text-green-400',
    purple: 'text-purple-600 dark:text-purple-400',
    yellow: 'text-yellow-600 dark:text-yellow-400',
    red: 'text-red-600 dark:text-red-400',
    orange: 'text-orange-600 dark:text-orange-400',
    indigo: 'text-indigo-600 dark:text-indigo-400',
    pink: 'text-pink-600 dark:text-pink-400'
  }
  return classes[color] || 'text-gray-600 dark:text-gray-400'
}

const getActivityIcon = (type) => {
  const icons = {
    user: 'i-heroicons-user-plus',
    course: 'i-heroicons-academic-cap',
    system: 'i-heroicons-cog-6-tooth',
    grade: 'i-heroicons-chart-bar-square'
  }
  return icons[type] || 'i-heroicons-information-circle'
}

const getActivityColor = (type) => {
  const colors = {
    user: 'bg-blue-100 dark:bg-blue-900/20',
    course: 'bg-green-100 dark:bg-green-900/20',
    system: 'bg-purple-100 dark:bg-purple-900/20',
    grade: 'bg-yellow-100 dark:bg-yellow-900/20'
  }
  return colors[type] || 'bg-gray-100 dark:bg-gray-900/20'
}

const getNotificationIcon = (type) => {
  const icons = {
    info: 'i-heroicons-information-circle',
    warning: 'i-heroicons-exclamation-triangle',
    success: 'i-heroicons-check-circle',
    error: 'i-heroicons-x-circle'
  }
  return icons[type] || 'i-heroicons-information-circle'
}

const formatTimeAgo = (date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
  return `${Math.floor(diffInMinutes / 1440)}d ago`
}

// Actions
const refreshData = async () => {
  isRefreshing.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    // Refresh all data
    recentActivities.value = generateActivities('admin', 8)
    recentNotifications.value = generateNotifications('admin', 3)

    useToast().add({
      title: 'Data Refreshed',
      description: 'Dashboard data has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const refreshActivities = async () => {
  isRefreshingActivities.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    recentActivities.value = generateActivities('admin', 8)
  } finally {
    isRefreshingActivities.value = false
  }
}

const initiateBackup = () => {
  useToast().add({
    title: 'Backup Started',
    description: 'System backup has been initiated',
    icon: 'i-heroicons-cloud-arrow-up'
  })
}

// Auto-refresh data every 5 minutes
onMounted(async () => {
  // Simulate initial loading
  await new Promise(resolve => setTimeout(resolve, 1500))
  isLoading.value = false

  const interval = setInterval(() => {
    recentActivities.value = generateActivities('admin', 8)
  }, 5 * 60 * 1000)

  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>
