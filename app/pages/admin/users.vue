<template>
  <UContainer class="py-6">
    <div class="space-y-4">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">User Management</h1>
        <p class="text-gray-600 dark:text-gray-300">Manage system users and their permissions</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshUsers"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton color="primary" icon="i-heroicons-plus" @click="showAddUserModal = true">
          Add User
        </UButton>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalUsers }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-check-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ activeUsers }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-clock" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ pendingUsers }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-x-circle" class="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Inactive</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ inactiveUsers }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters and Search -->
    <UCard>
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="flex items-center space-x-3">
          <UInput
            v-model="searchQuery"
            placeholder="Search users..."
            icon="i-heroicons-magnifying-glass"
            class="w-64"
            @input="filterUsers"
          />
          <USelect
            v-model="selectedRole"
            :options="roleOptions"
            placeholder="All Roles"
            @change="filterUsers"
          />
          <USelect
            v-model="selectedStatus"
            :options="statusOptions"
            placeholder="All Status"
            @change="filterUsers"
          />
        </div>
        <div class="flex items-center space-x-2">
          <UButton variant="outline" icon="i-heroicons-funnel" @click="showFilters = !showFilters">
            Filters
          </UButton>
          <UButton variant="outline" icon="i-heroicons-arrow-down-tray">
            Export
          </UButton>
        </div>
      </div>
    </UCard>

    <!-- Users Table -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">Users ({{ filteredUsers.length }})</h3>
          <div class="flex items-center space-x-2">
            <UButton variant="ghost" size="sm" icon="i-heroicons-view-columns">
              Columns
            </UButton>
          </div>
        </div>
      </template>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                User
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Role
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Last Login
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="user in paginatedUsers" :key="user.id" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <UAvatar :src="user.avatar" :alt="user.name" size="sm" />
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ user.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getRoleColor(user.role)" variant="subtle">
                  {{ user.role }}
                </UBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getStatusColor(user.status)" variant="subtle">
                  {{ user.status }}
                </UBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(user.lastLogin) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <UButton variant="ghost" size="sm" icon="i-heroicons-pencil" @click="editUser(user)">
                    Edit
                  </UButton>
                  <UButton variant="ghost" size="sm" icon="i-heroicons-trash" color="red" @click="deleteUser(user)">
                    Delete
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex items-center justify-between px-6 py-3 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, filteredUsers.length) }} of {{ filteredUsers.length }} results
        </div>
        <div class="flex items-center space-x-2">
          <UButton
            variant="outline"
            size="sm"
            icon="i-heroicons-chevron-left"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            Previous
          </UButton>
          <span class="text-sm text-gray-500 dark:text-gray-400">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <UButton
            variant="outline"
            size="sm"
            icon="i-heroicons-chevron-right"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            Next
          </UButton>
        </div>
      </div>
    </UCard>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// Reactive data
const isRefreshing = ref(false)
const showAddUserModal = ref(false)
const showFilters = ref(false)
const searchQuery = ref('')
const selectedRole = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// Mock data
const users = ref([
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
    avatar: 'https://github.com/johndoe.png',
    lastLogin: new Date('2024-01-15T10:30:00')
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'Faculty',
    status: 'Active',
    avatar: 'https://github.com/janesmith.png',
    lastLogin: new Date('2024-01-14T15:45:00')
  },
  {
    id: 3,
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: 'Student',
    status: 'Active',
    avatar: 'https://github.com/bobjohnson.png',
    lastLogin: new Date('2024-01-13T09:15:00')
  },
  {
    id: 4,
    name: 'Alice Brown',
    email: '<EMAIL>',
    role: 'Faculty',
    status: 'Inactive',
    avatar: 'https://github.com/alicebrown.png',
    lastLogin: new Date('2024-01-10T14:20:00')
  },
  {
    id: 5,
    name: 'Charlie Wilson',
    email: '<EMAIL>',
    role: 'Student',
    status: 'Pending',
    avatar: 'https://github.com/charliewilson.png',
    lastLogin: null
  }
])

const filteredUsers = ref([...users.value])

// Options
const roleOptions = [
  { label: 'All Roles', value: '' },
  { label: 'Admin', value: 'Admin' },
  { label: 'Faculty', value: 'Faculty' },
  { label: 'Student', value: 'Student' }
]

const statusOptions = [
  { label: 'All Status', value: '' },
  { label: 'Active', value: 'Active' },
  { label: 'Inactive', value: 'Inactive' },
  { label: 'Pending', value: 'Pending' }
]

// Computed properties
const totalUsers = computed(() => users.value.length)
const activeUsers = computed(() => users.value.filter(u => u.status === 'Active').length)
const inactiveUsers = computed(() => users.value.filter(u => u.status === 'Inactive').length)
const pendingUsers = computed(() => users.value.filter(u => u.status === 'Pending').length)

const totalPages = computed(() => Math.ceil(filteredUsers.value.length / pageSize.value))

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredUsers.value.slice(start, end)
})

// Methods
const refreshUsers = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    // Simulate data refresh
    useToast().add({
      title: 'Users Refreshed',
      description: 'User data has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const filterUsers = () => {
  let filtered = [...users.value]
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user => 
      user.name.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query)
    )
  }
  
  if (selectedRole.value) {
    filtered = filtered.filter(user => user.role === selectedRole.value)
  }
  
  if (selectedStatus.value) {
    filtered = filtered.filter(user => user.status === selectedStatus.value)
  }
  
  filteredUsers.value = filtered
  currentPage.value = 1
}

const getRoleColor = (role) => {
  const colors = {
    'Admin': 'red',
    'Faculty': 'blue',
    'Student': 'green'
  }
  return colors[role] || 'gray'
}

const getStatusColor = (status) => {
  const colors = {
    'Active': 'green',
    'Inactive': 'red',
    'Pending': 'yellow'
  }
  return colors[status] || 'gray'
}

const formatDate = (date) => {
  if (!date) return 'Never'
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const editUser = (user) => {
  useToast().add({
    title: 'Edit User',
    description: `Editing ${user.name}`,
    icon: 'i-heroicons-pencil'
  })
}

const deleteUser = (user) => {
  useToast().add({
    title: 'Delete User',
    description: `Deleting ${user.name}`,
    icon: 'i-heroicons-trash',
    color: 'red'
  })
}

// Initialize
onMounted(() => {
  filterUsers()
})
</script>
