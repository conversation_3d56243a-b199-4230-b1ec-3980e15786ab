<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Tuition Management</h1>
          <p class="text-gray-600 dark:text-gray-300">Manage tuition rates, fees, and payment structures</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showTuitionModal = true"
          >
            Add Tuition Rate
          </UButton>
        </div>
      </div>

      <!-- Tuition Calculator -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Tuition Calculator</h2>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <USelect
            v-model="calculator.rateId"
            label="Tuition Rate"
            :options="tuitionRateOptions"
            placeholder="Select rate"
          />
          <UInput
            v-model.number="calculator.creditHours"
            label="Credit Hours"
            type="number"
            min="1"
            max="21"
            placeholder="12"
          />
          <div class="flex flex-col">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Total Cost</label>
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
              ${{ formatCurrency(calculatedCost) }}
            </div>
          </div>
          <div class="flex items-end">
            <UButton
              color="primary"
              @click="calculateTuition"
              :disabled="!calculator.rateId || !calculator.creditHours"
              class="w-full"
            >
              Calculate
            </UButton>
          </div>
        </div>

        <div v-if="selectedRate && calculatedCost > 0" class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h3 class="font-medium text-gray-900 dark:text-white mb-2">Cost Breakdown</h3>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">Tuition ({{ calculator.creditHours }} credit hours)</span>
              <span class="font-medium">${{ formatCurrency(tuitionCost) }}</span>
            </div>
            <div v-for="fee in selectedRate.fees" :key="fee.id" class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">{{ fee.name }}</span>
              <span class="font-medium">${{ formatCurrency(fee.amount) }}</span>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 pt-1 mt-2">
              <div class="flex justify-between font-semibold">
                <span>Total</span>
                <span>${{ formatCurrency(calculatedCost) }}</span>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Tuition Rates Table -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Tuition Rates</h2>
            <div class="flex items-center space-x-2">
              <USelect
                v-model="filters.type"
                :options="typeOptions"
                placeholder="All Types"
                size="sm"
              />
              <USelect
                v-model="filters.status"
                :options="statusOptions"
                placeholder="All Status"
                size="sm"
              />
            </div>
          </div>
        </template>

        <div v-if="loading" class="space-y-4">
          <USkeleton v-for="i in 5" :key="i" class="h-16" />
        </div>

        <div v-else-if="filteredTuitionRates.length === 0" class="text-center py-12">
          <UIcon name="i-heroicons-currency-dollar" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Tuition Rates Found</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Get started by adding your first tuition rate.
          </p>
          <UButton color="primary" @click="showTuitionModal = true">
            Add Tuition Rate
          </UButton>
        </div>

        <UTable
          v-else
          :rows="filteredTuitionRates"
          :columns="tuitionColumns"
          :loading="loading"
        >
          <template #name-data="{ row }">
            <div>
              <div class="font-medium text-gray-900 dark:text-white">{{ row.name }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.type }} • {{ row.residencyStatus.replace('_', ' ') }}</div>
            </div>
          </template>

          <template #rate-data="{ row }">
            <div>
              <div class="font-medium text-gray-900 dark:text-white">
                ${{ formatCurrency(row.creditHourRate) }}/credit
              </div>
              <div v-if="row.flatRate" class="text-sm text-gray-500 dark:text-gray-400">
                Flat rate: ${{ formatCurrency(row.flatRate) }}
              </div>
            </div>
          </template>

          <template #fees-data="{ row }">
            <div class="space-y-1">
              <div
                v-for="fee in row.fees.slice(0, 2)"
                :key="fee.id"
                class="text-sm"
              >
                <span class="text-gray-600 dark:text-gray-300">{{ fee.name }}: </span>
                <span class="font-medium">${{ formatCurrency(fee.amount) }}</span>
              </div>
              <div v-if="row.fees.length > 2" class="text-xs text-gray-500 dark:text-gray-400">
                +{{ row.fees.length - 2 }} more fees
              </div>
            </div>
          </template>

          <template #status-data="{ row }">
            <UBadge
              :color="getStatusColor(row.status)"
              variant="soft"
            >
              {{ row.status }}
            </UBadge>
          </template>

          <template #effectiveDate-data="{ row }">
            <div class="text-sm">
              {{ formatDate(row.effectiveDate) }}
              <div v-if="row.expirationDate" class="text-xs text-gray-500 dark:text-gray-400">
                Expires: {{ formatDate(row.expirationDate) }}
              </div>
            </div>
          </template>

          <template #actions-data="{ row }">
            <div class="flex items-center space-x-2">
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-eye"
                @click="viewTuitionRate(row.id)"
              />
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-pencil"
                @click="editTuitionRate(row.id)"
              />
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-trash"
                class="text-red-600 dark:text-red-400"
                @click="deleteTuitionRate(row.id)"
              />
            </div>
          </template>
        </UTable>
      </UCard>

      <!-- Fee Management -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Fee Management</h2>
            <UButton
              color="primary"
              size="sm"
              icon="i-heroicons-plus"
              @click="showFeeModal = true"
            >
              Add Fee
            </UButton>
          </div>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="fee in allFees"
            :key="fee.id"
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-medium text-gray-900 dark:text-white">{{ fee.name }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ fee.description }}</p>
                <div class="flex items-center space-x-2 mt-2">
                  <span class="text-lg font-bold text-green-600 dark:text-green-400">
                    ${{ formatCurrency(fee.amount) }}
                  </span>
                  <UBadge
                    :color="getFeeTypeColor(fee.type)"
                    variant="soft"
                    size="xs"
                  >
                    {{ fee.type.replace('_', ' ') }}
                  </UBadge>
                </div>
              </div>
              <div class="flex items-center space-x-1">
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-pencil"
                  @click="editFee(fee.id)"
                />
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-trash"
                  class="text-red-600 dark:text-red-400"
                  @click="deleteFee(fee.id)"
                />
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Add Tuition Rate Modal -->
    <UModal v-model="showTuitionModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Tuition Rate</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newTuitionRate.name"
            label="Rate Name"
            placeholder="e.g., Undergraduate In-State Tuition"
            required
          />

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <USelect
              v-model="newTuitionRate.type"
              label="Student Type"
              :options="typeOptions"
              required
            />
            <USelect
              v-model="newTuitionRate.residencyStatus"
              label="Residency Status"
              :options="residencyOptions"
              required
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model.number="newTuitionRate.creditHourRate"
              label="Credit Hour Rate"
              type="number"
              min="0"
              step="0.01"
              required
            />
            <UInput
              v-model.number="newTuitionRate.flatRate"
              label="Flat Rate (Optional)"
              type="number"
              min="0"
              step="0.01"
            />
          </div>

          <UInput
            v-model="newTuitionRate.effectiveDate"
            label="Effective Date"
            type="date"
            required
          />

          <USelect
            v-model="newTuitionRate.status"
            label="Status"
            :options="statusOptions"
            required
          />
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showTuitionModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addTuitionRate" :loading="adding">
              Add Rate
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Add Fee Modal -->
    <UModal v-model="showFeeModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Fee</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newFee.name"
            label="Fee Name"
            placeholder="e.g., Technology Fee"
            required
          />

          <UTextarea
            v-model="newFee.description"
            label="Description"
            placeholder="Enter fee description..."
            :rows="3"
          />

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model.number="newFee.amount"
              label="Amount"
              type="number"
              min="0"
              step="0.01"
              required
            />
            <USelect
              v-model="newFee.type"
              label="Fee Type"
              :options="feeTypeOptions"
              required
            />
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showFeeModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addFee" :loading="adding">
              Add Fee
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { tuitionRates, loading, calculateTuition: calcTuition, getTuitionRateById } = useFinancialData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Tuition Management'
})

// Reactive state
const showTuitionModal = ref(false)
const showFeeModal = ref(false)
const adding = ref(false)

// Calculator state
const calculator = ref({
  rateId: '',
  creditHours: 12
})
const calculatedCost = ref(0)
const tuitionCost = ref(0)

// Filters
const filters = ref({
  type: '',
  status: ''
})

// New tuition rate form
const newTuitionRate = ref({
  name: '',
  type: 'undergraduate',
  residencyStatus: 'in_state',
  creditHourRate: 0,
  flatRate: undefined,
  effectiveDate: '',
  status: 'active',
  fees: []
})

// New fee form
const newFee = ref({
  name: '',
  description: '',
  amount: 0,
  type: 'mandatory'
})

// Table columns
const tuitionColumns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'rate', label: 'Rate', sortable: true },
  { key: 'fees', label: 'Fees' },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'effectiveDate', label: 'Effective Date', sortable: true },
  { key: 'actions', label: 'Actions' }
]

// Computed properties
const filteredTuitionRates = computed(() => {
  return tuitionRates.value.filter(rate => {
    const matchesType = !filters.value.type || rate.type === filters.value.type
    const matchesStatus = !filters.value.status || rate.status === filters.value.status
    return matchesType && matchesStatus
  })
})

const tuitionRateOptions = computed(() => {
  return tuitionRates.value.map(rate => ({
    label: rate.name,
    value: rate.id
  }))
})

const selectedRate = computed(() => {
  return calculator.value.rateId ? getTuitionRateById(calculator.value.rateId) : null
})

const allFees = computed(() => {
  const fees = new Map()
  tuitionRates.value.forEach(rate => {
    rate.fees.forEach(fee => {
      fees.set(fee.id, fee)
    })
  })
  return Array.from(fees.values())
})

// Options for dropdowns
const typeOptions = [
  { label: 'Undergraduate', value: 'undergraduate' },
  { label: 'Graduate', value: 'graduate' },
  { label: 'Doctoral', value: 'doctoral' },
  { label: 'Continuing Education', value: 'continuing_education' }
]

const residencyOptions = [
  { label: 'In-State', value: 'in_state' },
  { label: 'Out-of-State', value: 'out_of_state' },
  { label: 'International', value: 'international' }
]

const statusOptions = [
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
  { label: 'Archived', value: 'archived' }
]

const feeTypeOptions = [
  { label: 'Mandatory', value: 'mandatory' },
  { label: 'Optional', value: 'optional' },
  { label: 'Program Specific', value: 'program_specific' }
]

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US').format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getStatusColor = (status: string) => {
  const colors = {
    'active': 'green',
    'inactive': 'yellow',
    'archived': 'gray'
  }
  return colors[status] || 'gray'
}

const getFeeTypeColor = (type: string) => {
  const colors = {
    'mandatory': 'red',
    'optional': 'blue',
    'program_specific': 'purple'
  }
  return colors[type] || 'gray'
}

// Methods
const refreshData = () => {
  console.log('Refreshing tuition data...')
}

const calculateTuition = () => {
  if (calculator.value.rateId && calculator.value.creditHours) {
    calculatedCost.value = calcTuition(calculator.value.rateId, calculator.value.creditHours)
    
    if (selectedRate.value) {
      tuitionCost.value = selectedRate.value.flatRate || 
        (selectedRate.value.creditHourRate * calculator.value.creditHours)
    }
  }
}

const viewTuitionRate = (rateId: string) => {
  console.log('View tuition rate:', rateId)
}

const editTuitionRate = (rateId: string) => {
  console.log('Edit tuition rate:', rateId)
}

const deleteTuitionRate = (rateId: string) => {
  if (confirm('Are you sure you want to delete this tuition rate?')) {
    console.log('Delete tuition rate:', rateId)
  }
}

const addTuitionRate = () => {
  console.log('Add tuition rate:', newTuitionRate.value)
  showTuitionModal.value = false
}

const editFee = (feeId: string) => {
  console.log('Edit fee:', feeId)
}

const deleteFee = (feeId: string) => {
  if (confirm('Are you sure you want to delete this fee?')) {
    console.log('Delete fee:', feeId)
  }
}

const addFee = () => {
  console.log('Add fee:', newFee.value)
  showFeeModal.value = false
}

// Auto-calculate when inputs change
watch([() => calculator.value.rateId, () => calculator.value.creditHours], () => {
  if (calculator.value.rateId && calculator.value.creditHours) {
    calculateTuition()
  }
})
</script>
