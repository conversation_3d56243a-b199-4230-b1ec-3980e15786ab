<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Financial Reports</h1>
          <p class="text-gray-600 dark:text-gray-300">Generate and manage financial reports and analytics</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showReportModal = true"
          >
            Generate Report
          </UButton>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ formatCurrency(financialStats.totalRevenue) }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-arrow-trending-up" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Revenue Growth</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ financialStats.revenueGrowth }}%</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-document-text" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Reports</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ reports.length }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-clock" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Reports</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ pendingReports }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Report Templates -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Report Templates</h2>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="template in reportTemplates"
            :key="template.id"
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
            @click="generateQuickReport(template)"
          >
            <div class="flex items-start space-x-3">
              <div
                class="w-10 h-10 rounded-lg flex items-center justify-center"
                :class="getTemplateIconClasses(template.type)"
              >
                <UIcon :name="getTemplateIcon(template.type)" class="w-5 h-5" />
              </div>
              <div class="flex-1">
                <h3 class="font-medium text-gray-900 dark:text-white">{{ template.name }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ template.description }}</p>
                <div class="flex items-center space-x-2 mt-2">
                  <UBadge
                    :color="getTemplateTypeColor(template.type)"
                    variant="soft"
                    size="xs"
                  >
                    {{ template.type }}
                  </UBadge>
                  <span class="text-xs text-gray-500 dark:text-gray-400">{{ template.period }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Reports List -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Generated Reports</h2>
            <div class="flex items-center space-x-2">
              <USelect
                v-model="filters.type"
                :options="typeOptions"
                placeholder="All Types"
                size="sm"
              />
              <USelect
                v-model="filters.status"
                :options="statusOptions"
                placeholder="All Status"
                size="sm"
              />
            </div>
          </div>
        </template>

        <div v-if="loading" class="space-y-4">
          <USkeleton v-for="i in 5" :key="i" class="h-16" />
        </div>

        <div v-else-if="filteredReports.length === 0" class="text-center py-12">
          <UIcon name="i-heroicons-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Reports Found</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Generate your first financial report to get started.
          </p>
          <UButton color="primary" @click="showReportModal = true">
            Generate Report
          </UButton>
        </div>

        <UTable
          v-else
          :rows="filteredReports"
          :columns="reportColumns"
          :loading="loading"
        >
          <template #title-data="{ row }">
            <div>
              <div class="font-medium text-gray-900 dark:text-white">{{ row.title }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ formatDateRange(row.startDate, row.endDate) }}
              </div>
            </div>
          </template>

          <template #type-data="{ row }">
            <UBadge
              :color="getReportTypeColor(row.type)"
              variant="soft"
            >
              {{ row.type.replace('_', ' ') }}
            </UBadge>
          </template>

          <template #status-data="{ row }">
            <div class="flex items-center space-x-2">
              <UBadge
                :color="getStatusColor(row.status)"
                variant="soft"
              >
                {{ row.status }}
              </UBadge>
              <div v-if="row.status === 'generating'" class="w-4 h-4">
                <UIcon name="i-heroicons-arrow-path" class="w-4 h-4 animate-spin text-blue-500" />
              </div>
            </div>
          </template>

          <template #generatedAt-data="{ row }">
            <div class="text-sm">
              {{ formatDate(row.generatedAt) }}
              <div class="text-xs text-gray-500 dark:text-gray-400">
                by {{ row.generatedBy }}
              </div>
            </div>
          </template>

          <template #actions-data="{ row }">
            <div class="flex items-center space-x-2">
              <UButton
                v-if="row.status === 'completed' && row.downloadUrl"
                size="xs"
                variant="ghost"
                icon="i-heroicons-arrow-down-tray"
                @click="downloadReport(row)"
              />
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-eye"
                @click="viewReport(row.id)"
              />
              <UButton
                v-if="row.status === 'completed'"
                size="xs"
                variant="ghost"
                icon="i-heroicons-share"
                @click="shareReport(row.id)"
              />
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-trash"
                class="text-red-600 dark:text-red-400"
                @click="deleteReport(row.id)"
              />
            </div>
          </template>
        </UTable>
      </UCard>
    </div>

    <!-- Generate Report Modal -->
    <UModal v-model="showReportModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Generate Financial Report</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newReport.title"
            label="Report Title"
            placeholder="e.g., Q4 2024 Revenue Analysis"
            required
          />

          <USelect
            v-model="newReport.type"
            label="Report Type"
            :options="typeOptions"
            required
          />

          <USelect
            v-model="newReport.period"
            label="Period"
            :options="periodOptions"
            required
          />

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput
              v-model="newReport.startDate"
              label="Start Date"
              type="date"
              required
            />
            <UInput
              v-model="newReport.endDate"
              label="End Date"
              type="date"
              required
            />
          </div>

          <div v-if="newReport.type === 'department_summary'" class="space-y-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Departments</label>
            <div class="grid grid-cols-2 gap-2">
              <div
                v-for="dept in departments"
                :key="dept"
                class="flex items-center space-x-2"
              >
                <UCheckbox
                  v-model="selectedDepartments"
                  :value="dept"
                  :label="dept"
                />
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showReportModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="generateReport" :loading="generating">
              Generate Report
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { reports, loading, financialStats, generateReport: createReport } = useFinancialData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Financial Reports'
})

// Reactive state
const showReportModal = ref(false)
const generating = ref(false)
const selectedDepartments = ref([])

// Filters
const filters = ref({
  type: '',
  status: ''
})

// New report form
const newReport = ref({
  title: '',
  type: 'revenue',
  period: 'monthly',
  startDate: '',
  endDate: '',
  generatedBy: 'Admin User',
  data: {}
})

// Report templates
const reportTemplates = ref([
  {
    id: '1',
    name: 'Monthly Revenue Report',
    description: 'Comprehensive revenue analysis for the current month',
    type: 'revenue',
    period: 'monthly'
  },
  {
    id: '2',
    name: 'Quarterly Expenses',
    description: 'Detailed expense breakdown by department and category',
    type: 'expenses',
    period: 'quarterly'
  },
  {
    id: '3',
    name: 'Budget Variance Analysis',
    description: 'Compare actual spending against budgeted amounts',
    type: 'budget_variance',
    period: 'yearly'
  },
  {
    id: '4',
    name: 'Enrollment Revenue',
    description: 'Revenue generated from student enrollments and tuition',
    type: 'enrollment_revenue',
    period: 'quarterly'
  },
  {
    id: '5',
    name: 'Department Summary',
    description: 'Financial overview by department with key metrics',
    type: 'department_summary',
    period: 'yearly'
  }
])

// Table columns
const reportColumns = [
  { key: 'title', label: 'Report', sortable: true },
  { key: 'type', label: 'Type', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'generatedAt', label: 'Generated', sortable: true },
  { key: 'actions', label: 'Actions' }
]

// Computed properties
const filteredReports = computed(() => {
  return reports.value.filter(report => {
    const matchesType = !filters.value.type || report.type === filters.value.type
    const matchesStatus = !filters.value.status || report.status === filters.value.status
    return matchesType && matchesStatus
  }).sort((a, b) => new Date(b.generatedAt).getTime() - new Date(a.generatedAt).getTime())
})

const pendingReports = computed(() => {
  return reports.value.filter(r => r.status === 'generating').length
})

// Options for dropdowns
const typeOptions = [
  { label: 'Revenue', value: 'revenue' },
  { label: 'Expenses', value: 'expenses' },
  { label: 'Budget Variance', value: 'budget_variance' },
  { label: 'Enrollment Revenue', value: 'enrollment_revenue' },
  { label: 'Department Summary', value: 'department_summary' }
]

const statusOptions = [
  { label: 'Generating', value: 'generating' },
  { label: 'Completed', value: 'completed' },
  { label: 'Error', value: 'error' }
]

const periodOptions = [
  { label: 'Monthly', value: 'monthly' },
  { label: 'Quarterly', value: 'quarterly' },
  { label: 'Yearly', value: 'yearly' },
  { label: 'Custom', value: 'custom' }
]

const departments = ['Computer Science', 'Mathematics', 'English', 'Physics', 'Chemistry', 'Biology']

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US').format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatDateRange = (startDate: string, endDate: string) => {
  const start = new Date(startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  const end = new Date(endDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
  return `${start} - ${end}`
}

const getStatusColor = (status: string) => {
  const colors = {
    'generating': 'blue',
    'completed': 'green',
    'error': 'red'
  }
  return colors[status] || 'gray'
}

const getReportTypeColor = (type: string) => {
  const colors = {
    'revenue': 'green',
    'expenses': 'red',
    'budget_variance': 'purple',
    'enrollment_revenue': 'blue',
    'department_summary': 'orange'
  }
  return colors[type] || 'gray'
}

const getTemplateTypeColor = (type: string) => {
  return getReportTypeColor(type)
}

const getTemplateIcon = (type: string) => {
  const icons = {
    'revenue': 'i-heroicons-arrow-trending-up',
    'expenses': 'i-heroicons-arrow-trending-down',
    'budget_variance': 'i-heroicons-chart-pie',
    'enrollment_revenue': 'i-heroicons-users',
    'department_summary': 'i-heroicons-building-office'
  }
  return icons[type] || 'i-heroicons-document-text'
}

const getTemplateIconClasses = (type: string) => {
  const classes = {
    'revenue': 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400',
    'expenses': 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
    'budget_variance': 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400',
    'enrollment_revenue': 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
    'department_summary': 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400'
  }
  return classes[type] || 'bg-gray-100 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400'
}

// Methods
const refreshData = () => {
  console.log('Refreshing reports data...')
}

const generateQuickReport = (template: any) => {
  const today = new Date()
  const startDate = new Date(today.getFullYear(), today.getMonth(), 1)
  const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)

  newReport.value = {
    title: template.name,
    type: template.type,
    period: template.period,
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0],
    generatedBy: 'Admin User',
    data: {}
  }

  generateReport()
}

const generateReport = async () => {
  generating.value = true
  try {
    await createReport(newReport.value)
    showReportModal.value = false
    // Reset form
    Object.assign(newReport.value, {
      title: '',
      type: 'revenue',
      period: 'monthly',
      startDate: '',
      endDate: '',
      generatedBy: 'Admin User',
      data: {}
    })
  } finally {
    generating.value = false
  }
}

const viewReport = (reportId: string) => {
  console.log('View report:', reportId)
}

const downloadReport = (report: any) => {
  console.log('Download report:', report.downloadUrl)
  // In a real app, this would trigger a file download
  window.open(report.downloadUrl, '_blank')
}

const shareReport = (reportId: string) => {
  console.log('Share report:', reportId)
}

const deleteReport = (reportId: string) => {
  if (confirm('Are you sure you want to delete this report?')) {
    console.log('Delete report:', reportId)
  }
}
</script>
