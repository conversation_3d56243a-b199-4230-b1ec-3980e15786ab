<template>
  <UContainer class="py-6">
    <div class="space-y-6">
      <!-- <PERSON> Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Budget Management</h1>
          <p class="text-gray-600 dark:text-gray-300">Manage departmental budgets and financial planning</p>
        </div>
        <div class="flex items-center space-x-3">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-arrow-path"
            @click="refreshData"
            :loading="loading"
          >
            Refresh
          </UButton>
          <UButton
            color="primary"
            icon="i-heroicons-plus"
            @click="showBudgetModal = true"
          >
            Create Budget
          </UButton>
        </div>
      </div>

      <!-- Financial Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-banknotes" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Budget</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ formatCurrency(financialStats.totalBudget) }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-arrow-trending-up" class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Spent</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ formatCurrency(financialStats.totalSpent) }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-chart-pie" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Budget Utilization</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ Math.round(financialStats.budgetUtilization) }}%</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Approvals</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ financialStats.pendingApprovals }}</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Search and Filters -->
      <UCard>
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Search Input -->
            <div class="lg:col-span-2">
              <UInput
                v-model="filters.search"
                placeholder="Search budgets by name or department..."
                icon="i-heroicons-magnifying-glass"
                size="sm"
              />
            </div>

            <!-- Department Filter -->
            <USelect
              v-model="filters.department"
              :options="departmentOptions"
              placeholder="All Departments"
              size="sm"
            />

            <!-- Status Filter -->
            <USelect
              v-model="filters.status"
              :options="statusOptions"
              placeholder="All Status"
              size="sm"
            />
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-300">
              Showing {{ filteredBudgets.length }} of {{ budgets.length }} budgets
            </span>
            <UButton
              variant="ghost"
              size="sm"
              @click="clearFilters"
              v-if="hasActiveFilters"
            >
              Clear Filters
            </UButton>
          </div>
        </div>
      </UCard>

      <!-- Budgets Grid -->
      <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <USkeleton v-for="i in 6" :key="i" class="h-64" />
      </div>

      <div v-else-if="filteredBudgets.length === 0" class="text-center py-12">
        <UIcon name="i-heroicons-banknotes" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Budgets Found</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          {{ hasActiveFilters ? 'Try adjusting your search criteria.' : 'Get started by creating your first budget.' }}
        </p>
        <UButton v-if="!hasActiveFilters" color="primary" @click="showBudgetModal = true">
          Create Budget
        </UButton>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <UCard
          v-for="budget in paginatedBudgets"
          :key="budget.id"
          class="hover:shadow-lg transition-shadow duration-200"
        >
          <template #header>
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                  {{ budget.name }}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">{{ budget.department }}</p>
              </div>
              <UBadge
                :color="getStatusColor(budget.status)"
                variant="soft"
              >
                {{ budget.status }}
              </UBadge>
            </div>
          </template>

          <div class="space-y-4">
            <!-- Budget Overview -->
            <div class="space-y-2">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-300">Total Budget</span>
                <span class="font-medium text-gray-900 dark:text-white">${{ formatCurrency(budget.totalBudget) }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-300">Allocated</span>
                <span class="font-medium text-gray-900 dark:text-white">${{ formatCurrency(budget.allocatedAmount) }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-300">Spent</span>
                <span class="font-medium text-gray-900 dark:text-white">${{ formatCurrency(budget.spentAmount) }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-300">Remaining</span>
                <span class="font-medium" :class="budget.remainingAmount >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  ${{ formatCurrency(Math.abs(budget.remainingAmount)) }}{{ budget.remainingAmount < 0 ? ' over' : '' }}
                </span>
              </div>
            </div>

            <!-- Budget Progress -->
            <div class="space-y-2">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-300">Budget Utilization</span>
                <span class="font-medium text-gray-900 dark:text-white">
                  {{ Math.round((budget.spentAmount / budget.allocatedAmount) * 100) }}%
                </span>
              </div>
              <UProgress
                :value="(budget.spentAmount / budget.allocatedAmount) * 100"
                :color="getBudgetUtilizationColor(budget.spentAmount, budget.allocatedAmount)"
              />
            </div>

            <!-- Categories Summary -->
            <div class="space-y-2">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">Top Categories</h4>
              <div class="space-y-1">
                <div
                  v-for="category in budget.categories.slice(0, 2)"
                  :key="category.id"
                  class="flex items-center justify-between text-xs"
                >
                  <span class="text-gray-600 dark:text-gray-300 truncate">{{ category.name }}</span>
                  <span class="font-medium text-gray-900 dark:text-white">${{ formatCurrency(category.spentAmount) }}</span>
                </div>
                <div v-if="budget.categories.length > 2" class="text-xs text-gray-500 dark:text-gray-400">
                  +{{ budget.categories.length - 2 }} more categories
                </div>
              </div>
            </div>

            <!-- Fiscal Year -->
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Fiscal Year {{ budget.fiscalYear }}
            </div>
          </div>

          <template #footer>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-eye"
                  @click="viewBudget(budget.id)"
                >
                  View
                </UButton>
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-pencil"
                  @click="editBudget(budget.id)"
                >
                  Edit
                </UButton>
              </div>
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-trash"
                class="text-red-600 dark:text-red-400"
                @click="deleteBudgetConfirm(budget.id)"
              >
                Delete
              </UButton>
            </div>
          </template>
        </UCard>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex justify-center">
        <UPagination
          v-model="currentPage"
          :page-count="itemsPerPage"
          :total="filteredBudgets.length"
          :max="5"
        />
      </div>
    </div>

    <!-- Create Budget Modal -->
    <UModal v-model="showBudgetModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Create New Budget</h3>
        </template>

        <div class="space-y-4">
          <UInput
            v-model="newBudget.name"
            label="Budget Name"
            placeholder="e.g., Computer Science Department Budget FY2025"
            required
          />

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <USelect
              v-model="newBudget.department"
              label="Department"
              :options="departmentOptions"
              placeholder="Select department"
              required
            />
            <UInput
              v-model.number="newBudget.fiscalYear"
              label="Fiscal Year"
              type="number"
              min="2024"
              max="2030"
              required
            />
          </div>

          <UInput
            v-model.number="newBudget.totalBudget"
            label="Total Budget"
            type="number"
            min="0"
            step="1000"
            required
          />

          <USelect
            v-model="newBudget.status"
            label="Status"
            :options="statusOptions"
            required
          />
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="outline" @click="showBudgetModal = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="createBudget" :loading="creating">
              Create Budget
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </UContainer>
</template>

<script setup lang="ts">
const { budgets, loading, financialStats, searchBudgets, addBudget, deleteBudget } = useFinancialData()

// Page metadata
definePageMeta({
  layout: 'admin',
  title: 'Budget Management'
})

// Reactive state
const showBudgetModal = ref(false)
const creating = ref(false)
const currentPage = ref(1)
const itemsPerPage = 9

// Filters
const filters = ref({
  search: '',
  department: '',
  status: ''
})

// New budget form
const newBudget = ref({
  name: '',
  department: '',
  departmentId: '',
  fiscalYear: new Date().getFullYear() + 1,
  totalBudget: 0,
  allocatedAmount: 0,
  spentAmount: 0,
  remainingAmount: 0,
  categories: [],
  status: 'draft' as const
})

// Computed properties
const filteredBudgets = computed(() => {
  return searchBudgets(filters.value.search, filters.value)
})

const paginatedBudgets = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredBudgets.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredBudgets.value.length / itemsPerPage)
})

const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(filter => filter !== '')
})

// Options for dropdowns
const departmentOptions = computed(() => {
  const departments = [...new Set(budgets.value.map(b => b.department))]
  return departments.map(dept => ({ label: dept, value: dept }))
})

const statusOptions = [
  { label: 'Draft', value: 'draft' },
  { label: 'Approved', value: 'approved' },
  { label: 'Active', value: 'active' },
  { label: 'Closed', value: 'closed' }
]

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US').format(amount)
}

const getStatusColor = (status: string) => {
  const colors = {
    'draft': 'yellow',
    'approved': 'blue',
    'active': 'green',
    'closed': 'gray'
  }
  return colors[status] || 'gray'
}

const getBudgetUtilizationColor = (spent: number, allocated: number) => {
  const percentage = (spent / allocated) * 100
  if (percentage >= 95) return 'red'
  if (percentage >= 80) return 'orange'
  if (percentage >= 60) return 'yellow'
  return 'green'
}

// Methods
const refreshData = () => {
  console.log('Refreshing budget data...')
}

const clearFilters = () => {
  filters.value = {
    search: '',
    department: '',
    status: ''
  }
  currentPage.value = 1
}

const viewBudget = (budgetId: string) => {
  navigateTo(`/admin/financial/budget/${budgetId}`)
}

const editBudget = (budgetId: string) => {
  navigateTo(`/admin/financial/budget/${budgetId}/edit`)
}

const deleteBudgetConfirm = async (budgetId: string) => {
  if (confirm('Are you sure you want to delete this budget?')) {
    await deleteBudget(budgetId)
  }
}

const createBudget = async () => {
  creating.value = true
  try {
    await addBudget({
      ...newBudget.value,
      remainingAmount: newBudget.value.totalBudget - newBudget.value.spentAmount
    })
    showBudgetModal.value = false
    // Reset form
    Object.assign(newBudget.value, {
      name: '',
      department: '',
      departmentId: '',
      fiscalYear: new Date().getFullYear() + 1,
      totalBudget: 0,
      allocatedAmount: 0,
      spentAmount: 0,
      remainingAmount: 0,
      categories: [],
      status: 'draft' as const
    })
  } finally {
    creating.value = false
  }
}

// Watch for filter changes to reset pagination
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })
</script>
