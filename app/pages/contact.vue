<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Contact Us</h1>
        <p class="text-lg text-gray-600 dark:text-gray-300">
          Get in touch with Springfield College - we're here to help
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Contact Form -->
        <UCard>
          <template #header>
            <h2 class="text-xl font-semibold">Send us a Message</h2>
          </template>
          <form @submit.prevent="submitForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  First Name *
                </label>
                <UInput v-model="form.firstName" placeholder="Enter your first name" required />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Last Name *
                </label>
                <UInput v-model="form.lastName" placeholder="Enter your last name" required />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email Address *
              </label>
              <UInput v-model="form.email" type="email" placeholder="Enter your email" required />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Phone Number
              </label>
              <UInput v-model="form.phone" placeholder="Enter your phone number" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                I am a... *
              </label>
              <USelect v-model="form.category" :options="categoryOptions" placeholder="Select category" required />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Subject *
              </label>
              <UInput v-model="form.subject" placeholder="Enter subject" required />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Message *
              </label>
              <UTextarea
                v-model="form.message"
                placeholder="Enter your message..."
                rows="5"
                required
              />
            </div>

            <div class="flex items-center">
              <UCheckbox v-model="form.newsletter" />
              <label class="ml-2 text-sm text-gray-600 dark:text-gray-300">
                Subscribe to our newsletter for updates and announcements
              </label>
            </div>

            <UButton type="submit" color="primary" size="lg" :loading="isSubmitting" class="w-full">
              Send Message
            </UButton>
          </form>
        </UCard>

        <!-- Contact Information -->
        <div class="space-y-8">
          <!-- Main Contact -->
          <UCard>
            <template #header>
              <h2 class="text-xl font-semibold">Get in Touch</h2>
            </template>
            <div class="space-y-6">
              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-map-pin" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">Address</h3>
                  <p class="text-gray-600 dark:text-gray-300">
                    123 College Avenue<br>
                    Springfield, IL 62701<br>
                    United States
                  </p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-phone" class="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">Phone</h3>
                  <p class="text-gray-600 dark:text-gray-300">
                    Main: <a href="tel:+12175551234" class="hover:text-primary-600 dark:hover:text-primary-400">(*************</a><br>
                    Admissions: <a href="tel:+12175551235" class="hover:text-primary-600 dark:hover:text-primary-400">(*************</a>
                  </p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-envelope" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">Email</h3>
                  <p class="text-gray-600 dark:text-gray-300">
                    General: <a href="mailto:<EMAIL>" class="hover:text-primary-600 dark:hover:text-primary-400"><EMAIL></a><br>
                    Admissions: <a href="mailto:<EMAIL>" class="hover:text-primary-600 dark:hover:text-primary-400"><EMAIL></a>
                  </p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-clock" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">Office Hours</h3>
                  <p class="text-gray-600 dark:text-gray-300">
                    Monday - Friday: 8:00 AM - 6:00 PM<br>
                    Saturday: 9:00 AM - 4:00 PM<br>
                    Sunday: Closed
                  </p>
                </div>
              </div>
            </div>
          </UCard>

          <!-- Department Contacts -->
          <UCard>
            <template #header>
              <h2 class="text-xl font-semibold">Department Contacts</h2>
            </template>
            <div class="space-y-4">
              <div v-for="dept in departments" :key="dept.name" class="flex items-center justify-between">
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">{{ dept.name }}</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ dept.description }}</p>
                </div>
                <div class="text-right">
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ dept.phone }}</p>
                  <a :href="`mailto:${dept.email}`" class="text-sm text-primary-600 dark:text-primary-400 hover:underline">
                    {{ dept.email }}
                  </a>
                </div>
              </div>
            </div>
          </UCard>

          <!-- Emergency Contact -->
          <UCard>
            <template #header>
              <h2 class="text-xl font-semibold text-red-600 dark:text-red-400">Emergency Contact</h2>
            </template>
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <UIcon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-red-600 dark:text-red-400" />
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">Campus Security</h3>
                  <p class="text-gray-600 dark:text-gray-300">24/7 Emergency Line</p>
                </div>
              </div>
              <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                <p class="text-lg font-bold text-red-600 dark:text-red-400">
                  Emergency: <a href="tel:911" class="hover:underline">911</a>
                </p>
                <p class="text-sm text-red-600 dark:text-red-400">
                  Campus Security: <a href="tel:+12175559999" class="hover:underline">(*************</a>
                </p>
              </div>
            </div>
          </UCard>

          <!-- Social Media -->
          <UCard>
            <template #header>
              <h2 class="text-xl font-semibold">Follow Us</h2>
            </template>
            <div class="flex space-x-4">
              <UButton variant="outline" icon="i-heroicons-globe-alt" @click="openSocialMedia('website')">
                Website
              </UButton>
              <UButton variant="outline" icon="i-heroicons-chat-bubble-left-right" @click="openSocialMedia('facebook')">
                Facebook
              </UButton>
              <UButton variant="outline" icon="i-heroicons-camera" @click="openSocialMedia('instagram')">
                Instagram
              </UButton>
              <UButton variant="outline" icon="i-heroicons-megaphone" @click="openSocialMedia('twitter')">
                Twitter
              </UButton>
            </div>
          </UCard>
        </div>
      </div>

      <!-- Back to Home -->
      <div class="text-center mt-12">
        <UButton to="/" variant="outline" icon="i-heroicons-arrow-left">
          Back to Home
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'default'
})

// Reactive data
const isSubmitting = ref(false)

const form = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  category: '',
  subject: '',
  message: '',
  newsletter: false
})

// Options
const categoryOptions = [
  { label: 'Prospective Student', value: 'prospective-student' },
  { label: 'Current Student', value: 'current-student' },
  { label: 'Parent/Guardian', value: 'parent' },
  { label: 'Alumni', value: 'alumni' },
  { label: 'Faculty/Staff', value: 'faculty-staff' },
  { label: 'Media/Press', value: 'media' },
  { label: 'Business Partner', value: 'business' },
  { label: 'Other', value: 'other' }
]

// Department data
const departments = [
  {
    name: 'Admissions',
    description: 'Application and enrollment',
    phone: '(*************',
    email: '<EMAIL>'
  },
  {
    name: 'Financial Aid',
    description: 'Scholarships and financial assistance',
    phone: '(*************',
    email: '<EMAIL>'
  },
  {
    name: 'Registrar',
    description: 'Academic records and transcripts',
    phone: '(*************',
    email: '<EMAIL>'
  },
  {
    name: 'Student Services',
    description: 'Student support and resources',
    phone: '(*************',
    email: '<EMAIL>'
  },
  {
    name: 'IT Support',
    description: 'Technical assistance',
    phone: '(*************',
    email: '<EMAIL>'
  }
]

// Methods
const submitForm = async () => {
  isSubmitting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    useToast().add({
      title: 'Message Sent',
      description: 'Thank you for contacting us. We\'ll get back to you soon!',
      icon: 'i-heroicons-check-circle'
    })
    
    // Reset form
    form.value = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      category: '',
      subject: '',
      message: '',
      newsletter: false
    }
  } catch (error) {
    useToast().add({
      title: 'Error',
      description: 'Failed to send message. Please try again.',
      color: 'red',
      icon: 'i-heroicons-x-circle'
    })
  } finally {
    isSubmitting.value = false
  }
}

const openSocialMedia = (platform) => {
  useToast().add({
    title: 'Social Media',
    description: `Opening ${platform} page...`,
    icon: 'i-heroicons-arrow-top-right-on-square'
  })
}

// SEO
useHead({
  title: 'Contact Us - Springfield College',
  meta: [
    { name: 'description', content: 'Get in touch with Springfield College. Find contact information, office hours, and send us a message.' }
  ]
})
</script>
