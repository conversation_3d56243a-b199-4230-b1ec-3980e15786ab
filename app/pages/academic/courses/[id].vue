<template>
  <UContainer class="py-6">
    <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="space-y-6">
      <div class="animate-pulse">
        <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
        <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
      </div>
      <UCard class="animate-pulse">
        <div class="space-y-4">
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
        </div>
      </UCard>
    </div>

    <!-- Course Content -->
    <div v-else-if="course" class="space-y-6">
      <!-- Course Header -->
      <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-2">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              {{ course.code }}
            </h1>
            <UBadge
              :color="getAvailabilityColor(course.availability)"
              variant="soft"
              size="lg"
            >
              {{ course.availability }}
            </UBadge>
          </div>
          <h2 class="text-xl text-gray-700 dark:text-gray-300 mb-4">
            {{ course.title }}
          </h2>
          
          <!-- Quick Info -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-gray-500" />
              <span class="text-sm">{{ course.credits }} Credits</span>
            </div>
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-user" class="w-5 h-5 text-gray-500" />
              <span class="text-sm">{{ course.instructor }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-clock" class="w-5 h-5 text-gray-500" />
              <span class="text-sm">{{ course.schedule }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-map-pin" class="w-5 h-5 text-gray-500" />
              <span class="text-sm">{{ course.location }}</span>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 lg:flex-col lg:w-48">
          <UButton
            color="primary"
            size="lg"
            icon="i-heroicons-plus-circle"
            :disabled="course.availability === 'Full'"
            @click="registerForCourse"
            block
          >
            {{ course.availability === 'Full' ? 'Course Full' : 
               course.availability === 'Waitlist' ? 'Join Waitlist' : 'Register' }}
          </UButton>
          <UButton
            variant="outline"
            size="lg"
            icon="i-heroicons-heart"
            @click="toggleWishlist"
            block
          >
            {{ isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist' }}
          </UButton>
          <UButton
            variant="outline"
            size="lg"
            icon="i-heroicons-share"
            @click="shareCourse"
            block
          >
            Share Course
          </UButton>
        </div>
      </div>

      <!-- Course Tabs -->
      <UTabs :items="tabItems" v-model="activeTab">
        <!-- Overview Tab -->
        <template #overview>
          <div class="space-y-6">
            <!-- Course Description -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">Course Description</h3>
              </template>
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                {{ course.description }}
              </p>
            </UCard>

            <!-- Learning Objectives -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">Learning Objectives</h3>
              </template>
              <ul class="space-y-2">
                <li v-for="objective in course.objectives" :key="objective" class="flex items-start space-x-2">
                  <UIcon name="i-heroicons-check-circle" class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <span class="text-gray-700 dark:text-gray-300">{{ objective }}</span>
                </li>
              </ul>
            </UCard>

            <!-- Prerequisites -->
            <UCard v-if="course.prerequisites?.length">
              <template #header>
                <h3 class="text-lg font-semibold">Prerequisites</h3>
              </template>
              <div class="space-y-2">
                <NuxtLink
                  v-for="prereq in course.prerequisites"
                  :key="prereq.code"
                  :to="`/academic/courses/${prereq.id}`"
                  class="inline-block"
                >
                  <UBadge
                    variant="outline"
                    class="hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors cursor-pointer"
                  >
                    {{ prereq.code }}: {{ prereq.title }}
                  </UBadge>
                </NuxtLink>
              </div>
            </UCard>

            <!-- Course Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <UCard>
                <div class="text-center">
                  <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {{ course.enrolled }}/{{ course.capacity }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">Enrollment</div>
                </div>
              </UCard>
              <UCard>
                <div class="text-center">
                  <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {{ course.rating || 'N/A' }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">Average Rating</div>
                </div>
              </UCard>
              <UCard>
                <div class="text-center">
                  <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {{ course.difficulty || 'N/A' }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">Difficulty Level</div>
                </div>
              </UCard>
            </div>
          </div>
        </template>

        <!-- Schedule Tab -->
        <template #schedule>
          <div class="space-y-6">
            <!-- Meeting Times -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">Meeting Times</h3>
              </template>
              <div class="space-y-4">
                <div v-for="meeting in course.meetings" :key="meeting.id" class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <div class="font-medium">{{ meeting.type }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ meeting.days }} {{ meeting.time }}</div>
                  </div>
                  <div class="text-right">
                    <div class="font-medium">{{ meeting.location }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ meeting.instructor }}</div>
                  </div>
                </div>
              </div>
            </UCard>

            <!-- Important Dates -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">Important Dates</h3>
              </template>
              <div class="space-y-3">
                <div v-for="date in course.importantDates" :key="date.event" class="flex items-center justify-between">
                  <span class="font-medium">{{ date.event }}</span>
                  <span class="text-gray-600 dark:text-gray-400">{{ formatDate(date.date) }}</span>
                </div>
              </div>
            </UCard>
          </div>
        </template>

        <!-- Instructor Tab -->
        <template #instructor>
          <div class="space-y-6">
            <!-- Instructor Profile -->
            <UCard>
              <div class="flex items-start space-x-4">
                <UAvatar
                  :src="course.instructorDetails?.avatar"
                  :alt="course.instructor"
                  size="xl"
                />
                <div class="flex-1">
                  <h3 class="text-xl font-semibold">{{ course.instructor }}</h3>
                  <p class="text-gray-600 dark:text-gray-400">{{ course.instructorDetails?.title }}</p>
                  <p class="text-gray-600 dark:text-gray-400">{{ course.instructorDetails?.department }}</p>
                  
                  <div class="mt-4 space-y-2">
                    <div class="flex items-center space-x-2">
                      <UIcon name="i-heroicons-envelope" class="w-4 h-4" />
                      <span class="text-sm">{{ course.instructorDetails?.email }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <UIcon name="i-heroicons-phone" class="w-4 h-4" />
                      <span class="text-sm">{{ course.instructorDetails?.phone }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <UIcon name="i-heroicons-map-pin" class="w-4 h-4" />
                      <span class="text-sm">{{ course.instructorDetails?.office }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </UCard>

            <!-- Office Hours -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">Office Hours</h3>
              </template>
              <div class="space-y-2">
                <div v-for="hours in course.instructorDetails?.officeHours" :key="hours.day" class="flex justify-between">
                  <span class="font-medium">{{ hours.day }}</span>
                  <span class="text-gray-600 dark:text-gray-400">{{ hours.time }}</span>
                </div>
              </div>
            </UCard>

            <!-- Bio -->
            <UCard v-if="course.instructorDetails?.bio">
              <template #header>
                <h3 class="text-lg font-semibold">Biography</h3>
              </template>
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                {{ course.instructorDetails.bio }}
              </p>
            </UCard>
          </div>
        </template>

        <!-- Resources Tab -->
        <template #resources>
          <div class="space-y-6">
            <!-- Required Materials -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">Required Materials</h3>
              </template>
              <div class="space-y-3">
                <div v-for="material in course.materials" :key="material.title" class="flex items-start space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <UIcon :name="getMaterialIcon(material.type)" class="w-5 h-5 text-gray-500 mt-1" />
                  <div class="flex-1">
                    <div class="font-medium">{{ material.title }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ material.author }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ material.type }}</div>
                  </div>
                  <div class="text-right">
                    <div class="font-medium">${{ material.price }}</div>
                    <UButton
                      v-if="material.url"
                      size="xs"
                      variant="outline"
                      :to="material.url"
                      target="_blank"
                    >
                      View
                    </UButton>
                  </div>
                </div>
              </div>
            </UCard>

            <!-- Syllabus -->
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold">Syllabus</h3>
                  <UButton
                    size="sm"
                    variant="outline"
                    icon="i-heroicons-arrow-down-tray"
                    @click="downloadSyllabus"
                  >
                    Download
                  </UButton>
                </div>
              </template>
              <div class="space-y-4">
                <div v-for="week in course.syllabus" :key="week.week" class="border-l-4 border-primary-500 pl-4">
                  <div class="font-medium">Week {{ week.week }}: {{ week.topic }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">{{ week.description }}</div>
                  <div v-if="week.assignments?.length" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Assignments: {{ week.assignments.join(', ') }}
                  </div>
                </div>
              </div>
            </UCard>
          </div>
        </template>
      </UTabs>
    </div>

    <!-- Error State -->
    <UCard v-else-if="error">
      <div class="text-center py-8">
        <UIcon name="i-heroicons-exclamation-triangle" class="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Course Not Found</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
        <UButton to="/academic/courses">
          Back to Course Catalog
        </UButton>
      </div>
    </UCard>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

const route = useRoute()
const courseId = route.params.id

// Reactive data
const course = ref(null)
const isLoading = ref(true)
const error = ref(null)
const activeTab = ref(0)
const isInWishlist = ref(false)

// Tab configuration
const tabItems = [
  { label: 'Overview', slot: 'overview' },
  { label: 'Schedule', slot: 'schedule' },
  { label: 'Instructor', slot: 'instructor' },
  { label: 'Resources', slot: 'resources' }
]

// Methods
const getAvailabilityColor = (availability) => {
  const colors = {
    'Available': 'green',
    'Limited': 'yellow',
    'Full': 'red',
    'Waitlist': 'orange'
  }
  return colors[availability] || 'gray'
}

const getMaterialIcon = (type) => {
  const icons = {
    'Textbook': 'i-heroicons-book-open',
    'Software': 'i-heroicons-computer-desktop',
    'Online Resource': 'i-heroicons-globe-alt',
    'Lab Manual': 'i-heroicons-document-text'
  }
  return icons[type] || 'i-heroicons-document'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const registerForCourse = () => {
  navigateTo(`/academic/registration?course=${courseId}`)
}

const toggleWishlist = () => {
  isInWishlist.value = !isInWishlist.value
  useToast().add({
    title: isInWishlist.value ? 'Added to Wishlist' : 'Removed from Wishlist',
    description: `${course.value.code} has been ${isInWishlist.value ? 'added to' : 'removed from'} your wishlist`,
    icon: isInWishlist.value ? 'i-heroicons-heart' : 'i-heroicons-heart-slash'
  })
}

const shareCourse = () => {
  if (navigator.share) {
    navigator.share({
      title: `${course.value.code}: ${course.value.title}`,
      text: course.value.description,
      url: window.location.href
    })
  } else {
    // Fallback to clipboard
    navigator.clipboard.writeText(window.location.href)
    useToast().add({
      title: 'Link Copied',
      description: 'Course link has been copied to clipboard',
      icon: 'i-heroicons-clipboard'
    })
  }
}

const downloadSyllabus = () => {
  // Mock download functionality
  useToast().add({
    title: 'Download Started',
    description: 'Syllabus download has been initiated',
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const loadCourse = async () => {
  isLoading.value = true
  error.value = null

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock course data - replace with actual API call
    const mockCourses = {
      'cs101': {
        id: 'cs101',
        code: 'CS 101',
        title: 'Introduction to Computer Science',
        description: 'An introduction to the fundamental concepts of computer science including programming, algorithms, and data structures. This course provides students with a solid foundation in computational thinking and problem-solving techniques.',
        instructor: 'Dr. Sarah Johnson',
        department: 'CS',
        level: '100',
        credits: 3,
        schedule: 'MWF 9:00-10:00 AM',
        location: 'Science Building Room 201',
        availability: 'Available',
        semester: 'fall-2024',
        capacity: 30,
        enrolled: 25,
        rating: 4.5,
        difficulty: 'Moderate',
        objectives: [
          'Understand fundamental programming concepts and syntax',
          'Develop problem-solving skills using computational thinking',
          'Learn basic data structures and their applications',
          'Implement simple algorithms and analyze their efficiency',
          'Practice debugging and testing techniques'
        ],
        prerequisites: [
          {
            id: 'math101',
            code: 'MATH 101',
            title: 'College Algebra'
          }
        ],
        meetings: [
          {
            id: 1,
            type: 'Lecture',
            days: 'Monday, Wednesday, Friday',
            time: '9:00-10:00 AM',
            location: 'Science Building Room 201',
            instructor: 'Dr. Sarah Johnson'
          },
          {
            id: 2,
            type: 'Lab',
            days: 'Tuesday',
            time: '2:00-4:00 PM',
            location: 'Computer Lab A',
            instructor: 'TA: Mike Chen'
          }
        ],
        importantDates: [
          { event: 'First Day of Class', date: '2024-08-26' },
          { event: 'Midterm Exam', date: '2024-10-15' },
          { event: 'Final Project Due', date: '2024-12-10' },
          { event: 'Final Exam', date: '2024-12-17' }
        ],
        instructorDetails: {
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-1.2.1&auto=format&fit=facepad&facepad=2&w=256&h=256&q=80',
          title: 'Associate Professor of Computer Science',
          department: 'Department of Computer Science',
          email: '<EMAIL>',
          phone: '(*************',
          office: 'Science Building Room 305',
          bio: 'Dr. Sarah Johnson has been teaching computer science for over 10 years. She specializes in algorithms, data structures, and software engineering. She has published numerous papers in top-tier conferences and is passionate about making computer science accessible to all students.',
          officeHours: [
            { day: 'Monday', time: '2:00-4:00 PM' },
            { day: 'Wednesday', time: '2:00-4:00 PM' },
            { day: 'Friday', time: '10:00-12:00 PM' }
          ]
        },
        materials: [
          {
            title: 'Introduction to Programming with Python',
            author: 'John Smith',
            type: 'Textbook',
            price: 89.99,
            url: 'https://example.com/textbook'
          },
          {
            title: 'PyCharm IDE',
            author: 'JetBrains',
            type: 'Software',
            price: 0,
            url: 'https://jetbrains.com/pycharm'
          },
          {
            title: 'Python Documentation',
            author: 'Python Software Foundation',
            type: 'Online Resource',
            price: 0,
            url: 'https://docs.python.org'
          }
        ],
        syllabus: [
          {
            week: 1,
            topic: 'Introduction to Programming',
            description: 'Basic concepts, variables, and data types',
            assignments: ['Reading Assignment 1', 'Lab 1']
          },
          {
            week: 2,
            topic: 'Control Structures',
            description: 'Conditionals and loops',
            assignments: ['Programming Assignment 1', 'Lab 2']
          },
          {
            week: 3,
            topic: 'Functions',
            description: 'Function definition, parameters, and return values',
            assignments: ['Programming Assignment 2', 'Lab 3']
          },
          {
            week: 4,
            topic: 'Data Structures',
            description: 'Lists, tuples, and dictionaries',
            assignments: ['Programming Assignment 3', 'Lab 4']
          }
        ]
      }
    }

    course.value = mockCourses[courseId]

    if (!course.value) {
      error.value = 'Course not found. Please check the course ID and try again.'
    }
  } catch (err) {
    error.value = 'Failed to load course details. Please try again later.'
    console.error('Error loading course:', err)
  } finally {
    isLoading.value = false
  }
}

// Load course on mount
onMounted(() => {
  loadCourse()
})

// Watch for route changes
watch(() => route.params.id, () => {
  loadCourse()
})
</script>
