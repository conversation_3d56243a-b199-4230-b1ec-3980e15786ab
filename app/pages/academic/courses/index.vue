<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Course Catalog</h1>
        <p class="text-gray-600 dark:text-gray-300">Browse and search available courses</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshCourses"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton
          color="primary"
          icon="i-heroicons-plus"
          to="/academic/courses/new"
        >
          Add Course
        </UButton>
      </div>
    </div>

    <!-- Search and Filters -->
    <UCard>
      <div class="space-y-4">
        <!-- Search Bar -->
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <UInput
              v-model="searchQuery"
              placeholder="Search courses by title, code, or instructor..."
              icon="i-heroicons-magnifying-glass"
              size="lg"
              @input="debouncedSearch"
            />
          </div>
          <UButton
            variant="outline"
            icon="i-heroicons-funnel"
            @click="showFilters = !showFilters"
            :color="hasActiveFilters ? 'primary' : 'gray'"
          >
            Filters
            <UBadge v-if="activeFilterCount > 0" size="xs" color="primary" class="ml-2">
              {{ activeFilterCount }}
            </UBadge>
          </UButton>
        </div>

        <!-- Filter Panel -->
        <div v-if="showFilters" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <USelect
            v-model="filters.department"
            :options="departmentOptions"
            placeholder="All Departments"
            @change="applyFilters"
          />
          <USelect
            v-model="filters.level"
            :options="levelOptions"
            placeholder="Course Level"
            @change="applyFilters"
          />
          <USelect
            v-model="filters.credits"
            :options="creditOptions"
            placeholder="Credits"
            @change="applyFilters"
          />
          <USelect
            v-model="filters.semester"
            :options="semesterOptions"
            placeholder="Semester"
            @change="applyFilters"
          />
        </div>

        <!-- Sort and View Options -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex items-center space-x-4">
            <USelect
              v-model="sortBy"
              :options="sortOptions"
              @change="applySorting"
            />
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-600 dark:text-gray-400">View:</span>
              <UButtonGroup>
                <UButton
                  :variant="viewMode === 'grid' ? 'solid' : 'outline'"
                  icon="i-heroicons-squares-2x2"
                  size="sm"
                  @click="viewMode = 'grid'"
                />
                <UButton
                  :variant="viewMode === 'list' ? 'solid' : 'outline'"
                  icon="i-heroicons-list-bullet"
                  size="sm"
                  @click="viewMode = 'list'"
                />
              </UButtonGroup>
            </div>
          </div>
          <div class="text-sm text-gray-600 dark:text-gray-400">
            Showing {{ filteredCourses.length }} of {{ totalCourses }} courses
          </div>
        </div>
      </div>
    </UCard>

    <!-- Course Grid/List -->
    <div v-if="isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard v-for="i in 6" :key="i" class="animate-pulse">
        <div class="space-y-3">
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          <div class="space-y-2">
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Grid View -->
    <div v-else-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard
        v-for="course in paginatedCourses"
        :key="course.id"
        class="hover:shadow-lg transition-shadow cursor-pointer"
        @click="navigateToCourse(course.id)"
      >
        <div class="space-y-4">
          <!-- Course Header -->
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ course.code }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ course.title }}</p>
            </div>
            <UBadge
              :color="getAvailabilityColor(course.availability)"
              variant="soft"
              size="sm"
            >
              {{ course.availability }}
            </UBadge>
          </div>

          <!-- Course Info -->
          <div class="space-y-2">
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <UIcon name="i-heroicons-user" class="w-4 h-4 mr-2" />
              {{ course.instructor }}
            </div>
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <UIcon name="i-heroicons-clock" class="w-4 h-4 mr-2" />
              {{ course.schedule }}
            </div>
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <UIcon name="i-heroicons-academic-cap" class="w-4 h-4 mr-2" />
              {{ course.credits }} Credits
            </div>
          </div>

          <!-- Course Description -->
          <p class="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
            {{ course.description }}
          </p>

          <!-- Course Actions -->
          <div class="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center space-x-2">
              <UBadge variant="outline" size="sm">{{ course.department }}</UBadge>
              <UBadge variant="outline" size="sm">Level {{ course.level }}</UBadge>
            </div>
            <UButton
              size="sm"
              variant="outline"
              @click.stop="quickViewCourse(course)"
            >
              Quick View
            </UButton>
          </div>
        </div>
      </UCard>
    </div>

    <!-- List View -->
    <UCard v-else>
      <UTable
        :rows="paginatedCourses"
        :columns="tableColumns"
        @select="navigateToCourse"
      >
        <template #code-data="{ row }">
          <div class="font-medium">{{ row.code }}</div>
        </template>
        <template #title-data="{ row }">
          <div>
            <div class="font-medium text-gray-900 dark:text-white">{{ row.title }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.instructor }}</div>
          </div>
        </template>
        <template #schedule-data="{ row }">
          <div class="text-sm">{{ row.schedule }}</div>
        </template>
        <template #availability-data="{ row }">
          <UBadge
            :color="getAvailabilityColor(row.availability)"
            variant="soft"
            size="sm"
          >
            {{ row.availability }}
          </UBadge>
        </template>
        <template #actions-data="{ row }">
          <div class="flex items-center space-x-2">
            <UButton
              size="sm"
              variant="ghost"
              icon="i-heroicons-eye"
              @click="quickViewCourse(row)"
            />
            <UButton
              size="sm"
              variant="ghost"
              icon="i-heroicons-arrow-top-right-on-square"
              @click="navigateToCourse(row.id)"
            />
          </div>
        </template>
      </UTable>
    </UCard>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="flex justify-center">
      <UPagination
        v-model="currentPage"
        :page-count="itemsPerPage"
        :total="filteredCourses.length"
        @update:model-value="handlePageChange"
      />
    </div>

    <!-- Quick View Modal -->
    <UModal v-model="showQuickView" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard v-if="selectedCourse">
        <template #header>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold">{{ selectedCourse.code }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ selectedCourse.title }}</p>
            </div>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showQuickView = false"
            />
          </div>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Instructor:</span>
              <p class="text-sm">{{ selectedCourse.instructor }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Credits:</span>
              <p class="text-sm">{{ selectedCourse.credits }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Schedule:</span>
              <p class="text-sm">{{ selectedCourse.schedule }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Availability:</span>
              <UBadge
                :color="getAvailabilityColor(selectedCourse.availability)"
                variant="soft"
                size="sm"
              >
                {{ selectedCourse.availability }}
              </UBadge>
            </div>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Description:</span>
            <p class="text-sm mt-1">{{ selectedCourse.description }}</p>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              variant="outline"
              @click="showQuickView = false"
            >
              Close
            </UButton>
            <UButton
              color="primary"
              @click="navigateToCourse(selectedCourse.id)"
            >
              View Details
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// Reactive data
const searchQuery = ref('')
const showFilters = ref(false)
const viewMode = ref('grid')
const currentPage = ref(1)
const itemsPerPage = ref(12)
const isLoading = ref(false)
const isRefreshing = ref(false)
const showQuickView = ref(false)
const selectedCourse = ref(null)

// Filters
const filters = ref({
  department: '',
  level: '',
  credits: '',
  semester: ''
})

const sortBy = ref('title')

// Mock data - will be replaced with API calls
const courses = ref([])
const totalCourses = ref(0)

// Options for filters and sorting
const departmentOptions = [
  { label: 'All Departments', value: '' },
  { label: 'Computer Science', value: 'CS' },
  { label: 'Mathematics', value: 'MATH' },
  { label: 'English', value: 'ENG' },
  { label: 'Physics', value: 'PHYS' },
  { label: 'Chemistry', value: 'CHEM' },
  { label: 'Biology', value: 'BIO' },
  { label: 'History', value: 'HIST' },
  { label: 'Psychology', value: 'PSYC' }
]

const levelOptions = [
  { label: 'All Levels', value: '' },
  { label: 'Level 100', value: '100' },
  { label: 'Level 200', value: '200' },
  { label: 'Level 300', value: '300' },
  { label: 'Level 400', value: '400' }
]

const creditOptions = [
  { label: 'All Credits', value: '' },
  { label: '1 Credit', value: '1' },
  { label: '2 Credits', value: '2' },
  { label: '3 Credits', value: '3' },
  { label: '4 Credits', value: '4' }
]

const semesterOptions = [
  { label: 'All Semesters', value: '' },
  { label: 'Fall 2024', value: 'fall-2024' },
  { label: 'Spring 2025', value: 'spring-2025' },
  { label: 'Summer 2025', value: 'summer-2025' }
]

const sortOptions = [
  { label: 'Course Title', value: 'title' },
  { label: 'Course Code', value: 'code' },
  { label: 'Department', value: 'department' },
  { label: 'Credits', value: 'credits' },
  { label: 'Instructor', value: 'instructor' }
]

const tableColumns = [
  { key: 'code', label: 'Code' },
  { key: 'title', label: 'Course' },
  { key: 'credits', label: 'Credits' },
  { key: 'schedule', label: 'Schedule' },
  { key: 'availability', label: 'Status' },
  { key: 'actions', label: 'Actions' }
]

// Computed properties
const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(filter => filter !== '')
})

const activeFilterCount = computed(() => {
  return Object.values(filters.value).filter(filter => filter !== '').length
})

const filteredCourses = computed(() => {
  let result = [...courses.value]

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(course =>
      course.title.toLowerCase().includes(query) ||
      course.code.toLowerCase().includes(query) ||
      course.instructor.toLowerCase().includes(query) ||
      course.description.toLowerCase().includes(query)
    )
  }

  // Apply filters
  if (filters.value.department) {
    result = result.filter(course => course.department === filters.value.department)
  }
  if (filters.value.level) {
    result = result.filter(course => course.level === filters.value.level)
  }
  if (filters.value.credits) {
    result = result.filter(course => course.credits.toString() === filters.value.credits)
  }
  if (filters.value.semester) {
    result = result.filter(course => course.semester === filters.value.semester)
  }

  // Apply sorting
  result.sort((a, b) => {
    const aValue = a[sortBy.value]
    const bValue = b[sortBy.value]

    if (typeof aValue === 'string') {
      return aValue.localeCompare(bValue)
    }
    return aValue - bValue
  })

  return result
})

const totalPages = computed(() => {
  return Math.ceil(filteredCourses.value.length / itemsPerPage.value)
})

const paginatedCourses = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredCourses.value.slice(start, end)
})

// Methods
const debouncedSearch = useDebounceFn(() => {
  currentPage.value = 1
}, 300)

const applyFilters = () => {
  currentPage.value = 1
}

const applySorting = () => {
  currentPage.value = 1
}

const handlePageChange = (page) => {
  currentPage.value = page
  // Scroll to top when page changes
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

const getAvailabilityColor = (availability) => {
  const colors = {
    'Available': 'green',
    'Limited': 'yellow',
    'Full': 'red',
    'Waitlist': 'orange'
  }
  return colors[availability] || 'gray'
}

const navigateToCourse = (courseId) => {
  navigateTo(`/academic/courses/${courseId}`)
}

const quickViewCourse = (course) => {
  selectedCourse.value = course
  showQuickView.value = true
}

const refreshCourses = async () => {
  isRefreshing.value = true
  try {
    await loadCourses()
    useToast().add({
      title: 'Courses Refreshed',
      description: 'Course catalog has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const loadCourses = async () => {
  isLoading.value = true
  try {
    // Simulate API call - replace with actual API
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock course data
    courses.value = [
      {
        id: 'cs101',
        code: 'CS 101',
        title: 'Introduction to Computer Science',
        description: 'An introduction to the fundamental concepts of computer science including programming, algorithms, and data structures.',
        instructor: 'Dr. Sarah Johnson',
        department: 'CS',
        level: '100',
        credits: 3,
        schedule: 'MWF 9:00-10:00 AM',
        availability: 'Available',
        semester: 'fall-2024',
        capacity: 30,
        enrolled: 25
      },
      {
        id: 'math201',
        code: 'MATH 201',
        title: 'Calculus II',
        description: 'Continuation of Calculus I covering integration techniques, applications of integrals, and infinite series.',
        instructor: 'Prof. Michael Chen',
        department: 'MATH',
        level: '200',
        credits: 4,
        schedule: 'TTh 11:00-12:30 PM',
        availability: 'Limited',
        semester: 'fall-2024',
        capacity: 25,
        enrolled: 22
      },
      {
        id: 'eng102',
        code: 'ENG 102',
        title: 'Composition and Literature',
        description: 'Advanced composition course focusing on critical reading and analytical writing skills.',
        instructor: 'Dr. Emily Rodriguez',
        department: 'ENG',
        level: '100',
        credits: 3,
        schedule: 'MWF 2:00-3:00 PM',
        availability: 'Available',
        semester: 'fall-2024',
        capacity: 20,
        enrolled: 15
      },
      {
        id: 'phys301',
        code: 'PHYS 301',
        title: 'Quantum Mechanics',
        description: 'Introduction to quantum mechanics including wave functions, operators, and quantum systems.',
        instructor: 'Dr. Robert Kim',
        department: 'PHYS',
        level: '300',
        credits: 4,
        schedule: 'TTh 1:00-2:30 PM',
        availability: 'Full',
        semester: 'fall-2024',
        capacity: 15,
        enrolled: 15
      },
      {
        id: 'chem101',
        code: 'CHEM 101',
        title: 'General Chemistry',
        description: 'Introduction to chemical principles including atomic structure, bonding, and chemical reactions.',
        instructor: 'Prof. Lisa Wang',
        department: 'CHEM',
        level: '100',
        credits: 4,
        schedule: 'MWF 10:00-11:00 AM + Lab',
        availability: 'Waitlist',
        semester: 'fall-2024',
        capacity: 24,
        enrolled: 26
      },
      {
        id: 'bio201',
        code: 'BIO 201',
        title: 'Cell Biology',
        description: 'Study of cellular structure and function including metabolism, genetics, and cell division.',
        instructor: 'Dr. James Thompson',
        department: 'BIO',
        level: '200',
        credits: 3,
        schedule: 'TTh 9:30-11:00 AM',
        availability: 'Available',
        semester: 'fall-2024',
        capacity: 28,
        enrolled: 20
      }
    ]

    totalCourses.value = courses.value.length
  } finally {
    isLoading.value = false
  }
}

// Load courses on mount
onMounted(() => {
  loadCourses()
})
</script>
