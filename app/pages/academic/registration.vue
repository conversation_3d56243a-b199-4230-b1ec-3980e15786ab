<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Course Registration</h1>
        <p class="text-gray-600 dark:text-gray-300">Register for courses and build your schedule</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshData"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-document-arrow-down"
          @click="exportSchedule"
        >
          Export Schedule
        </UButton>
      </div>
    </div>

    <!-- Registration Progress -->
    <UCard>
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">Registration Progress</h3>
          <UBadge
            :color="getRegistrationStatusColor(registrationStatus)"
            variant="soft"
            size="lg"
          >
            {{ registrationStatus }}
          </UBadge>
        </div>
        
        <!-- Progress Steps -->
        <div class="flex items-center space-x-4">
          <div
            v-for="(step, index) in registrationSteps"
            :key="step.id"
            class="flex items-center"
          >
            <div
              class="flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium"
              :class="[
                step.completed ? 'bg-green-500 text-white' :
                step.current ? 'bg-primary-500 text-white' :
                'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
              ]"
            >
              <UIcon
                v-if="step.completed"
                name="i-heroicons-check"
                class="w-4 h-4"
              />
              <span v-else>{{ index + 1 }}</span>
            </div>
            <span
              class="ml-2 text-sm"
              :class="[
                step.completed || step.current ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'
              ]"
            >
              {{ step.title }}
            </span>
            <div
              v-if="index < registrationSteps.length - 1"
              class="w-8 h-0.5 mx-4"
              :class="[
                registrationSteps[index + 1].completed || registrationSteps[index + 1].current ? 
                'bg-primary-500' : 'bg-gray-200 dark:bg-gray-700'
              ]"
            ></div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Course Search and Selection -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Course Search -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Find Courses</h3>
          </template>
          
          <div class="space-y-4">
            <!-- Search Bar -->
            <UInput
              v-model="searchQuery"
              placeholder="Search courses by title, code, or instructor..."
              icon="i-heroicons-magnifying-glass"
              size="lg"
              @input="debouncedSearch"
            />
            
            <!-- Quick Filters -->
            <div class="flex flex-wrap gap-2">
              <UButton
                v-for="filter in quickFilters"
                :key="filter.value"
                :variant="activeQuickFilter === filter.value ? 'solid' : 'outline'"
                size="sm"
                @click="applyQuickFilter(filter.value)"
              >
                {{ filter.label }}
              </UButton>
            </div>
            
            <!-- Advanced Filters -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <USelect
                v-model="filters.department"
                :options="departmentOptions"
                placeholder="Department"
                @change="filterCourses"
              />
              <USelect
                v-model="filters.timeSlot"
                :options="timeSlotOptions"
                placeholder="Time Slot"
                @change="filterCourses"
              />
              <USelect
                v-model="filters.credits"
                :options="creditOptions"
                placeholder="Credits"
                @change="filterCourses"
              />
            </div>
          </div>
        </UCard>

        <!-- Available Courses -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">Available Courses</h3>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {{ filteredCourses.length }} courses found
              </span>
            </div>
          </template>
          
          <div class="space-y-3 max-h-96 overflow-y-auto">
            <div
              v-for="course in filteredCourses"
              :key="course.id"
              class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      {{ course.code }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ course.title }}</p>
                  </div>
                  <UBadge
                    :color="getAvailabilityColor(course.availability)"
                    variant="soft"
                    size="sm"
                  >
                    {{ course.availability }}
                  </UBadge>
                </div>
                
                <div class="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                  <span>{{ course.instructor }}</span>
                  <span>{{ course.schedule }}</span>
                  <span>{{ course.credits }} credits</span>
                </div>
                
                <!-- Prerequisites Warning -->
                <div v-if="!checkPrerequisites(course)" class="mt-2">
                  <UAlert
                    color="yellow"
                    variant="soft"
                    title="Prerequisites Not Met"
                    :description="`Required: ${course.prerequisites?.map(p => p.code).join(', ')}`"
                    icon="i-heroicons-exclamation-triangle"
                  />
                </div>
                
                <!-- Schedule Conflict Warning -->
                <div v-if="hasScheduleConflict(course)" class="mt-2">
                  <UAlert
                    color="red"
                    variant="soft"
                    title="Schedule Conflict"
                    description="This course conflicts with your current schedule"
                    icon="i-heroicons-exclamation-triangle"
                  />
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <UButton
                  size="sm"
                  variant="ghost"
                  icon="i-heroicons-eye"
                  @click="viewCourseDetails(course)"
                />
                <UButton
                  size="sm"
                  :disabled="!canAddCourse(course)"
                  @click="addCourseToSchedule(course)"
                  :loading="addingCourse === course.id"
                >
                  {{ isInSchedule(course) ? 'Added' : 'Add' }}
                </UButton>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Schedule Builder -->
      <div class="space-y-6">
        <!-- Current Schedule -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">My Schedule</h3>
              <UBadge variant="soft" color="primary">
                {{ selectedCourses.length }} courses
              </UBadge>
            </div>
          </template>
          
          <div class="space-y-3">
            <div
              v-for="course in selectedCourses"
              :key="course.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-900 dark:text-white truncate">
                  {{ course.code }}
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">{{ course.title }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ course.schedule }}</p>
              </div>
              <UButton
                size="xs"
                color="red"
                variant="ghost"
                icon="i-heroicons-x-mark"
                @click="removeCourseFromSchedule(course)"
              />
            </div>
            
            <div v-if="selectedCourses.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
              <UIcon name="i-heroicons-calendar" class="w-8 h-8 mx-auto mb-2" />
              <p class="text-sm">No courses selected</p>
            </div>
          </div>
        </UCard>

        <!-- Schedule Summary -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Schedule Summary</h3>
          </template>
          
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Total Courses:</span>
              <span class="font-medium">{{ selectedCourses.length }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Total Credits:</span>
              <span class="font-medium">{{ totalCredits }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Status:</span>
              <UBadge
                :color="totalCredits >= 12 ? 'green' : totalCredits >= 6 ? 'yellow' : 'red'"
                variant="soft"
                size="sm"
              >
                {{ getEnrollmentStatus(totalCredits) }}
              </UBadge>
            </div>
          </div>
        </UCard>

        <!-- Weekly Schedule View -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Weekly Schedule</h3>
          </template>
          
          <div class="space-y-2">
            <div
              v-for="day in weekDays"
              :key="day"
              class="border border-gray-200 dark:border-gray-700 rounded-lg p-2"
            >
              <div class="font-medium text-sm text-gray-900 dark:text-white mb-1">{{ day }}</div>
              <div class="space-y-1">
                <div
                  v-for="course in getCoursesForDay(day)"
                  :key="course.id"
                  class="text-xs p-1 bg-primary-100 dark:bg-primary-900/20 text-primary-800 dark:text-primary-200 rounded"
                >
                  {{ course.code }} - {{ course.timeSlot }}
                </div>
                <div v-if="getCoursesForDay(day).length === 0" class="text-xs text-gray-400">
                  No classes
                </div>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Registration Actions -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Complete Registration</h3>
          </template>
          
          <div class="space-y-4">
            <div class="text-sm text-gray-600 dark:text-gray-400">
              Review your schedule and complete registration when ready.
            </div>
            
            <div class="space-y-2">
              <UButton
                color="primary"
                size="lg"
                block
                :disabled="selectedCourses.length === 0 || hasConflicts"
                @click="proceedToRegistration"
                :loading="isRegistering"
              >
                Register for {{ selectedCourses.length }} Course{{ selectedCourses.length !== 1 ? 's' : '' }}
              </UButton>
              
              <UButton
                variant="outline"
                size="lg"
                block
                @click="saveAsDraft"
                :disabled="selectedCourses.length === 0"
              >
                Save as Draft
              </UButton>
            </div>
            
            <div v-if="hasConflicts" class="text-sm text-red-600 dark:text-red-400">
              <UIcon name="i-heroicons-exclamation-triangle" class="w-4 h-4 inline mr-1" />
              Please resolve schedule conflicts before registering
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- Registration Confirmation Modal -->
    <UModal v-model="showConfirmation" :ui="{ width: 'sm:max-w-lg' }">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">Confirm Registration</h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showConfirmation = false"
            />
          </div>
        </template>

        <div class="space-y-4">
          <p class="text-gray-700 dark:text-gray-300">
            You are about to register for the following courses:
          </p>
          
          <div class="space-y-2">
            <div
              v-for="course in selectedCourses"
              :key="course.id"
              class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div>
                <div class="font-medium">{{ course.code }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{ course.title }}</div>
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ course.credits }} credits
              </div>
            </div>
          </div>
          
          <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
            <div class="flex justify-between font-medium">
              <span>Total Credits:</span>
              <span>{{ totalCredits }}</span>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              variant="outline"
              @click="showConfirmation = false"
            >
              Cancel
            </UButton>
            <UButton
              color="primary"
              @click="confirmRegistration"
              :loading="isRegistering"
            >
              Confirm Registration
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

const route = useRoute()

// Reactive data
const searchQuery = ref('')
const activeQuickFilter = ref('')
const selectedCourses = ref([])
const availableCourses = ref([])
const isRefreshing = ref(false)
const isRegistering = ref(false)
const addingCourse = ref(null)
const showConfirmation = ref(false)

// Filters
const filters = ref({
  department: '',
  timeSlot: '',
  credits: ''
})

// Registration status
const registrationStatus = ref('In Progress')
const registrationSteps = ref([
  { id: 1, title: 'Course Selection', completed: false, current: true },
  { id: 2, title: 'Schedule Review', completed: false, current: false },
  { id: 3, title: 'Registration', completed: false, current: false },
  { id: 4, title: 'Confirmation', completed: false, current: false }
])

// Options
const quickFilters = [
  { label: 'All Courses', value: '' },
  { label: 'Core Requirements', value: 'core' },
  { label: 'Electives', value: 'elective' },
  { label: 'Available Seats', value: 'available' },
  { label: 'Online', value: 'online' }
]

const departmentOptions = [
  { label: 'All Departments', value: '' },
  { label: 'Computer Science', value: 'CS' },
  { label: 'Mathematics', value: 'MATH' },
  { label: 'English', value: 'ENG' },
  { label: 'Physics', value: 'PHYS' },
  { label: 'Chemistry', value: 'CHEM' }
]

const timeSlotOptions = [
  { label: 'Any Time', value: '' },
  { label: 'Morning (8-12)', value: 'morning' },
  { label: 'Afternoon (12-17)', value: 'afternoon' },
  { label: 'Evening (17-21)', value: 'evening' }
]

const creditOptions = [
  { label: 'Any Credits', value: '' },
  { label: '1 Credit', value: '1' },
  { label: '2 Credits', value: '2' },
  { label: '3 Credits', value: '3' },
  { label: '4 Credits', value: '4' }
]

const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']

// Computed properties
const filteredCourses = computed(() => {
  let result = [...availableCourses.value]

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(course =>
      course.title.toLowerCase().includes(query) ||
      course.code.toLowerCase().includes(query) ||
      course.instructor.toLowerCase().includes(query)
    )
  }

  // Apply quick filter
  if (activeQuickFilter.value) {
    switch (activeQuickFilter.value) {
      case 'core':
        result = result.filter(course => course.type === 'core')
        break
      case 'elective':
        result = result.filter(course => course.type === 'elective')
        break
      case 'available':
        result = result.filter(course => course.availability === 'Available')
        break
      case 'online':
        result = result.filter(course => course.format === 'online')
        break
    }
  }

  // Apply filters
  if (filters.value.department) {
    result = result.filter(course => course.department === filters.value.department)
  }
  if (filters.value.timeSlot) {
    result = result.filter(course => course.timeSlot === filters.value.timeSlot)
  }
  if (filters.value.credits) {
    result = result.filter(course => course.credits.toString() === filters.value.credits)
  }

  return result
})

const totalCredits = computed(() => {
  return selectedCourses.value.reduce((total, course) => total + course.credits, 0)
})

const hasConflicts = computed(() => {
  return selectedCourses.value.some(course => hasScheduleConflict(course))
})

// Methods
const debouncedSearch = useDebounceFn(() => {
  // Search is handled by computed property
}, 300)

const applyQuickFilter = (filterValue) => {
  activeQuickFilter.value = activeQuickFilter.value === filterValue ? '' : filterValue
}

const filterCourses = () => {
  // Filtering is handled by computed property
}

const getAvailabilityColor = (availability) => {
  const colors = {
    'Available': 'green',
    'Limited': 'yellow',
    'Full': 'red',
    'Waitlist': 'orange'
  }
  return colors[availability] || 'gray'
}

const getRegistrationStatusColor = (status) => {
  const colors = {
    'In Progress': 'yellow',
    'Completed': 'green',
    'Pending': 'blue',
    'Failed': 'red'
  }
  return colors[status] || 'gray'
}

const getEnrollmentStatus = (credits) => {
  if (credits >= 12) return 'Full-time'
  if (credits >= 6) return 'Part-time'
  return 'Under-enrolled'
}

const checkPrerequisites = (course) => {
  // Mock prerequisite check - in real app, check against student's completed courses
  if (!course.prerequisites || course.prerequisites.length === 0) return true

  // For demo, randomly return true/false for some courses
  const mockCompletedCourses = ['MATH 101', 'ENG 101', 'CS 100']
  return course.prerequisites.every(prereq =>
    mockCompletedCourses.includes(prereq.code)
  )
}

const hasScheduleConflict = (course) => {
  // Check if course conflicts with already selected courses
  return selectedCourses.value.some(selectedCourse => {
    if (selectedCourse.id === course.id) return false

    // Simple time conflict check (in real app, use proper time parsing)
    const courseTime = course.schedule.toLowerCase()
    const selectedTime = selectedCourse.schedule.toLowerCase()

    // Check for day overlap
    const courseDays = extractDays(courseTime)
    const selectedDays = extractDays(selectedTime)
    const hasCommonDay = courseDays.some(day => selectedDays.includes(day))

    if (!hasCommonDay) return false

    // Check for time overlap (simplified)
    const courseTimeSlot = extractTimeSlot(courseTime)
    const selectedTimeSlot = extractTimeSlot(selectedTime)

    return courseTimeSlot === selectedTimeSlot
  })
}

const extractDays = (schedule) => {
  const days = []
  if (schedule.includes('m')) days.push('monday')
  if (schedule.includes('t') && !schedule.includes('th')) days.push('tuesday')
  if (schedule.includes('w')) days.push('wednesday')
  if (schedule.includes('th')) days.push('thursday')
  if (schedule.includes('f')) days.push('friday')
  return days
}

const extractTimeSlot = (schedule) => {
  if (schedule.includes('8:') || schedule.includes('9:') || schedule.includes('10:') || schedule.includes('11:')) {
    return 'morning'
  }
  if (schedule.includes('12:') || schedule.includes('1:') || schedule.includes('2:') || schedule.includes('3:') || schedule.includes('4:')) {
    return 'afternoon'
  }
  return 'evening'
}

const canAddCourse = (course) => {
  return !isInSchedule(course) &&
         course.availability !== 'Full' &&
         checkPrerequisites(course) &&
         !hasScheduleConflict(course)
}

const isInSchedule = (course) => {
  return selectedCourses.value.some(selected => selected.id === course.id)
}

const addCourseToSchedule = async (course) => {
  if (!canAddCourse(course)) return

  addingCourse.value = course.id

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))

    selectedCourses.value.push(course)

    useToast().add({
      title: 'Course Added',
      description: `${course.code} has been added to your schedule`,
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    addingCourse.value = null
  }
}

const removeCourseFromSchedule = (course) => {
  const index = selectedCourses.value.findIndex(selected => selected.id === course.id)
  if (index > -1) {
    selectedCourses.value.splice(index, 1)

    useToast().add({
      title: 'Course Removed',
      description: `${course.code} has been removed from your schedule`,
      icon: 'i-heroicons-x-circle'
    })
  }
}

const getCoursesForDay = (day) => {
  return selectedCourses.value.filter(course => {
    const schedule = course.schedule.toLowerCase()
    const dayInitial = day.toLowerCase().charAt(0)

    if (day === 'Thursday') {
      return schedule.includes('th')
    }

    return schedule.includes(dayInitial) && !schedule.includes('th')
  }).map(course => ({
    ...course,
    timeSlot: extractTimeFromSchedule(course.schedule)
  }))
}

const extractTimeFromSchedule = (schedule) => {
  // Extract time portion from schedule string
  const timeMatch = schedule.match(/(\d{1,2}:\d{2}(?:-\d{1,2}:\d{2})?(?:\s*[AP]M)?)/i)
  return timeMatch ? timeMatch[1] : ''
}

const viewCourseDetails = (course) => {
  navigateTo(`/academic/courses/${course.id}`)
}

const proceedToRegistration = () => {
  if (selectedCourses.value.length === 0 || hasConflicts.value) return

  showConfirmation.value = true
}

const confirmRegistration = async () => {
  isRegistering.value = true

  try {
    // Simulate registration API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Update registration status
    registrationStatus.value = 'Completed'
    registrationSteps.value.forEach(step => {
      step.completed = true
      step.current = false
    })

    useToast().add({
      title: 'Registration Successful',
      description: `Successfully registered for ${selectedCourses.value.length} courses`,
      icon: 'i-heroicons-check-circle'
    })

    showConfirmation.value = false

    // Redirect to confirmation page or dashboard
    setTimeout(() => {
      navigateTo('/student/courses/enrolled')
    }, 2000)

  } catch (error) {
    useToast().add({
      title: 'Registration Failed',
      description: 'There was an error processing your registration. Please try again.',
      color: 'red',
      icon: 'i-heroicons-exclamation-triangle'
    })
  } finally {
    isRegistering.value = false
  }
}

const saveAsDraft = () => {
  // Save current selection as draft
  localStorage.setItem('registrationDraft', JSON.stringify(selectedCourses.value))

  useToast().add({
    title: 'Draft Saved',
    description: 'Your course selection has been saved as a draft',
    icon: 'i-heroicons-bookmark'
  })
}

const refreshData = async () => {
  isRefreshing.value = true
  try {
    await loadAvailableCourses()
    useToast().add({
      title: 'Data Refreshed',
      description: 'Course information has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const exportSchedule = () => {
  // Mock export functionality
  useToast().add({
    title: 'Export Started',
    description: 'Schedule export has been initiated',
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const loadAvailableCourses = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock course data
    availableCourses.value = [
      {
        id: 'cs101',
        code: 'CS 101',
        title: 'Introduction to Computer Science',
        instructor: 'Dr. Sarah Johnson',
        department: 'CS',
        credits: 3,
        schedule: 'MWF 9:00-10:00 AM',
        timeSlot: 'morning',
        availability: 'Available',
        type: 'core',
        format: 'in-person',
        prerequisites: [{ code: 'MATH 101', title: 'College Algebra' }]
      },
      {
        id: 'math201',
        code: 'MATH 201',
        title: 'Calculus II',
        instructor: 'Prof. Michael Chen',
        department: 'MATH',
        credits: 4,
        schedule: 'TTh 11:00-12:30 PM',
        timeSlot: 'morning',
        availability: 'Limited',
        type: 'core',
        format: 'in-person',
        prerequisites: [{ code: 'MATH 101', title: 'Calculus I' }]
      },
      {
        id: 'eng102',
        code: 'ENG 102',
        title: 'Composition and Literature',
        instructor: 'Dr. Emily Rodriguez',
        department: 'ENG',
        credits: 3,
        schedule: 'MWF 2:00-3:00 PM',
        timeSlot: 'afternoon',
        availability: 'Available',
        type: 'core',
        format: 'in-person',
        prerequisites: []
      },
      {
        id: 'cs201',
        code: 'CS 201',
        title: 'Data Structures',
        instructor: 'Dr. Sarah Johnson',
        department: 'CS',
        credits: 3,
        schedule: 'TTh 2:00-3:30 PM',
        timeSlot: 'afternoon',
        availability: 'Available',
        type: 'core',
        format: 'in-person',
        prerequisites: [{ code: 'CS 101', title: 'Introduction to Computer Science' }]
      },
      {
        id: 'phys101',
        code: 'PHYS 101',
        title: 'General Physics I',
        instructor: 'Dr. Robert Kim',
        department: 'PHYS',
        credits: 4,
        schedule: 'MWF 10:00-11:00 AM + Lab',
        timeSlot: 'morning',
        availability: 'Full',
        type: 'elective',
        format: 'in-person',
        prerequisites: [{ code: 'MATH 101', title: 'College Algebra' }]
      },
      {
        id: 'cs300',
        code: 'CS 300',
        title: 'Web Development',
        instructor: 'Prof. Lisa Wang',
        department: 'CS',
        credits: 3,
        schedule: 'Online - Asynchronous',
        timeSlot: 'online',
        availability: 'Available',
        type: 'elective',
        format: 'online',
        prerequisites: [{ code: 'CS 101', title: 'Introduction to Computer Science' }]
      },
      {
        id: 'math301',
        code: 'MATH 301',
        title: 'Linear Algebra',
        instructor: 'Dr. James Thompson',
        department: 'MATH',
        credits: 3,
        schedule: 'TTh 9:30-11:00 AM',
        timeSlot: 'morning',
        availability: 'Waitlist',
        type: 'elective',
        format: 'in-person',
        prerequisites: [{ code: 'MATH 201', title: 'Calculus II' }]
      },
      {
        id: 'eng201',
        code: 'ENG 201',
        title: 'Creative Writing',
        instructor: 'Prof. Maria Garcia',
        department: 'ENG',
        credits: 3,
        schedule: 'MW 6:00-7:30 PM',
        timeSlot: 'evening',
        availability: 'Available',
        type: 'elective',
        format: 'in-person',
        prerequisites: [{ code: 'ENG 102', title: 'Composition and Literature' }]
      }
    ]
  } catch (error) {
    console.error('Error loading courses:', error)
    useToast().add({
      title: 'Error Loading Courses',
      description: 'Failed to load available courses. Please try again.',
      color: 'red',
      icon: 'i-heroicons-exclamation-triangle'
    })
  }
}

// Load data on mount
onMounted(async () => {
  await loadAvailableCourses()

  // Check for pre-selected course from query params
  const courseId = route.query.course
  if (courseId) {
    const course = availableCourses.value.find(c => c.id === courseId)
    if (course && canAddCourse(course)) {
      await addCourseToSchedule(course)
    }
  }

  // Load draft if available
  const draft = localStorage.getItem('registrationDraft')
  if (draft) {
    try {
      const draftCourses = JSON.parse(draft)
      selectedCourses.value = draftCourses.filter(course =>
        availableCourses.value.some(available => available.id === course.id)
      )
    } catch (error) {
      console.error('Error loading draft:', error)
    }
  }
})
</script>
