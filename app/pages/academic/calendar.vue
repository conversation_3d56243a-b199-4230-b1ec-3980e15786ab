<template>
  <UContainer class="py-6">
    <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Academic Calendar</h1>
        <p class="text-gray-600 dark:text-gray-300">View important academic dates and events</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshCalendar"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-down-tray"
          @click="exportCalendar"
        >
          Export
        </UButton>
        <UButton
          color="primary"
          icon="i-heroicons-plus"
          @click="showAddEvent = true"
        >
          Add Event
        </UButton>
      </div>
    </div>

    <!-- Calendar Controls -->
    <UCard>
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <!-- View Toggle -->
        <div class="flex items-center space-x-4">
          <UButtonGroup>
            <UButton
              :variant="viewMode === 'calendar' ? 'solid' : 'outline'"
              icon="i-heroicons-calendar-days"
              @click="viewMode = 'calendar'"
            >
              Calendar
            </UButton>
            <UButton
              :variant="viewMode === 'list' ? 'solid' : 'outline'"
              icon="i-heroicons-list-bullet"
              @click="viewMode = 'list'"
            >
              List
            </UButton>
          </UButtonGroup>
        </div>

        <!-- Filters -->
        <div class="flex items-center space-x-3">
          <USelect
            v-model="selectedEventType"
            :options="eventTypeOptions"
            placeholder="All Events"
            @change="filterEvents"
          />
          <USelect
            v-model="selectedSemester"
            :options="semesterOptions"
            placeholder="Current Semester"
            @change="filterEvents"
          />
        </div>

        <!-- Month Navigation (Calendar View) -->
        <div v-if="viewMode === 'calendar'" class="flex items-center space-x-2">
          <UButton
            icon="i-heroicons-chevron-left"
            variant="outline"
            size="sm"
            @click="previousMonth"
          />
          <span class="text-lg font-semibold min-w-[200px] text-center">
            {{ currentMonthYear }}
          </span>
          <UButton
            icon="i-heroicons-chevron-right"
            variant="outline"
            size="sm"
            @click="nextMonth"
          />
        </div>
      </div>
    </UCard>

    <!-- Calendar View -->
    <UCard v-if="viewMode === 'calendar'">
      <div class="calendar-grid">
        <!-- Calendar Header -->
        <div class="grid grid-cols-7 gap-1 mb-4">
          <div
            v-for="day in weekDays"
            :key="day"
            class="p-2 text-center text-sm font-medium text-gray-600 dark:text-gray-400"
          >
            {{ day }}
          </div>
        </div>

        <!-- Calendar Days -->
        <div class="grid grid-cols-7 gap-1">
          <div
            v-for="day in calendarDays"
            :key="day.date"
            class="min-h-[120px] p-2 border border-gray-200 dark:border-gray-700 rounded-lg"
            :class="[
              day.isCurrentMonth ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-900',
              day.isToday ? 'ring-2 ring-primary-500' : '',
              day.isCurrentMonth ? 'text-gray-900 dark:text-white' : 'text-gray-400 dark:text-gray-600'
            ]"
          >
            <!-- Day Number -->
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm font-medium">{{ day.dayNumber }}</span>
              <UBadge
                v-if="day.events.length > 3"
                size="xs"
                variant="soft"
                color="primary"
              >
                +{{ day.events.length - 3 }}
              </UBadge>
            </div>

            <!-- Events -->
            <div class="space-y-1">
              <div
                v-for="(event, index) in day.events.slice(0, 3)"
                :key="event.id"
                class="text-xs p-1 rounded cursor-pointer truncate"
                :class="getEventClasses(event.type)"
                @click="viewEvent(event)"
                :title="event.title"
              >
                {{ event.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- List View -->
    <div v-else class="space-y-4">
      <!-- Upcoming Events -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Upcoming Events</h3>
        </template>
        <div class="space-y-3">
          <div
            v-for="event in upcomingEvents"
            :key="event.id"
            class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
            @click="viewEvent(event)"
          >
            <div class="flex-shrink-0">
              <div
                class="w-3 h-3 rounded-full"
                :class="getEventColorClass(event.type)"
              ></div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ event.title }}
                </h4>
                <UBadge
                  :color="getEventBadgeColor(event.type)"
                  variant="soft"
                  size="sm"
                >
                  {{ event.type }}
                </UBadge>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{ event.description }}
              </p>
              <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                <div class="flex items-center space-x-1">
                  <UIcon name="i-heroicons-calendar" class="w-3 h-3" />
                  <span>{{ formatEventDate(event.date) }}</span>
                </div>
                <div v-if="event.time" class="flex items-center space-x-1">
                  <UIcon name="i-heroicons-clock" class="w-3 h-3" />
                  <span>{{ event.time }}</span>
                </div>
                <div v-if="event.location" class="flex items-center space-x-1">
                  <UIcon name="i-heroicons-map-pin" class="w-3 h-3" />
                  <span>{{ event.location }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Past Events -->
      <UCard v-if="pastEvents.length > 0">
        <template #header>
          <h3 class="text-lg font-semibold">Recent Past Events</h3>
        </template>
        <div class="space-y-3">
          <div
            v-for="event in pastEvents.slice(0, 5)"
            :key="event.id"
            class="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg opacity-75"
          >
            <div class="flex-shrink-0">
              <div
                class="w-3 h-3 rounded-full"
                :class="getEventColorClass(event.type)"
              ></div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ event.title }}
                </h4>
                <UBadge
                  :color="getEventBadgeColor(event.type)"
                  variant="outline"
                  size="sm"
                >
                  {{ event.type }}
                </UBadge>
              </div>
              <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                <div class="flex items-center space-x-1">
                  <UIcon name="i-heroicons-calendar" class="w-3 h-3" />
                  <span>{{ formatEventDate(event.date) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Event Detail Modal -->
    <UModal v-model="showEventDetail" :ui="{ width: 'sm:max-w-lg' }">
      <UCard v-if="selectedEvent">
        <template #header>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold">{{ selectedEvent.title }}</h3>
              <UBadge
                :color="getEventBadgeColor(selectedEvent.type)"
                variant="soft"
                size="sm"
                class="mt-1"
              >
                {{ selectedEvent.type }}
              </UBadge>
            </div>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showEventDetail = false"
            />
          </div>
        </template>

        <div class="space-y-4">
          <p class="text-gray-700 dark:text-gray-300">
            {{ selectedEvent.description }}
          </p>
          
          <div class="grid grid-cols-1 gap-3">
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-calendar" class="w-4 h-4 text-gray-500" />
              <span class="text-sm">{{ formatEventDate(selectedEvent.date) }}</span>
            </div>
            <div v-if="selectedEvent.time" class="flex items-center space-x-2">
              <UIcon name="i-heroicons-clock" class="w-4 h-4 text-gray-500" />
              <span class="text-sm">{{ selectedEvent.time }}</span>
            </div>
            <div v-if="selectedEvent.location" class="flex items-center space-x-2">
              <UIcon name="i-heroicons-map-pin" class="w-4 h-4 text-gray-500" />
              <span class="text-sm">{{ selectedEvent.location }}</span>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              variant="outline"
              @click="showEventDetail = false"
            >
              Close
            </UButton>
            <UButton
              color="primary"
              icon="i-heroicons-calendar-plus"
              @click="addToPersonalCalendar"
            >
              Add to Calendar
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Add Event Modal -->
    <UModal v-model="showAddEvent" :ui="{ width: 'sm:max-w-lg' }">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">Add New Event</h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showAddEvent = false"
            />
          </div>
        </template>

        <UForm :state="newEvent" @submit="submitNewEvent">
          <div class="space-y-4">
            <UFormGroup label="Event Title" required>
              <UInput v-model="newEvent.title" placeholder="Enter event title" />
            </UFormGroup>
            
            <UFormGroup label="Event Type" required>
              <USelect
                v-model="newEvent.type"
                :options="eventTypeOptions.filter(opt => opt.value !== '')"
                placeholder="Select event type"
              />
            </UFormGroup>
            
            <UFormGroup label="Date" required>
              <UInput v-model="newEvent.date" type="date" />
            </UFormGroup>
            
            <UFormGroup label="Time">
              <UInput v-model="newEvent.time" type="time" />
            </UFormGroup>
            
            <UFormGroup label="Location">
              <UInput v-model="newEvent.location" placeholder="Enter location" />
            </UFormGroup>
            
            <UFormGroup label="Description">
              <UTextarea v-model="newEvent.description" placeholder="Enter event description" />
            </UFormGroup>
          </div>
        </UForm>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              variant="outline"
              @click="showAddEvent = false"
            >
              Cancel
            </UButton>
            <UButton
              color="primary"
              @click="submitNewEvent"
            >
              Add Event
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
    </div>
  </UContainer>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// Reactive data
const viewMode = ref('calendar')
const currentDate = ref(new Date())
const selectedEventType = ref('')
const selectedSemester = ref('')
const isRefreshing = ref(false)
const showEventDetail = ref(false)
const showAddEvent = ref(false)
const selectedEvent = ref(null)
const events = ref([])

// New event form
const newEvent = ref({
  title: '',
  type: '',
  date: '',
  time: '',
  location: '',
  description: ''
})

// Options
const eventTypeOptions = [
  { label: 'All Events', value: '' },
  { label: 'Registration', value: 'registration' },
  { label: 'Exam', value: 'exam' },
  { label: 'Holiday', value: 'holiday' },
  { label: 'Deadline', value: 'deadline' },
  { label: 'Orientation', value: 'orientation' },
  { label: 'Graduation', value: 'graduation' },
  { label: 'Conference', value: 'conference' },
  { label: 'Workshop', value: 'workshop' }
]

const semesterOptions = [
  { label: 'Current Semester', value: '' },
  { label: 'Fall 2024', value: 'fall-2024' },
  { label: 'Spring 2025', value: 'spring-2025' },
  { label: 'Summer 2025', value: 'summer-2025' }
]

const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

// Computed properties
const currentMonthYear = computed(() => {
  return currentDate.value.toLocaleDateString('en-US', {
    month: 'long',
    year: 'numeric'
  })
})

const filteredEvents = computed(() => {
  let result = [...events.value]

  if (selectedEventType.value) {
    result = result.filter(event => event.type === selectedEventType.value)
  }

  if (selectedSemester.value) {
    result = result.filter(event => event.semester === selectedSemester.value)
  }

  return result
})

const upcomingEvents = computed(() => {
  const today = new Date()
  return filteredEvents.value
    .filter(event => new Date(event.date) >= today)
    .sort((a, b) => new Date(a.date) - new Date(b.date))
    .slice(0, 10)
})

const pastEvents = computed(() => {
  const today = new Date()
  return filteredEvents.value
    .filter(event => new Date(event.date) < today)
    .sort((a, b) => new Date(b.date) - new Date(a.date))
})

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  // Get first day of month and how many days in month
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const daysInMonth = lastDay.getDate()
  const startingDayOfWeek = firstDay.getDay()

  // Get days from previous month to fill the grid
  const daysFromPrevMonth = startingDayOfWeek
  const prevMonth = new Date(year, month - 1, 0)
  const daysInPrevMonth = prevMonth.getDate()

  // Get days from next month to fill the grid
  const totalCells = 42 // 6 weeks * 7 days
  const daysFromNextMonth = totalCells - daysInMonth - daysFromPrevMonth

  const days = []
  const today = new Date()

  // Previous month days
  for (let i = daysFromPrevMonth - 1; i >= 0; i--) {
    const day = daysInPrevMonth - i
    const date = new Date(year, month - 1, day)
    days.push({
      dayNumber: day,
      date: date.toISOString().split('T')[0],
      isCurrentMonth: false,
      isToday: false,
      events: getEventsForDate(date)
    })
  }

  // Current month days
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day)
    const isToday = date.toDateString() === today.toDateString()
    days.push({
      dayNumber: day,
      date: date.toISOString().split('T')[0],
      isCurrentMonth: true,
      isToday,
      events: getEventsForDate(date)
    })
  }

  // Next month days
  for (let day = 1; day <= daysFromNextMonth; day++) {
    const date = new Date(year, month + 1, day)
    days.push({
      dayNumber: day,
      date: date.toISOString().split('T')[0],
      isCurrentMonth: false,
      isToday: false,
      events: getEventsForDate(date)
    })
  }

  return days
})

// Methods
const getEventsForDate = (date) => {
  const dateString = date.toISOString().split('T')[0]
  return filteredEvents.value.filter(event => event.date === dateString)
}

const getEventClasses = (type) => {
  const classes = {
    'registration': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
    'exam': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
    'holiday': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
    'deadline': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
    'orientation': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
    'graduation': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
    'conference': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300',
    'workshop': 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-300'
  }
  return classes[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
}

const getEventColorClass = (type) => {
  const classes = {
    'registration': 'bg-blue-500',
    'exam': 'bg-red-500',
    'holiday': 'bg-green-500',
    'deadline': 'bg-orange-500',
    'orientation': 'bg-purple-500',
    'graduation': 'bg-yellow-500',
    'conference': 'bg-indigo-500',
    'workshop': 'bg-pink-500'
  }
  return classes[type] || 'bg-gray-500'
}

const getEventBadgeColor = (type) => {
  const colors = {
    'registration': 'blue',
    'exam': 'red',
    'holiday': 'green',
    'deadline': 'orange',
    'orientation': 'purple',
    'graduation': 'yellow',
    'conference': 'indigo',
    'workshop': 'pink'
  }
  return colors[type] || 'gray'
}

const formatEventDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const previousMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

const nextMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

const filterEvents = () => {
  // Events are automatically filtered via computed property
}

const viewEvent = (event) => {
  selectedEvent.value = event
  showEventDetail.value = true
}

const addToPersonalCalendar = () => {
  // Mock functionality for adding to personal calendar
  useToast().add({
    title: 'Added to Calendar',
    description: `${selectedEvent.value.title} has been added to your personal calendar`,
    icon: 'i-heroicons-calendar-plus'
  })
  showEventDetail.value = false
}

const submitNewEvent = () => {
  if (!newEvent.value.title || !newEvent.value.type || !newEvent.value.date) {
    useToast().add({
      title: 'Validation Error',
      description: 'Please fill in all required fields',
      color: 'red',
      icon: 'i-heroicons-exclamation-triangle'
    })
    return
  }

  // Add new event to the list
  const event = {
    id: Date.now().toString(),
    ...newEvent.value,
    semester: 'fall-2024' // Default semester
  }

  events.value.push(event)

  useToast().add({
    title: 'Event Added',
    description: `${event.title} has been added to the calendar`,
    icon: 'i-heroicons-check-circle'
  })

  // Reset form
  newEvent.value = {
    title: '',
    type: '',
    date: '',
    time: '',
    location: '',
    description: ''
  }

  showAddEvent.value = false
}

const refreshCalendar = async () => {
  isRefreshing.value = true
  try {
    await loadEvents()
    useToast().add({
      title: 'Calendar Refreshed',
      description: 'Academic calendar has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const exportCalendar = () => {
  // Mock export functionality
  useToast().add({
    title: 'Export Started',
    description: 'Calendar export has been initiated',
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const loadEvents = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))

    // Mock events data
    events.value = [
      {
        id: '1',
        title: 'Fall Semester Registration Opens',
        type: 'registration',
        date: '2024-07-15',
        time: '08:00',
        location: 'Online',
        description: 'Registration for Fall 2024 semester opens for all students.',
        semester: 'fall-2024'
      },
      {
        id: '2',
        title: 'First Day of Classes',
        type: 'orientation',
        date: '2024-08-26',
        time: '08:00',
        location: 'Campus Wide',
        description: 'First day of Fall 2024 semester classes.',
        semester: 'fall-2024'
      },
      {
        id: '3',
        title: 'Labor Day Holiday',
        type: 'holiday',
        date: '2024-09-02',
        location: 'Campus Closed',
        description: 'Campus closed for Labor Day holiday.',
        semester: 'fall-2024'
      },
      {
        id: '4',
        title: 'Midterm Exams Begin',
        type: 'exam',
        date: '2024-10-14',
        time: '08:00',
        location: 'Various Locations',
        description: 'Midterm examination period begins.',
        semester: 'fall-2024'
      },
      {
        id: '5',
        title: 'Course Withdrawal Deadline',
        type: 'deadline',
        date: '2024-10-25',
        time: '17:00',
        location: 'Registrar Office',
        description: 'Last day to withdraw from courses without academic penalty.',
        semester: 'fall-2024'
      },
      {
        id: '6',
        title: 'Thanksgiving Break',
        type: 'holiday',
        date: '2024-11-28',
        location: 'Campus Closed',
        description: 'Thanksgiving break begins. Classes resume December 2.',
        semester: 'fall-2024'
      },
      {
        id: '7',
        title: 'Final Exams Begin',
        type: 'exam',
        date: '2024-12-16',
        time: '08:00',
        location: 'Various Locations',
        description: 'Final examination period begins.',
        semester: 'fall-2024'
      },
      {
        id: '8',
        title: 'Fall Graduation Ceremony',
        type: 'graduation',
        date: '2024-12-21',
        time: '10:00',
        location: 'Main Auditorium',
        description: 'Fall 2024 graduation ceremony.',
        semester: 'fall-2024'
      },
      {
        id: '9',
        title: 'Spring Registration Opens',
        type: 'registration',
        date: '2024-11-01',
        time: '08:00',
        location: 'Online',
        description: 'Registration for Spring 2025 semester opens.',
        semester: 'spring-2025'
      },
      {
        id: '10',
        title: 'Academic Conference',
        type: 'conference',
        date: '2024-11-15',
        time: '09:00',
        location: 'Conference Center',
        description: 'Annual academic conference featuring research presentations.',
        semester: 'fall-2024'
      },
      {
        id: '11',
        title: 'Study Skills Workshop',
        type: 'workshop',
        date: '2024-09-20',
        time: '14:00',
        location: 'Library Room 201',
        description: 'Workshop on effective study techniques and time management.',
        semester: 'fall-2024'
      },
      {
        id: '12',
        title: 'Career Fair',
        type: 'conference',
        date: '2024-10-10',
        time: '10:00',
        location: 'Student Center',
        description: 'Annual career fair with employers from various industries.',
        semester: 'fall-2024'
      }
    ]
  } catch (error) {
    console.error('Error loading events:', error)
    useToast().add({
      title: 'Error Loading Events',
      description: 'Failed to load calendar events. Please try again.',
      color: 'red',
      icon: 'i-heroicons-exclamation-triangle'
    })
  }
}

// Load events on mount
onMounted(() => {
  loadEvents()
})
</script>
