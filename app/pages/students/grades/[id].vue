<template>
  <div v-if="student" class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="flex items-center space-x-4">
        <UButton
          variant="ghost"
          icon="i-heroicons-arrow-left"
          @click="$router.back()"
        >
          Back
        </UButton>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Grade Management - {{ student.firstName }} {{ student.lastName }}
          </h1>
          <p class="text-gray-600 dark:text-gray-300">{{ student.studentId }} • {{ student.program }}</p>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <USelect
          v-model="selectedSemester"
          :options="semesterOptions"
          @change="loadGrades"
        />
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshGrades"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton color="primary" icon="i-heroicons-plus" @click="showAddGrade = true">
          Add Grade
        </UButton>
      </div>
    </div>

    <!-- Student Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <UCard>
        <div class="text-center">
          <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ student.gpa }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Current GPA</div>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ student.credits.earned }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Credits Earned</div>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ currentSemesterGrades.length }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Current Courses</div>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ Math.round((student.credits.earned / student.credits.required) * 100) }}%</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Degree Progress</div>
        </div>
      </UCard>
    </div>

    <!-- Current Semester Grades -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">{{ selectedSemester }} Grades</h2>
          <div class="flex items-center space-x-2">
            <UInput
              v-model="searchQuery"
              placeholder="Search courses..."
              icon="i-heroicons-magnifying-glass"
              size="sm"
              class="w-64"
            />
            <UButton variant="ghost" size="sm" icon="i-heroicons-funnel">
              Filter
            </UButton>
          </div>
        </div>
      </template>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Course
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Instructor
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Credits
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Grade
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Grade Points
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="grade in filteredGrades" :key="grade.id" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ grade.courseId }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ grade.courseName }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ grade.instructor }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ grade.credits }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="editingGrade === grade.id" class="flex items-center space-x-2">
                  <USelect
                    v-model="editGradeValue"
                    :options="gradeOptions"
                    size="sm"
                    class="w-20"
                  />
                  <UButton size="sm" @click="saveGrade(grade)">Save</UButton>
                  <UButton size="sm" variant="outline" @click="cancelEdit">Cancel</UButton>
                </div>
                <UBadge v-else :color="getGradeColor(grade.grade)" variant="subtle">
                  {{ grade.grade }}
                </UBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ grade.gradePoints }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <UButton
                    v-if="editingGrade !== grade.id"
                    variant="ghost"
                    size="sm"
                    icon="i-heroicons-pencil"
                    @click="editGrade(grade)"
                  >
                    Edit
                  </UButton>
                  <UButton
                    variant="ghost"
                    size="sm"
                    icon="i-heroicons-eye"
                    @click="viewGradeDetails(grade)"
                  >
                    Details
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-if="filteredGrades.length === 0" class="text-center py-8">
        <UIcon name="i-heroicons-chart-bar-square" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No grades found</h3>
        <p class="text-gray-600 dark:text-gray-300">No grades available for the selected semester.</p>
      </div>
    </UCard>

    <!-- GPA Calculation -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">GPA Calculation</h2>
      </template>

      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Semester GPA</h3>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Total Grade Points:</span>
                <span class="text-sm font-medium">{{ semesterGradePoints }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Total Credits:</span>
                <span class="text-sm font-medium">{{ semesterCredits }}</span>
              </div>
              <div class="flex justify-between border-t pt-2">
                <span class="text-sm font-medium text-gray-900 dark:text-white">Semester GPA:</span>
                <span class="text-sm font-bold" :class="getGPAColor(semesterGPA)">{{ semesterGPA }}</span>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Cumulative GPA</h3>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Total Grade Points:</span>
                <span class="text-sm font-medium">{{ cumulativeGradePoints }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Total Credits:</span>
                <span class="text-sm font-medium">{{ student.credits.earned }}</span>
              </div>
              <div class="flex justify-between border-t pt-2">
                <span class="text-sm font-medium text-gray-900 dark:text-white">Cumulative GPA:</span>
                <span class="text-sm font-bold" :class="getGPAColor(student.gpa)">{{ student.gpa }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Grade Distribution Chart -->
        <div>
          <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Grade Distribution</h3>
          <div class="grid grid-cols-5 gap-2">
            <div v-for="(count, grade) in gradeDistribution" :key="grade" class="text-center">
              <div class="text-2xl font-bold" :class="getGradeColor(grade)">{{ count }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ grade }}</div>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Add Grade Modal -->
    <UModal v-model="showAddGrade">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Add Grade</h3>
        </template>

        <form @submit.prevent="addGrade" class="space-y-4">
          <UInput
            v-model="newGrade.courseId"
            placeholder="Course ID (e.g., CS 301)"
            required
          />
          <UInput
            v-model="newGrade.courseName"
            placeholder="Course Name"
            required
          />
          <UInput
            v-model="newGrade.instructor"
            placeholder="Instructor Name"
            required
          />
          <UInput
            v-model="newGrade.credits"
            type="number"
            placeholder="Credits"
            min="1"
            max="6"
            required
          />
          <USelect
            v-model="newGrade.grade"
            :options="gradeOptions"
            placeholder="Select Grade"
            required
          />
        </form>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton variant="outline" @click="showAddGrade = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addGrade">
              Add Grade
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Grade Details Modal -->
    <UModal v-model="showGradeDetails" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard v-if="selectedGrade">
        <template #header>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold">{{ selectedGrade.courseId }} - Grade Details</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ selectedGrade.courseName }}</p>
            </div>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showGradeDetails = false"
            />
          </div>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Course:</span>
              <p class="text-sm">{{ selectedGrade.courseId }} - {{ selectedGrade.courseName }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Instructor:</span>
              <p class="text-sm">{{ selectedGrade.instructor }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Credits:</span>
              <p class="text-sm">{{ selectedGrade.credits }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Grade:</span>
              <UBadge :color="getGradeColor(selectedGrade.grade)" variant="subtle">
                {{ selectedGrade.grade }}
              </UBadge>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Grade Points:</span>
              <p class="text-sm">{{ selectedGrade.gradePoints }}</p>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Semester:</span>
              <p class="text-sm">{{ selectedGrade.semester }} {{ selectedGrade.year }}</p>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end">
            <UButton @click="showGradeDetails = false">
              Close
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>

  <!-- Loading State -->
  <div v-else class="flex items-center justify-center min-h-96">
    <div class="text-center">
      <UIcon name="i-heroicons-chart-bar-square" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Student Not Found</h3>
      <p class="text-gray-600 dark:text-gray-300">The requested student could not be found.</p>
      <UButton class="mt-4" @click="$router.back()">
        Go Back
      </UButton>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

const route = useRoute()
const { getStudentById } = useStudentData()

// Get student data
const studentId = Number.parseInt(route.params.id)
const student = computed(() => getStudentById(studentId))

// Reactive data
const isRefreshing = ref(false)
const showAddGrade = ref(false)
const showGradeDetails = ref(false)
const selectedGrade = ref(null)
const searchQuery = ref('')
const selectedSemester = ref('Fall 2024')
const editingGrade = ref(null)
const editGradeValue = ref('')

// Mock grade data
const grades = ref([
  {
    id: 1,
    studentId: studentId,
    courseId: 'CS 301',
    courseName: 'Data Structures',
    semester: 'Fall',
    year: 2024,
    grade: 'A-',
    credits: 3,
    gradePoints: 11.1,
    instructor: 'Dr. Sarah Johnson'
  },
  {
    id: 2,
    studentId: studentId,
    courseId: 'MATH 201',
    courseName: 'Calculus II',
    semester: 'Fall',
    year: 2024,
    grade: 'B+',
    credits: 4,
    gradePoints: 13.2,
    instructor: 'Prof. Michael Chen'
  },
  {
    id: 3,
    studentId: studentId,
    courseId: 'ENG 102',
    courseName: 'Composition',
    semester: 'Fall',
    year: 2024,
    grade: 'A',
    credits: 3,
    gradePoints: 12.0,
    instructor: 'Dr. Emily Rodriguez'
  }
])

const newGrade = ref({
  courseId: '',
  courseName: '',
  instructor: '',
  credits: '',
  grade: ''
})

// Options
const semesterOptions = [
  { label: 'Fall 2024', value: 'Fall 2024' },
  { label: 'Spring 2024', value: 'Spring 2024' },
  { label: 'Fall 2023', value: 'Fall 2023' },
  { label: 'Spring 2023', value: 'Spring 2023' }
]

const gradeOptions = [
  { label: 'A', value: 'A' },
  { label: 'A-', value: 'A-' },
  { label: 'B+', value: 'B+' },
  { label: 'B', value: 'B' },
  { label: 'B-', value: 'B-' },
  { label: 'C+', value: 'C+' },
  { label: 'C', value: 'C' },
  { label: 'C-', value: 'C-' },
  { label: 'D+', value: 'D+' },
  { label: 'D', value: 'D' },
  { label: 'F', value: 'F' }
]

// Computed properties
const currentSemesterGrades = computed(() => {
  return grades.value.filter(grade => 
    `${grade.semester} ${grade.year}` === selectedSemester.value
  )
})

const filteredGrades = computed(() => {
  let filtered = currentSemesterGrades.value
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(grade =>
      grade.courseId.toLowerCase().includes(query) ||
      grade.courseName.toLowerCase().includes(query) ||
      grade.instructor.toLowerCase().includes(query)
    )
  }
  
  return filtered
})

const semesterGradePoints = computed(() => {
  return currentSemesterGrades.value.reduce((sum, grade) => sum + grade.gradePoints, 0)
})

const semesterCredits = computed(() => {
  return currentSemesterGrades.value.reduce((sum, grade) => sum + grade.credits, 0)
})

const semesterGPA = computed(() => {
  if (semesterCredits.value === 0) return '0.00'
  return (semesterGradePoints.value / semesterCredits.value).toFixed(2)
})

const cumulativeGradePoints = computed(() => {
  return grades.value.reduce((sum, grade) => sum + grade.gradePoints, 0)
})

const gradeDistribution = computed(() => {
  const distribution = { 'A': 0, 'B': 0, 'C': 0, 'D': 0, 'F': 0 }
  currentSemesterGrades.value.forEach(grade => {
    const letter = grade.grade.charAt(0)
    if (distribution.hasOwnProperty(letter)) {
      distribution[letter]++
    }
  })
  return distribution
})

// Methods
const loadGrades = () => {
  // In a real app, this would fetch grades for the selected semester
  useToast().add({
    title: 'Grades Loaded',
    description: `Loaded grades for ${selectedSemester.value}`,
    icon: 'i-heroicons-check-circle'
  })
}

const refreshGrades = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    useToast().add({
      title: 'Grades Refreshed',
      description: 'Grade data has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const editGrade = (grade) => {
  editingGrade.value = grade.id
  editGradeValue.value = grade.grade
}

const saveGrade = (grade) => {
  grade.grade = editGradeValue.value
  grade.gradePoints = calculateGradePoints(editGradeValue.value, grade.credits)
  editingGrade.value = null
  editGradeValue.value = ''
  
  useToast().add({
    title: 'Grade Updated',
    description: `Grade updated for ${grade.courseId}`,
    icon: 'i-heroicons-check-circle'
  })
}

const cancelEdit = () => {
  editingGrade.value = null
  editGradeValue.value = ''
}

const addGrade = () => {
  const gradePoints = calculateGradePoints(newGrade.value.grade, Number.parseInt(newGrade.value.credits))
  
  grades.value.push({
    id: grades.value.length + 1,
    studentId: studentId,
    courseId: newGrade.value.courseId,
    courseName: newGrade.value.courseName,
    semester: 'Fall',
    year: 2024,
    grade: newGrade.value.grade,
    credits: Number.parseInt(newGrade.value.credits),
    gradePoints: gradePoints,
    instructor: newGrade.value.instructor
  })
  
  useToast().add({
    title: 'Grade Added',
    description: `Grade added for ${newGrade.value.courseId}`,
    icon: 'i-heroicons-plus'
  })
  
  showAddGrade.value = false
  newGrade.value = {
    courseId: '',
    courseName: '',
    instructor: '',
    credits: '',
    grade: ''
  }
}

const viewGradeDetails = (grade) => {
  selectedGrade.value = grade
  showGradeDetails.value = true
}

const calculateGradePoints = (grade, credits) => {
  const gradeValues = {
    'A': 4.0, 'A-': 3.7, 'B+': 3.3, 'B': 3.0, 'B-': 2.7,
    'C+': 2.3, 'C': 2.0, 'C-': 1.7, 'D+': 1.3, 'D': 1.0, 'F': 0.0
  }
  return (gradeValues[grade] || 0) * credits
}

const getGradeColor = (grade) => {
  if (grade.startsWith('A')) return 'green'
  if (grade.startsWith('B')) return 'blue'
  if (grade.startsWith('C')) return 'yellow'
  if (grade.startsWith('D')) return 'orange'
  return 'red'
}

const getGPAColor = (gpa) => {
  const numGPA = typeof gpa === 'string' ? Number.parseFloat(gpa) : gpa
  if (numGPA >= 3.5) return 'text-green-600 dark:text-green-400'
  if (numGPA >= 3.0) return 'text-blue-600 dark:text-blue-400'
  if (numGPA >= 2.5) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-red-600 dark:text-red-400'
}

// Initialize
onMounted(() => {
  loadGrades()
})

// SEO
useHead({
  title: computed(() => student.value ? `${student.value.firstName} ${student.value.lastName} - Grades` : 'Student Grades'),
  meta: [
    { name: 'description', content: computed(() => student.value ? `Grade management for ${student.value.firstName} ${student.value.lastName}` : 'Student grade management') }
  ]
})
</script>
