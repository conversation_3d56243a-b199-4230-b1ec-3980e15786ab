<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Student Directory</h1>
        <p class="text-gray-600 dark:text-gray-300">Manage and view student information</p>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-path"
          @click="refreshStudents"
          :loading="isRefreshing"
        >
          Refresh
        </UButton>
        <UButton color="primary" icon="i-heroicons-plus" @click="showAddStudent = true">
          Add Student
        </UButton>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalStudents }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-check-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Students</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ activeStudents }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Programs</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalPrograms }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg GPA</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ averageGPA }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Search and Filters -->
    <UCard>
      <div class="flex flex-col sm:flex-row sm:items-center gap-4">
        <UInput
          v-model="searchQuery"
          placeholder="Search students..."
          icon="i-heroicons-magnifying-glass"
          class="flex-1"
          @input="debouncedSearch"
        />
        <div class="flex items-center space-x-3">
          <USelect
            v-model="selectedProgram"
            :options="programOptions"
            placeholder="All Programs"
            @change="filterStudents"
          />
          <USelect
            v-model="selectedYear"
            :options="yearOptions"
            placeholder="All Years"
            @change="filterStudents"
          />
          <USelect
            v-model="selectedStatus"
            :options="statusOptions"
            placeholder="All Status"
            @change="filterStudents"
          />
          <UButton variant="outline" icon="i-heroicons-funnel" @click="showAdvancedFilters = !showAdvancedFilters">
            Filters
          </UButton>
        </div>
      </div>

      <!-- Advanced Filters -->
      <div v-if="showAdvancedFilters" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">GPA Range</label>
            <div class="flex items-center space-x-2">
              <UInput v-model="gpaMin" type="number" step="0.1" min="0" max="4" placeholder="Min" size="sm" />
              <span class="text-gray-500">to</span>
              <UInput v-model="gpaMax" type="number" step="0.1" min="0" max="4" placeholder="Max" size="sm" />
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Financial Status</label>
            <USelect
              v-model="selectedFinancialStatus"
              :options="financialStatusOptions"
              placeholder="All Financial Status"
            />
          </div>
          <div class="flex items-end">
            <UButton variant="outline" @click="clearFilters" size="sm">
              Clear Filters
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Student Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard
        v-for="student in displayedStudents"
        :key="student.id"
        class="hover:shadow-lg transition-shadow cursor-pointer"
        @click="viewStudent(student.id)"
      >
        <div class="flex items-start space-x-4">
          <UAvatar :src="student.photo" :alt="`${student.firstName} ${student.lastName}`" size="lg" />
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
              {{ student.firstName }} {{ student.lastName }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ student.studentId }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ student.program }}</p>
            <div class="flex items-center space-x-2 mt-2">
              <UBadge :color="getStatusColor(student.status)" variant="subtle" size="sm">
                {{ student.status }}
              </UBadge>
              <span class="text-xs text-gray-500 dark:text-gray-400">Year {{ student.year }}</span>
            </div>
          </div>
        </div>

        <div class="mt-4 flex items-center justify-between">
          <div class="text-sm">
            <span class="text-gray-600 dark:text-gray-400">GPA:</span>
            <span class="font-medium ml-1" :class="getGPAColor(student.gpa)">{{ student.gpa }}</span>
          </div>
          <div class="flex items-center space-x-1">
            <UButton
              size="sm"
              variant="ghost"
              icon="i-heroicons-eye"
              @click.stop="viewStudent(student.id)"
            >
              View
            </UButton>
            <UButton
              size="sm"
              variant="ghost"
              icon="i-heroicons-chart-bar-square"
              @click.stop="viewGrades(student.id)"
            >
              Grades
            </UButton>
            <UButton
              size="sm"
              variant="ghost"
              icon="i-heroicons-document-text"
              @click.stop="viewTranscript(student.id)"
            >
              Transcript
            </UButton>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Empty State -->
    <div v-if="displayedStudents.length === 0" class="text-center py-12">
      <UIcon name="i-heroicons-users" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No students found</h3>
      <p class="text-gray-600 dark:text-gray-300">
        Try adjusting your search criteria or filters.
      </p>
    </div>

    <!-- Pagination -->
    <div v-if="pagination.totalPages > 1" class="flex justify-center">
      <UPagination
        v-model="currentPage"
        :page-count="pagination.totalPages"
        :total="pagination.total"
        :ui="{
          wrapper: 'flex items-center gap-1',
          rounded: '!rounded-full min-w-[32px] justify-center',
          default: {
            activeButton: {
              variant: 'outline'
            }
          }
        }"
        @update:model-value="loadStudents"
      />
    </div>

    <!-- Add Student Modal -->
    <UModal v-model="showAddStudent">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Add New Student</h3>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput v-model="newStudent.firstName" placeholder="First Name" />
            <UInput v-model="newStudent.lastName" placeholder="Last Name" />
          </div>
          <UInput v-model="newStudent.email" type="email" placeholder="Email Address" />
          <USelect
            v-model="newStudent.program"
            :options="programOptions.filter(p => p.value)"
            placeholder="Select Program"
          />
          <USelect
            v-model="newStudent.year"
            :options="yearOptions.filter(y => y.value)"
            placeholder="Select Year"
          />
        </div>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton variant="outline" @click="showAddStudent = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="addStudent">
              Add Student
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
// Remove lodash import and use a simple debounce function
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

definePageMeta({
  layout: 'admin'
})

const { getStudents, getPrograms, getYears, getStatuses, students } = useStudentData()

// Reactive data
const isRefreshing = ref(false)
const showAddStudent = ref(false)
const showAdvancedFilters = ref(false)
const searchQuery = ref('')
const selectedProgram = ref('')
const selectedYear = ref('')
const selectedStatus = ref('')
const selectedFinancialStatus = ref('')
const gpaMin = ref('')
const gpaMax = ref('')
const currentPage = ref(1)
const pageSize = ref(12)

const displayedStudents = ref([])
const pagination = ref({
  page: 1,
  limit: 12,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrev: false
})

const newStudent = ref({
  firstName: '',
  lastName: '',
  email: '',
  program: '',
  year: ''
})

// Options
const programOptions = computed(() => [
  { label: 'All Programs', value: '' },
  ...getPrograms().map(program => ({ label: program, value: program }))
])

const yearOptions = computed(() => [
  { label: 'All Years', value: '' },
  ...getYears().map(year => ({ label: `Year ${year}`, value: year }))
])

const statusOptions = computed(() => [
  { label: 'All Status', value: '' },
  ...getStatuses().map(status => ({ 
    label: status.charAt(0).toUpperCase() + status.slice(1), 
    value: status 
  }))
])

const financialStatusOptions = [
  { label: 'All Financial Status', value: '' },
  { label: 'Current', value: 'current' },
  { label: 'Hold', value: 'hold' },
  { label: 'Delinquent', value: 'delinquent' }
]

// Computed properties
const totalStudents = computed(() => students.value.length)
const activeStudents = computed(() => students.value.filter(s => s.status === 'active').length)
const totalPrograms = computed(() => getPrograms().length)
const averageGPA = computed(() => {
  const total = students.value.reduce((sum, student) => sum + student.gpa, 0)
  return (total / students.value.length).toFixed(2)
})

// Methods
const loadStudents = () => {
  const filters = {
    program: selectedProgram.value,
    year: selectedYear.value,
    status: selectedStatus.value,
    financialStatus: selectedFinancialStatus.value,
    gpaMin: gpaMin.value ? Number.parseFloat(gpaMin.value) : undefined,
    gpaMax: gpaMax.value ? Number.parseFloat(gpaMax.value) : undefined
  }

  const result = getStudents(currentPage.value, pageSize.value, searchQuery.value, filters)
  displayedStudents.value = result.students
  pagination.value = result.pagination
}

const debouncedSearch = debounce(() => {
  currentPage.value = 1
  loadStudents()
}, 300)

const filterStudents = () => {
  currentPage.value = 1
  loadStudents()
}

const clearFilters = () => {
  selectedProgram.value = ''
  selectedYear.value = ''
  selectedStatus.value = ''
  selectedFinancialStatus.value = ''
  gpaMin.value = ''
  gpaMax.value = ''
  searchQuery.value = ''
  currentPage.value = 1
  loadStudents()
}

const refreshStudents = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    loadStudents()
    useToast().add({
      title: 'Students Refreshed',
      description: 'Student data has been updated',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isRefreshing.value = false
  }
}

const viewStudent = (id) => {
  navigateTo(`/students/${id}`)
}

const viewGrades = (id) => {
  navigateTo(`/students/grades/${id}`)
}

const viewTranscript = (id) => {
  navigateTo(`/students/transcripts/${id}`)
}

const addStudent = () => {
  useToast().add({
    title: 'Student Added',
    description: `${newStudent.value.firstName} ${newStudent.value.lastName} has been added`,
    icon: 'i-heroicons-plus'
  })
  showAddStudent.value = false
  newStudent.value = {
    firstName: '',
    lastName: '',
    email: '',
    program: '',
    year: ''
  }
}

const getStatusColor = (status) => {
  const colors = {
    'active': 'green',
    'inactive': 'gray',
    'graduated': 'blue',
    'suspended': 'red'
  }
  return colors[status] || 'gray'
}

const getGPAColor = (gpa) => {
  if (gpa >= 3.5) return 'text-green-600 dark:text-green-400'
  if (gpa >= 3.0) return 'text-blue-600 dark:text-blue-400'
  if (gpa >= 2.5) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-red-600 dark:text-red-400'
}

// Initialize
onMounted(() => {
  loadStudents()
})

// Watch for page changes
watch(currentPage, () => {
  loadStudents()
})
</script>
