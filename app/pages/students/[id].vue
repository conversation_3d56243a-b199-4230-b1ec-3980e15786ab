<template>
  <div v-if="student" class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="flex items-center space-x-4">
        <UButton
          variant="ghost"
          icon="i-heroicons-arrow-left"
          @click="$router.back()"
        >
          Back
        </UButton>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ student.firstName }} {{ student.lastName }}
          </h1>
          <p class="text-gray-600 dark:text-gray-300">{{ student.studentId }} • {{ student.program }}</p>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-envelope"
          @click="sendEmail"
        >
          Email
        </UButton>
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-pencil"
          @click="editStudent"
        >
          Edit
        </UButton>
        <UDropdownMenu :items="actionItems">
          <UButton
            color="gray"
            variant="outline"
            icon="i-heroicons-ellipsis-horizontal"
          >
            Actions
          </UButton>
        </UDropdownMenu>
      </div>
    </div>

    <!-- Student Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Profile Card -->
      <UCard class="lg:col-span-1">
        <div class="text-center">
          <UAvatar :src="student.photo" :alt="`${student.firstName} ${student.lastName}`" size="3xl" class="mx-auto mb-4" />
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            {{ student.firstName }} {{ student.lastName }}
          </h2>
          <p class="text-gray-600 dark:text-gray-300">{{ student.program }}</p>
          <div class="flex justify-center mt-3">
            <UBadge :color="getStatusColor(student.status)" variant="subtle">
              {{ student.status.charAt(0).toUpperCase() + student.status.slice(1) }}
            </UBadge>
          </div>
        </div>

        <div class="mt-6 space-y-4">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Student ID:</span>
            <span class="text-sm font-medium">{{ student.studentId }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Year:</span>
            <span class="text-sm font-medium">{{ student.year }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">GPA:</span>
            <span class="text-sm font-medium" :class="getGPAColor(student.gpa)">{{ student.gpa }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Credits:</span>
            <span class="text-sm font-medium">{{ student.credits.earned }}/{{ student.credits.required }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Financial Status:</span>
            <UBadge :color="getFinancialStatusColor(student.financialStatus)" variant="subtle" size="sm">
              {{ student.financialStatus }}
            </UBadge>
          </div>
        </div>
      </UCard>

      <!-- Academic Progress -->
      <UCard class="lg:col-span-2">
        <template #header>
          <h3 class="text-lg font-semibold">Academic Progress</h3>
        </template>
        
        <div class="space-y-6">
          <!-- Degree Progress -->
          <div>
            <div class="flex justify-between text-sm mb-2">
              <span class="text-gray-600 dark:text-gray-400">Degree Completion</span>
              <span class="font-medium">{{ Math.round((student.credits.earned / student.credits.required) * 100) }}%</span>
            </div>
            <UProgress :value="(student.credits.earned / student.credits.required) * 100" color="blue" />
          </div>

          <!-- Academic Stats Grid -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ student.credits.earned }}</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">Credits Earned</div>
            </div>
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ student.gpa }}</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">Current GPA</div>
            </div>
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ student.year }}</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">Academic Year</div>
            </div>
          </div>

          <!-- Timeline -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Academic Timeline</h4>
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Enrolled</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ formatDate(student.enrollmentDate) }}</p>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Expected Graduation</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ formatDate(student.expectedGraduation) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Contact Information -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">Contact Information</h3>
      </template>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Student Contact</h4>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-envelope" class="w-4 h-4 text-gray-400" />
              <span class="text-sm">{{ student.email }}</span>
            </div>
            <div v-if="student.phone" class="flex items-center space-x-2">
              <UIcon name="i-heroicons-phone" class="w-4 h-4 text-gray-400" />
              <span class="text-sm">{{ student.phone }}</span>
            </div>
            <div v-if="student.address" class="flex items-start space-x-2">
              <UIcon name="i-heroicons-map-pin" class="w-4 h-4 text-gray-400 mt-0.5" />
              <div class="text-sm">
                <div>{{ student.address.street }}</div>
                <div>{{ student.address.city }}, {{ student.address.state }} {{ student.address.zip }}</div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="student.emergencyContact">
          <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Emergency Contact</h4>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-user" class="w-4 h-4 text-gray-400" />
              <span class="text-sm">{{ student.emergencyContact.name }} ({{ student.emergencyContact.relationship }})</span>
            </div>
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-phone" class="w-4 h-4 text-gray-400" />
              <span class="text-sm">{{ student.emergencyContact.phone }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-envelope" class="w-4 h-4 text-gray-400" />
              <span class="text-sm">{{ student.emergencyContact.email }}</span>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Academic Information -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">Academic Information</h3>
      </template>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Program Details</h4>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Program:</span>
              <span class="text-sm font-medium">{{ student.program }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Academic Year:</span>
              <span class="text-sm font-medium">{{ student.year }}</span>
            </div>
            <div v-if="student.academicAdvisor" class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Academic Advisor:</span>
              <span class="text-sm font-medium">{{ student.academicAdvisor }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Enrollment Date:</span>
              <span class="text-sm font-medium">{{ formatDate(student.enrollmentDate) }}</span>
            </div>
          </div>
        </div>

        <div>
          <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Academic Standing</h4>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Current GPA:</span>
              <span class="text-sm font-medium" :class="getGPAColor(student.gpa)">{{ student.gpa }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Credits Earned:</span>
              <span class="text-sm font-medium">{{ student.credits.earned }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Credits Attempted:</span>
              <span class="text-sm font-medium">{{ student.credits.attempted }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">Credits Required:</span>
              <span class="text-sm font-medium">{{ student.credits.required }}</span>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <UButton
        block
        variant="outline"
        icon="i-heroicons-chart-bar-square"
        @click="viewGrades"
      >
        View Grades
      </UButton>
      <UButton
        block
        variant="outline"
        icon="i-heroicons-document-text"
        @click="viewTranscript"
      >
        View Transcript
      </UButton>
      <UButton
        block
        variant="outline"
        icon="i-heroicons-academic-cap"
        @click="viewCourses"
      >
        View Courses
      </UButton>
    </div>
  </div>

  <!-- Loading State -->
  <div v-else class="flex items-center justify-center min-h-96">
    <div class="text-center">
      <UIcon name="i-heroicons-user" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Student Not Found</h3>
      <p class="text-gray-600 dark:text-gray-300">The requested student could not be found.</p>
      <UButton class="mt-4" @click="$router.back()">
        Go Back
      </UButton>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

const route = useRoute()
const { getStudentById } = useStudentData()

// Get student data
const studentId = Number.parseInt(route.params.id as string)
const student = computed(() => getStudentById(studentId))

// Action items for dropdown
const actionItems = [
  [{
    label: 'Send Message',
    icon: 'i-heroicons-chat-bubble-left',
    onSelect: () => sendMessage()
  }, {
    label: 'Schedule Meeting',
    icon: 'i-heroicons-calendar-days',
    onSelect: () => scheduleMeeting()
  }],
  [{
    label: 'Generate Report',
    icon: 'i-heroicons-document-chart-bar',
    onSelect: () => generateReport()
  }, {
    label: 'Export Data',
    icon: 'i-heroicons-arrow-down-tray',
    onSelect: () => exportData()
  }],
  [{
    label: 'Suspend Student',
    icon: 'i-heroicons-no-symbol',
    onSelect: () => suspendStudent()
  }]
]

// Methods
const sendEmail = () => {
  if (student.value) {
    window.location.href = `mailto:${student.value.email}`
  }
}

const editStudent = () => {
  useToast().add({
    title: 'Edit Student',
    description: 'Opening student edit form...',
    icon: 'i-heroicons-pencil'
  })
}

const viewGrades = () => {
  navigateTo(`/students/grades/${studentId}`)
}

const viewTranscript = () => {
  navigateTo(`/students/transcripts/${studentId}`)
}

const viewCourses = () => {
  navigateTo(`/students/courses/${studentId}`)
}

const sendMessage = () => {
  useToast().add({
    title: 'Send Message',
    description: 'Opening message composer...',
    icon: 'i-heroicons-chat-bubble-left'
  })
}

const scheduleMeeting = () => {
  useToast().add({
    title: 'Schedule Meeting',
    description: 'Opening meeting scheduler...',
    icon: 'i-heroicons-calendar-days'
  })
}

const generateReport = () => {
  useToast().add({
    title: 'Generate Report',
    description: 'Generating student report...',
    icon: 'i-heroicons-document-chart-bar'
  })
}

const exportData = () => {
  useToast().add({
    title: 'Export Data',
    description: 'Exporting student data...',
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const suspendStudent = () => {
  useToast().add({
    title: 'Suspend Student',
    description: 'This action requires confirmation',
    color: 'red',
    icon: 'i-heroicons-exclamation-triangle'
  })
}

const getStatusColor = (status: string) => {
  const colors = {
    'active': 'green',
    'inactive': 'gray',
    'graduated': 'blue',
    'suspended': 'red'
  }
  return colors[status] || 'gray'
}

const getGPAColor = (gpa: number) => {
  if (gpa >= 3.5) return 'text-green-600 dark:text-green-400'
  if (gpa >= 3.0) return 'text-blue-600 dark:text-blue-400'
  if (gpa >= 2.5) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-red-600 dark:text-red-400'
}

const getFinancialStatusColor = (status: string) => {
  const colors = {
    'current': 'green',
    'hold': 'yellow',
    'delinquent': 'red'
  }
  return colors[status] || 'gray'
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date)
}

// SEO
useHead({
  title: computed(() => student.value ? `${student.value.firstName} ${student.value.lastName} - Student Profile` : 'Student Profile'),
  meta: [
    { name: 'description', content: computed(() => student.value ? `Profile for ${student.value.firstName} ${student.value.lastName}, ${student.value.program} student` : 'Student profile page') }
  ]
})
</script>
