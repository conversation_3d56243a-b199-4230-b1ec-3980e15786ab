<template>
  <div v-if="student" class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="flex items-center space-x-4">
        <UButton
          variant="ghost"
          icon="i-heroicons-arrow-left"
          @click="$router.back()"
        >
          Back
        </UButton>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Transcript - {{ student.firstName }} {{ student.lastName }}
          </h1>
          <p class="text-gray-600 dark:text-gray-300">{{ student.studentId }} • {{ student.program }}</p>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-printer"
          @click="printTranscript"
        >
          Print
        </UButton>
        <UButton
          color="gray"
          variant="outline"
          icon="i-heroicons-arrow-down-tray"
          @click="downloadTranscript"
        >
          Download PDF
        </UButton>
        <UButton color="primary" icon="i-heroicons-plus" @click="showUploadDocument = true">
          Upload Document
        </UButton>
      </div>
    </div>

    <!-- Student Information -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">Student Information</h2>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-3">
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Name:</span>
            <p class="text-sm">{{ student.firstName }} {{ student.lastName }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Student ID:</span>
            <p class="text-sm">{{ student.studentId }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Program:</span>
            <p class="text-sm">{{ student.program }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Enrollment Date:</span>
            <p class="text-sm">{{ formatDate(student.enrollmentDate) }}</p>
          </div>
        </div>
        <div class="space-y-3">
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Academic Year:</span>
            <p class="text-sm">Year {{ student.year }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Expected Graduation:</span>
            <p class="text-sm">{{ formatDate(student.expectedGraduation) }}</p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Academic Status:</span>
            <UBadge :color="getStatusColor(student.status)" variant="subtle">
              {{ student.status.charAt(0).toUpperCase() + student.status.slice(1) }}
            </UBadge>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Academic Advisor:</span>
            <p class="text-sm">{{ student.academicAdvisor || 'Not Assigned' }}</p>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Academic Summary -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">Academic Summary</h2>
      </template>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ student.gpa }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Cumulative GPA</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ student.credits.earned }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Credits Earned</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ student.credits.attempted }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Credits Attempted</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ Math.round((student.credits.earned / student.credits.required) * 100) }}%</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">Degree Progress</div>
        </div>
      </div>
    </UCard>

    <!-- Academic History by Semester -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Academic History</h2>
          <div class="flex items-center space-x-2">
            <UInput
              v-model="searchQuery"
              placeholder="Search courses..."
              icon="i-heroicons-magnifying-glass"
              size="sm"
              class="w-64"
            />
            <USelect
              v-model="selectedYear"
              :options="yearOptions"
              placeholder="All Years"
            />
          </div>
        </div>
      </template>

      <div class="space-y-6">
        <div
          v-for="semester in filteredTranscriptData"
          :key="`${semester.semester}-${semester.year}`"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <!-- Semester Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-base font-semibold text-gray-900 dark:text-white">{{ semester.semester }} {{ semester.year }}</h3>
            <div class="flex items-center space-x-4 text-sm">
              <span class="text-gray-600 dark:text-gray-400">
                Credits: {{ semester.creditsEarned }}
              </span>
              <span class="text-gray-600 dark:text-gray-400">
                GPA: {{ semester.semesterGPA }}
              </span>
              <UBadge color="green" variant="subtle">
                Completed
              </UBadge>
            </div>
          </div>

          <!-- Course List -->
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead>
                <tr class="border-b border-gray-200 dark:border-gray-700">
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Course</th>
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Title</th>
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Credits</th>
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Grade</th>
                  <th class="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Points</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="course in semester.courses"
                  :key="course.id"
                  class="border-b border-gray-100 dark:border-gray-800"
                >
                  <td class="py-2 text-sm font-medium text-gray-900 dark:text-white">{{ course.courseId }}</td>
                  <td class="py-2 text-sm text-gray-600 dark:text-gray-300">{{ course.courseName }}</td>
                  <td class="py-2 text-sm text-gray-600 dark:text-gray-300">{{ course.credits }}</td>
                  <td class="py-2">
                    <UBadge :color="getGradeColor(course.grade)" variant="subtle" size="sm">
                      {{ course.grade }}
                    </UBadge>
                  </td>
                  <td class="py-2 text-sm text-gray-600 dark:text-gray-300">{{ course.gradePoints }}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Semester Summary -->
          <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="grid grid-cols-3 gap-4 text-sm">
              <div class="text-center">
                <div class="font-medium text-gray-900 dark:text-white">{{ semester.creditsEarned }}</div>
                <div class="text-gray-500 dark:text-gray-400">Credits Earned</div>
              </div>
              <div class="text-center">
                <div class="font-medium text-gray-900 dark:text-white">{{ semester.semesterGPA }}</div>
                <div class="text-gray-500 dark:text-gray-400">Semester GPA</div>
              </div>
              <div class="text-center">
                <div class="font-medium text-gray-900 dark:text-white">{{ semester.cumulativeGPA }}</div>
                <div class="text-gray-500 dark:text-gray-400">Cumulative GPA</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Documents -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Documents</h2>
          <UButton variant="outline" size="sm" @click="showUploadDocument = true">
            Upload Document
          </UButton>
        </div>
      </template>

      <div class="space-y-3">
        <div
          v-for="document in documents"
          :key="document.id"
          class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <UIcon :name="getDocumentIcon(document.type)" class="w-5 h-5 text-gray-400" />
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">{{ document.name }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">{{ document.type }} • {{ formatDate(document.uploadDate) }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <UButton
              size="sm"
              variant="ghost"
              icon="i-heroicons-eye"
              @click="viewDocument(document)"
            >
              View
            </UButton>
            <UButton
              size="sm"
              variant="ghost"
              icon="i-heroicons-arrow-down-tray"
              @click="downloadDocument(document)"
            >
              Download
            </UButton>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="documents.length === 0" class="text-center py-8">
        <UIcon name="i-heroicons-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No documents uploaded</h3>
        <p class="text-gray-600 dark:text-gray-300">Upload transcripts, certificates, and other academic documents.</p>
      </div>
    </UCard>

    <!-- Upload Document Modal -->
    <UModal v-model="showUploadDocument">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Upload Document</h3>
        </template>

        <form @submit.prevent="uploadDocument" class="space-y-4">
          <UInput
            v-model="newDocument.name"
            placeholder="Document Name"
            required
          />
          <USelect
            v-model="newDocument.type"
            :options="documentTypes"
            placeholder="Document Type"
            required
          />
          <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
            <UIcon name="i-heroicons-cloud-arrow-up" class="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-600 dark:text-gray-300">Click to upload or drag and drop</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, DOCX up to 10MB</p>
          </div>
          <UTextarea
            v-model="newDocument.notes"
            placeholder="Notes (optional)"
            rows="2"
          />
        </form>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton variant="outline" @click="showUploadDocument = false">
              Cancel
            </UButton>
            <UButton color="primary" @click="uploadDocument">
              Upload Document
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Transcript Footer -->
    <UCard>
      <div class="text-center text-sm text-gray-600 dark:text-gray-400 space-y-2">
        <p>This is an official transcript issued by Springfield College</p>
        <p>Generated on: {{ new Date().toLocaleDateString() }}</p>
        <p>Transcript ID: TR-{{ student.studentId }}-{{ new Date().getFullYear() }}</p>
      </div>
    </UCard>
  </div>

  <!-- Loading State -->
  <div v-else class="flex items-center justify-center min-h-96">
    <div class="text-center">
      <UIcon name="i-heroicons-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Student Not Found</h3>
      <p class="text-gray-600 dark:text-gray-300">The requested student could not be found.</p>
      <UButton class="mt-4" @click="$router.back()">
        Go Back
      </UButton>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

const route = useRoute()
const { getStudentById } = useStudentData()

// Get student data
const studentId = Number.parseInt(route.params.id)
const student = computed(() => getStudentById(studentId))

// Reactive data
const showUploadDocument = ref(false)
const searchQuery = ref('')
const selectedYear = ref('')

// Mock transcript data
const transcriptData = ref([
  {
    semester: 'Fall',
    year: 2024,
    creditsEarned: 15,
    semesterGPA: '3.75',
    cumulativeGPA: '3.68',
    courses: [
      { id: 1, courseId: 'CS 301', courseName: 'Data Structures', credits: 3, grade: 'A-', gradePoints: 11.1 },
      { id: 2, courseId: 'MATH 201', courseName: 'Calculus II', credits: 4, grade: 'B+', gradePoints: 13.2 },
      { id: 3, courseId: 'ENG 102', courseName: 'Composition', credits: 3, grade: 'A', gradePoints: 12.0 },
      { id: 4, courseId: 'PHYS 101', courseName: 'Physics I', credits: 3, grade: 'B', gradePoints: 9.0 },
      { id: 5, courseId: 'HIST 101', courseName: 'World History', credits: 2, grade: 'A-', gradePoints: 7.4 }
    ]
  },
  {
    semester: 'Spring',
    year: 2024,
    creditsEarned: 16,
    semesterGPA: '3.62',
    cumulativeGPA: '3.65',
    courses: [
      { id: 6, courseId: 'CS 201', courseName: 'Programming II', credits: 3, grade: 'A', gradePoints: 12.0 },
      { id: 7, courseId: 'MATH 101', courseName: 'Calculus I', credits: 4, grade: 'B+', gradePoints: 13.2 },
      { id: 8, courseId: 'ENG 101', courseName: 'Writing', credits: 3, grade: 'A-', gradePoints: 11.1 },
      { id: 9, courseId: 'CHEM 101', courseName: 'Chemistry I', credits: 4, grade: 'B', gradePoints: 12.0 },
      { id: 10, courseId: 'PE 101', courseName: 'Physical Education', credits: 2, grade: 'A', gradePoints: 8.0 }
    ]
  }
])

const documents = ref([
  {
    id: 1,
    name: 'Official Transcript Fall 2024',
    type: 'Transcript',
    uploadDate: new Date('2024-12-15'),
    size: '245 KB'
  },
  {
    id: 2,
    name: 'Degree Audit Report',
    type: 'Academic Report',
    uploadDate: new Date('2024-11-20'),
    size: '156 KB'
  }
])

const newDocument = ref({
  name: '',
  type: '',
  notes: ''
})

// Options
const yearOptions = computed(() => [
  { label: 'All Years', value: '' },
  ...Array.from(new Set(transcriptData.value.map(t => t.year)))
    .sort((a, b) => b - a)
    .map(year => ({ label: year.toString(), value: year.toString() }))
])

const documentTypes = [
  { label: 'Transcript', value: 'Transcript' },
  { label: 'Academic Report', value: 'Academic Report' },
  { label: 'Certificate', value: 'Certificate' },
  { label: 'Diploma', value: 'Diploma' },
  { label: 'Other', value: 'Other' }
]

// Computed properties
const filteredTranscriptData = computed(() => {
  let filtered = [...transcriptData.value]
  
  if (selectedYear.value) {
    filtered = filtered.filter(semester => semester.year.toString() === selectedYear.value)
  }
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.map(semester => ({
      ...semester,
      courses: semester.courses.filter(course =>
        course.courseId.toLowerCase().includes(query) ||
        course.courseName.toLowerCase().includes(query)
      )
    })).filter(semester => semester.courses.length > 0)
  }
  
  return filtered.sort((a, b) => b.year - a.year || (b.semester === 'Fall' ? 1 : -1))
})

// Methods
const printTranscript = () => {
  window.print()
}

const downloadTranscript = () => {
  useToast().add({
    title: 'Download Started',
    description: 'Generating transcript PDF...',
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const uploadDocument = () => {
  documents.value.push({
    id: documents.value.length + 1,
    name: newDocument.value.name,
    type: newDocument.value.type,
    uploadDate: new Date(),
    size: '0 KB'
  })
  
  useToast().add({
    title: 'Document Uploaded',
    description: `${newDocument.value.name} has been uploaded`,
    icon: 'i-heroicons-cloud-arrow-up'
  })
  
  showUploadDocument.value = false
  newDocument.value = {
    name: '',
    type: '',
    notes: ''
  }
}

const viewDocument = (document) => {
  useToast().add({
    title: 'View Document',
    description: `Opening ${document.name}`,
    icon: 'i-heroicons-eye'
  })
}

const downloadDocument = (document) => {
  useToast().add({
    title: 'Download Started',
    description: `Downloading ${document.name}`,
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const getStatusColor = (status) => {
  const colors = {
    'active': 'green',
    'inactive': 'gray',
    'graduated': 'blue',
    'suspended': 'red'
  }
  return colors[status] || 'gray'
}

const getGradeColor = (grade) => {
  if (grade.startsWith('A')) return 'green'
  if (grade.startsWith('B')) return 'blue'
  if (grade.startsWith('C')) return 'yellow'
  if (grade.startsWith('D')) return 'orange'
  return 'red'
}

const getDocumentIcon = (type) => {
  const icons = {
    'Transcript': 'i-heroicons-document-text',
    'Academic Report': 'i-heroicons-document-chart-bar',
    'Certificate': 'i-heroicons-academic-cap',
    'Diploma': 'i-heroicons-trophy',
    'Other': 'i-heroicons-document'
  }
  return icons[type] || 'i-heroicons-document'
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date)
}

// SEO
useHead({
  title: computed(() => student.value ? `${student.value.firstName} ${student.value.lastName} - Transcript` : 'Student Transcript'),
  meta: [
    { name: 'description', content: computed(() => student.value ? `Official transcript for ${student.value.firstName} ${student.value.lastName}` : 'Student transcript management') }
  ]
})
</script>
