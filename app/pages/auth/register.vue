<template>
  <div class="space-y-6">
    <UCard class="w-full">
      <template #header>
        <div class="text-center">
          <div class="w-16 h-16 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UIcon name="i-heroicons-academic-cap" class="w-8 h-8 text-white" />
          </div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create Account</h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">Join our academic community</p>
        </div>
      </template>
      
      <form @submit.prevent="handleRegister" class="space-y-6">
        <UAlert v-if="error" color="red" variant="soft" :title="error" />
        <UAlert v-if="success" color="green" variant="soft" :title="success" />
        
        <div class="space-y-4">
          <!-- Personal Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="First Name" :error="firstNameError">
              <UInput
                v-model="firstName"
                placeholder="John"
                icon="i-heroicons-user"
                autocomplete="given-name"
                :disabled="isLoading"
                size="lg"
              />
            </UFormGroup>
            
            <UFormGroup label="Last Name" :error="lastNameError">
              <UInput
                v-model="lastName"
                placeholder="Doe"
                icon="i-heroicons-user"
                autocomplete="family-name"
                :disabled="isLoading"
                size="lg"
              />
            </UFormGroup>
          </div>
          
          <UFormGroup label="Email Address" :error="emailError">
            <UInput
              v-model="email"
              type="email"
              placeholder="<EMAIL>"
              icon="i-heroicons-envelope"
              autocomplete="email"
              :disabled="isLoading"
              size="lg"
            />
          </UFormGroup>
          
          <UFormGroup label="Student/Employee ID" :error="studentIdError">
            <UInput
              v-model="studentId"
              placeholder="Enter your ID number"
              icon="i-heroicons-identification"
              :disabled="isLoading"
              size="lg"
            />
          </UFormGroup>
          
          <UFormGroup label="Role" :error="roleError">
            <USelect
              v-model="role"
              :options="roleOptions"
              placeholder="Select your role"
              icon="i-heroicons-user-group"
              :disabled="isLoading"
              size="lg"
            />
          </UFormGroup>
          
          <!-- Password Fields -->
          <UFormGroup label="Password" :error="passwordError">
            <UInput
              v-model="password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="Create a strong password"
              icon="i-heroicons-lock-closed"
              autocomplete="new-password"
              :disabled="isLoading"
              size="lg"
            >
              <template #trailing>
                <UButton
                  :icon="showPassword ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
                  variant="ghost"
                  color="gray"
                  size="sm"
                  @click="showPassword = !showPassword"
                />
              </template>
            </UInput>
            <template #hint>
              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Password must be at least 8 characters with uppercase, lowercase, number, and special character
              </div>
            </template>
          </UFormGroup>
          
          <UFormGroup label="Confirm Password" :error="confirmPasswordError">
            <UInput
              v-model="confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              placeholder="Confirm your password"
              icon="i-heroicons-lock-closed"
              autocomplete="new-password"
              :disabled="isLoading"
              size="lg"
            >
              <template #trailing>
                <UButton
                  :icon="showConfirmPassword ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
                  variant="ghost"
                  color="gray"
                  size="sm"
                  @click="showConfirmPassword = !showConfirmPassword"
                />
              </template>
            </UInput>
          </UFormGroup>
          
          <!-- Password Strength Indicator -->
          <div v-if="password" class="space-y-2">
            <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Password Strength</div>
            <UProgress :value="passwordStrength" :color="passwordStrengthColor" size="sm" />
            <div class="text-xs text-gray-500 dark:text-gray-400">{{ passwordStrengthText }}</div>
          </div>
          
          <!-- Terms and Conditions -->
          <div class="space-y-3">
            <UCheckbox 
              v-model="agreeToTerms" 
              :error="termsError"
            >
              <template #label>
                <span class="text-sm">
                  I agree to the 
                  <NuxtLink to="/terms" class="text-primary-600 hover:text-primary-500 dark:text-primary-400">
                    Terms of Service
                  </NuxtLink>
                  and 
                  <NuxtLink to="/privacy" class="text-primary-600 hover:text-primary-500 dark:text-primary-400">
                    Privacy Policy
                  </NuxtLink>
                </span>
              </template>
            </UCheckbox>
            
            <UCheckbox v-model="subscribeToUpdates">
              <template #label>
                <span class="text-sm">Send me updates about new features and announcements</span>
              </template>
            </UCheckbox>
          </div>
          
          <UButton
            type="submit"
            block
            size="lg"
            :loading="isLoading"
            :disabled="isLoading"
          >
            Create Account
          </UButton>
        </div>
      </form>
      
      <!-- SSO Options -->
      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
              Or sign up with
            </span>
          </div>
        </div>
        
        <div class="mt-6 grid grid-cols-2 gap-3">
          <UButton
            variant="outline"
            color="gray"
            size="lg"
            @click="signUpWithGoogle"
            :disabled="isLoading"
          >
            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Google
          </UButton>
          
          <UButton
            variant="outline"
            color="gray"
            size="lg"
            @click="signUpWithMicrosoft"
            :disabled="isLoading"
          >
            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="#F25022" d="M1 1h10v10H1z"/>
              <path fill="#00A4EF" d="M13 1h10v10H13z"/>
              <path fill="#7FBA00" d="M1 13h10v10H1z"/>
              <path fill="#FFB900" d="M13 13h10v10H13z"/>
            </svg>
            Microsoft
          </UButton>
        </div>
      </div>
      
      <template #footer>
        <div class="text-center text-sm text-gray-600 dark:text-gray-300">
          Already have an account?
          <NuxtLink 
            to="/auth/login" 
            class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
          >
            Sign in
          </NuxtLink>
        </div>
      </template>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'auth'
})

const { login } = useNavigation()

// Form fields
const firstName = ref('')
const lastName = ref('')
const email = ref('')
const studentId = ref('')
const role = ref('')
const password = ref('')
const confirmPassword = ref('')
const agreeToTerms = ref(false)
const subscribeToUpdates = ref(false)

// UI state
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isLoading = ref(false)
const error = ref('')
const success = ref('')

// Validation errors
const firstNameError = ref('')
const lastNameError = ref('')
const emailError = ref('')
const studentIdError = ref('')
const roleError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')
const termsError = ref('')

// Role options
const roleOptions = [
  { label: 'Student', value: 'student' },
  { label: 'Faculty', value: 'faculty' },
  { label: 'Staff', value: 'staff' }
]

// Password strength calculation
const passwordStrength = computed(() => {
  if (!password.value) return 0
  
  let strength = 0
  if (password.value.length >= 8) strength += 25
  if (/[a-z]/.test(password.value)) strength += 25
  if (/[A-Z]/.test(password.value)) strength += 25
  if (/[0-9]/.test(password.value)) strength += 12.5
  if (/[^A-Za-z0-9]/.test(password.value)) strength += 12.5
  
  return Math.min(strength, 100)
})

const passwordStrengthColor = computed(() => {
  if (passwordStrength.value < 25) return 'red'
  if (passwordStrength.value < 50) return 'orange'
  if (passwordStrength.value < 75) return 'yellow'
  return 'green'
})

const passwordStrengthText = computed(() => {
  if (passwordStrength.value < 25) return 'Weak'
  if (passwordStrength.value < 50) return 'Fair'
  if (passwordStrength.value < 75) return 'Good'
  return 'Strong'
})

const validateForm = () => {
  // Reset errors
  error.value = ''
  firstNameError.value = ''
  lastNameError.value = ''
  emailError.value = ''
  studentIdError.value = ''
  roleError.value = ''
  passwordError.value = ''
  confirmPasswordError.value = ''
  termsError.value = ''
  
  let isValid = true
  
  if (!firstName.value.trim()) {
    firstNameError.value = 'First name is required'
    isValid = false
  }
  
  if (!lastName.value.trim()) {
    lastNameError.value = 'Last name is required'
    isValid = false
  }
  
  if (!email.value) {
    emailError.value = 'Email is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
    emailError.value = 'Please enter a valid email address'
    isValid = false
  }
  
  if (!studentId.value.trim()) {
    studentIdError.value = 'Student/Employee ID is required'
    isValid = false
  }
  
  if (!role.value) {
    roleError.value = 'Please select your role'
    isValid = false
  }
  
  if (!password.value) {
    passwordError.value = 'Password is required'
    isValid = false
  } else if (passwordStrength.value < 50) {
    passwordError.value = 'Password is too weak. Please create a stronger password.'
    isValid = false
  }
  
  if (!confirmPassword.value) {
    confirmPasswordError.value = 'Please confirm your password'
    isValid = false
  } else if (password.value !== confirmPassword.value) {
    confirmPasswordError.value = 'Passwords do not match'
    isValid = false
  }
  
  if (!agreeToTerms.value) {
    termsError.value = 'You must agree to the terms and conditions'
    isValid = false
  }
  
  return isValid
}

const handleRegister = async () => {
  if (!validateForm()) return
  
  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock successful registration
    success.value = 'Account created successfully! Redirecting to dashboard...'
    
    // Simulate login after registration
    setTimeout(() => {
      login(role.value)
      navigateTo(`/${role.value}`)
    }, 1500)
    
  } catch (err) {
    error.value = 'Registration failed. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const signUpWithGoogle = () => {
  useToast().add({
    title: 'Google SSO',
    description: 'Google SSO integration will be implemented in a future task',
    icon: 'i-heroicons-information-circle'
  })
}

const signUpWithMicrosoft = () => {
  useToast().add({
    title: 'Microsoft SSO',
    description: 'Microsoft SSO integration will be implemented in a future task',
    icon: 'i-heroicons-information-circle'
  })
}
</script>
