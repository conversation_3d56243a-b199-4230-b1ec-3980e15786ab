<template>
  <div class="space-y-6">
    <UCard class="w-full">
      <template #header>
        <div class="text-center">
          <div class="w-16 h-16 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UIcon name="i-heroicons-key" class="w-8 h-8 text-white" />
          </div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ step === 'request' ? 'Reset Password' : 'Create New Password' }}
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            {{ step === 'request' 
              ? 'Enter your email to receive reset instructions' 
              : 'Enter your new password below' 
            }}
          </p>
        </div>
      </template>
      
      <!-- Step 1: Request Password Reset -->
      <form v-if="step === 'request'" @submit.prevent="handlePasswordReset" class="space-y-6">
        <UAlert v-if="error" color="red" variant="soft" :title="error" />
        <UAlert v-if="success" color="green" variant="soft" :title="success" />
        
        <div class="space-y-4">
          <UFormGroup label="Email Address" :error="emailError">
            <UInput
              v-model="email"
              type="email"
              placeholder="<EMAIL>"
              icon="i-heroicons-envelope"
              autocomplete="email"
              :disabled="isLoading"
              size="lg"
            />
            <template #hint>
              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                We'll send password reset instructions to this email address
              </div>
            </template>
          </UFormGroup>
          
          <UButton
            type="submit"
            block
            size="lg"
            :loading="isLoading"
            :disabled="isLoading || success"
          >
            {{ success ? 'Email Sent' : 'Send Reset Instructions' }}
          </UButton>
        </div>
      </form>
      
      <!-- Step 2: Reset Password Form -->
      <form v-else @submit.prevent="handleNewPassword" class="space-y-6">
        <UAlert v-if="error" color="red" variant="soft" :title="error" />
        <UAlert v-if="success" color="green" variant="soft" :title="success" />
        
        <div class="space-y-4">
          <UFormGroup label="New Password" :error="passwordError">
            <UInput
              v-model="newPassword"
              :type="showPassword ? 'text' : 'password'"
              placeholder="Create a strong password"
              icon="i-heroicons-lock-closed"
              autocomplete="new-password"
              :disabled="isLoading"
              size="lg"
            >
              <template #trailing>
                <UButton
                  :icon="showPassword ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
                  variant="ghost"
                  color="gray"
                  size="sm"
                  @click="showPassword = !showPassword"
                />
              </template>
            </UInput>
            <template #hint>
              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Password must be at least 8 characters with uppercase, lowercase, number, and special character
              </div>
            </template>
          </UFormGroup>
          
          <UFormGroup label="Confirm New Password" :error="confirmPasswordError">
            <UInput
              v-model="confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              placeholder="Confirm your new password"
              icon="i-heroicons-lock-closed"
              autocomplete="new-password"
              :disabled="isLoading"
              size="lg"
            >
              <template #trailing>
                <UButton
                  :icon="showConfirmPassword ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
                  variant="ghost"
                  color="gray"
                  size="sm"
                  @click="showConfirmPassword = !showConfirmPassword"
                />
              </template>
            </UInput>
          </UFormGroup>
          
          <!-- Password Strength Indicator -->
          <div v-if="newPassword" class="space-y-2">
            <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Password Strength</div>
            <UProgress :value="passwordStrength" :color="passwordStrengthColor" size="sm" />
            <div class="text-xs text-gray-500 dark:text-gray-400">{{ passwordStrengthText }}</div>
          </div>
          
          <UButton
            type="submit"
            block
            size="lg"
            :loading="isLoading"
            :disabled="isLoading"
          >
            Update Password
          </UButton>
        </div>
      </form>
      
      <template #footer>
        <div class="text-center space-y-2">
          <div class="text-sm text-gray-600 dark:text-gray-300">
            Remember your password?
            <NuxtLink 
              to="/auth/login" 
              class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
            >
              Sign in
            </NuxtLink>
          </div>
          
          <div v-if="step === 'reset'" class="text-sm text-gray-600 dark:text-gray-300">
            Didn't receive the email?
            <UButton
              @click="step = 'request'; success = ''; error = ''"
              variant="ghost"
              size="xs"
              class="p-0 h-auto font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
            >
              Try again
            </UButton>
          </div>
        </div>
      </template>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'auth'
})

const route = useRoute()

// Determine step based on query parameters
const step = ref(route.query.token ? 'reset' : 'request')

// Form fields
const email = ref('')
const newPassword = ref('')
const confirmPassword = ref('')

// UI state
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isLoading = ref(false)
const error = ref('')
const success = ref('')

// Validation errors
const emailError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')

// Password strength calculation
const passwordStrength = computed(() => {
  if (!newPassword.value) return 0
  
  let strength = 0
  if (newPassword.value.length >= 8) strength += 25
  if (/[a-z]/.test(newPassword.value)) strength += 25
  if (/[A-Z]/.test(newPassword.value)) strength += 25
  if (/[0-9]/.test(newPassword.value)) strength += 12.5
  if (/[^A-Za-z0-9]/.test(newPassword.value)) strength += 12.5
  
  return Math.min(strength, 100)
})

const passwordStrengthColor = computed(() => {
  if (passwordStrength.value < 25) return 'red'
  if (passwordStrength.value < 50) return 'orange'
  if (passwordStrength.value < 75) return 'yellow'
  return 'green'
})

const passwordStrengthText = computed(() => {
  if (passwordStrength.value < 25) return 'Weak'
  if (passwordStrength.value < 50) return 'Fair'
  if (passwordStrength.value < 75) return 'Good'
  return 'Strong'
})

const validateEmailForm = () => {
  error.value = ''
  emailError.value = ''
  
  if (!email.value) {
    emailError.value = 'Email is required'
    return false
  }
  
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
    emailError.value = 'Please enter a valid email address'
    return false
  }
  
  return true
}

const validatePasswordForm = () => {
  error.value = ''
  passwordError.value = ''
  confirmPasswordError.value = ''
  
  if (!newPassword.value) {
    passwordError.value = 'Password is required'
    return false
  }
  
  if (passwordStrength.value < 50) {
    passwordError.value = 'Password is too weak. Please create a stronger password.'
    return false
  }
  
  if (!confirmPassword.value) {
    confirmPasswordError.value = 'Please confirm your password'
    return false
  }
  
  if (newPassword.value !== confirmPassword.value) {
    confirmPasswordError.value = 'Passwords do not match'
    return false
  }
  
  return true
}

const handlePasswordReset = async () => {
  if (!validateEmailForm()) return
  
  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Mock successful email send
    success.value = 'Password reset instructions have been sent to your email address. Please check your inbox and follow the instructions.'
    
    // In a real app, this would send an email with a reset token
    // For demo purposes, we'll show a button to simulate the email link
    setTimeout(() => {
      if (success.value) {
        success.value += ' For demo purposes, you can proceed to reset your password.'
        // Add a demo button or automatically proceed to reset step
      }
    }, 2000)
    
  } catch (err) {
    error.value = 'Failed to send reset instructions. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const handleNewPassword = async () => {
  if (!validatePasswordForm()) return
  
  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Mock successful password reset
    success.value = 'Your password has been successfully updated! Redirecting to login...'
    
    // Redirect to login after success
    setTimeout(() => {
      navigateTo('/auth/login')
    }, 2000)
    
  } catch (err) {
    error.value = 'Failed to update password. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// Watch for route changes to handle token-based reset
watch(() => route.query.token, (token) => {
  if (token) {
    step.value = 'reset'
  }
})

// Auto-focus email field on mount for request step
onMounted(() => {
  nextTick(() => {
    if (step.value === 'request') {
      const emailInput = document.querySelector('input[type="email"]')
      if (emailInput) {
        emailInput.focus()
      }
    } else {
      const passwordInput = document.querySelector('input[type="password"]')
      if (passwordInput) {
        passwordInput.focus()
      }
    }
  })
})

// Demo function to simulate email link click
const proceedToReset = () => {
  step.value = 'reset'
  success.value = ''
  error.value = ''
}

// Expose demo function for testing
if (process.dev) {
  window.proceedToPasswordReset = proceedToReset
}
</script>
