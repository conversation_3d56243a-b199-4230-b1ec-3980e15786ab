<template>
  <div class="space-y-6">
    <UCard class="w-full">
      <template #header>
        <div class="text-center">
          <div class="w-16 h-16 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UIcon name="i-heroicons-academic-cap" class="w-8 h-8 text-white" />
          </div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome Back</h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">Sign in to your account</p>
        </div>
      </template>
      
      <form @submit.prevent="handleLogin" class="space-y-6">
        <UAlert v-if="error" color="red" variant="soft" :title="error" />
        
        <div class="space-y-4">
          <UFormGroup label="Email Address" :error="emailError">
            <UInput
              v-model="email"
              type="email"
              placeholder="<EMAIL>"
              icon="i-heroicons-envelope"
              autocomplete="email"
              :disabled="isLoading"
              size="lg"
            />
          </UFormGroup>
          
          <UFormGroup label="Password" :error="passwordError">
            <UInput
              v-model="password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="Enter your password"
              icon="i-heroicons-lock-closed"
              autocomplete="current-password"
              :disabled="isLoading"
              size="lg"
            >
              <template #trailing>
                <UButton
                  :icon="showPassword ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
                  variant="ghost"
                  color="gray"
                  size="sm"
                  @click="showPassword = !showPassword"
                />
              </template>
            </UInput>
          </UFormGroup>
          
          <div class="flex items-center justify-between">
            <UCheckbox v-model="rememberMe" label="Remember me" />
            <NuxtLink 
              to="/auth/reset-password" 
              class="text-sm font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
            >
              Forgot password?
            </NuxtLink>
          </div>
          
          <UButton
            type="submit"
            block
            size="lg"
            :loading="isLoading"
            :disabled="isLoading"
          >
            Sign In
          </UButton>
        </div>
      </form>
      
      <!-- SSO Options -->
      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
              Or continue with
            </span>
          </div>
        </div>
        
        <div class="mt-6 grid grid-cols-2 gap-3">
          <UButton
            variant="outline"
            color="gray"
            size="lg"
            @click="signInWithGoogle"
            :disabled="isLoading"
          >
            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Google
          </UButton>
          
          <UButton
            variant="outline"
            color="gray"
            size="lg"
            @click="signInWithMicrosoft"
            :disabled="isLoading"
          >
            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="#F25022" d="M1 1h10v10H1z"/>
              <path fill="#00A4EF" d="M13 1h10v10H13z"/>
              <path fill="#7FBA00" d="M1 13h10v10H1z"/>
              <path fill="#FFB900" d="M13 13h10v10H13z"/>
            </svg>
            Microsoft
          </UButton>
        </div>
      </div>
      
      <template #footer>
        <div class="text-center text-sm text-gray-600 dark:text-gray-300">
          Don't have an account?
          <NuxtLink 
            to="/auth/register" 
            class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
          >
            Sign up
          </NuxtLink>
        </div>
      </template>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'auth'
})

const { login } = useNavigation()

const email = ref('')
const password = ref('')
const rememberMe = ref(false)
const showPassword = ref(false)
const isLoading = ref(false)
const error = ref('')
const emailError = ref('')
const passwordError = ref('')

const validateForm = () => {
  error.value = ''
  emailError.value = ''
  passwordError.value = ''
  
  let isValid = true
  
  if (!email.value) {
    emailError.value = 'Email is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
    emailError.value = 'Please enter a valid email address'
    isValid = false
  }
  
  if (!password.value) {
    passwordError.value = 'Password is required'
    isValid = false
  } else if (password.value.length < 6) {
    passwordError.value = 'Password must be at least 6 characters'
    isValid = false
  }
  
  return isValid
}

const handleLogin = async () => {
  if (!validateForm()) return
  
  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Mock authentication logic
    if (email.value === '<EMAIL>') {
      login('admin')
      navigateTo('/admin')
    } else if (email.value === '<EMAIL>') {
      login('student')
      navigateTo('/student')
    } else if (email.value === '<EMAIL>') {
      login('faculty')
      navigateTo('/faculty')
    } else {
      // Default to student role for any other email
      login('student')
      navigateTo('/student')
    }
    
    // Store remember me preference
    if (rememberMe.value) {
      localStorage.setItem('remember_me', 'true')
    }
    
  } catch (err) {
    error.value = 'Invalid email or password. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const signInWithGoogle = () => {
  // Mock Google SSO
  useToast().add({
    title: 'Google SSO',
    description: 'Google SSO integration will be implemented in a future task',
    icon: 'i-heroicons-information-circle'
  })
}

const signInWithMicrosoft = () => {
  // Mock Microsoft SSO
  useToast().add({
    title: 'Microsoft SSO',
    description: 'Microsoft SSO integration will be implemented in a future task',
    icon: 'i-heroicons-information-circle'
  })
}

// Auto-focus email field on mount
onMounted(() => {
  nextTick(() => {
    const emailInput = document.querySelector('input[type="email"]')
    if (emailInput) {
      emailInput.focus()
    }
  })
})
</script>
