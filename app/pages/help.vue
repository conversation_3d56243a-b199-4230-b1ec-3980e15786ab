<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <UContainer class="py-12">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Help & Support</h1>
        <p class="text-lg text-gray-600 dark:text-gray-300">
          Find answers to common questions and get the help you need
        </p>
      </div>

      <!-- Search -->
      <div class="max-w-2xl mx-auto mb-12">
        <UInput
          v-model="searchQuery"
          placeholder="Search for help articles..."
          icon="i-heroicons-magnifying-glass"
          size="lg"
          class="w-full"
          @input="filterArticles"
        />
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <UCard class="text-center hover:shadow-lg transition-shadow cursor-pointer">
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UIcon name="i-heroicons-chat-bubble-left-right" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Live Chat</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Get instant help from our support team</p>
          <UButton color="blue" variant="outline">Start Chat</UButton>
        </UCard>

        <UCard class="text-center hover:shadow-lg transition-shadow cursor-pointer">
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UIcon name="i-heroicons-envelope" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Email Support</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Send us a detailed message</p>
          <UButton color="green" variant="outline">Send Email</UButton>
        </UCard>

        <UCard class="text-center hover:shadow-lg transition-shadow cursor-pointer">
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UIcon name="i-heroicons-phone" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Phone Support</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Call us for immediate assistance</p>
          <UButton color="purple" variant="outline">Call Now</UButton>
        </UCard>
      </div>

      <!-- Help Categories -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Categories Sidebar -->
        <div class="lg:col-span-1">
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Help Categories</h3>
            </template>
            <nav class="space-y-1">
              <UButton
                v-for="category in helpCategories"
                :key="category.id"
                @click="selectedCategory = category.id"
                variant="ghost"
                :color="selectedCategory === category.id ? 'primary' : 'gray'"
                class="w-full justify-start px-3 py-2"
                size="sm"
              >
                <template #leading>
                  <UIcon :name="category.icon" class="w-4 h-4" />
                </template>
                <div class="flex items-center justify-between w-full">
                  <span>{{ category.name }}</span>
                  <UBadge size="xs" variant="soft" color="gray">
                    {{ category.count }}
                  </UBadge>
                </div>
              </UButton>
            </nav>
          </UCard>

          <!-- Contact Info -->
          <UCard class="mt-6">
            <template #header>
              <h3 class="text-lg font-semibold">Contact Information</h3>
            </template>
            <div class="space-y-4">
              <div class="flex items-center">
                <UIcon name="i-heroicons-envelope" class="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Email</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300"><EMAIL></p>
                </div>
              </div>
              <div class="flex items-center">
                <UIcon name="i-heroicons-phone" class="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Phone</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">(*************</p>
                </div>
              </div>
              <div class="flex items-center">
                <UIcon name="i-heroicons-clock" class="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Hours</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">Mon-Fri 8AM-6PM</p>
                </div>
              </div>
            </div>
          </UCard>
        </div>

        <!-- Help Articles -->
        <div class="lg:col-span-2">
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">
                  {{ getCurrentCategoryName() }}
                </h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {{ filteredArticles.length }} articles
                </span>
              </div>
            </template>

            <div class="space-y-4">
              <div
                v-for="article in filteredArticles"
                :key="article.id"
                class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
                @click="openArticle(article)"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="text-base font-medium text-gray-900 dark:text-white mb-2">
                      {{ article.title }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">
                      {{ article.excerpt }}
                    </p>
                    <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <span>{{ article.category }}</span>
                      <span>•</span>
                      <span>{{ article.readTime }} min read</span>
                      <span>•</span>
                      <span>Updated {{ formatDate(article.updatedAt) }}</span>
                    </div>
                  </div>
                  <UIcon name="i-heroicons-chevron-right" class="w-5 h-5 text-gray-400 ml-4" />
                </div>
              </div>

              <!-- No Results -->
              <div v-if="filteredArticles.length === 0" class="text-center py-12">
                <UIcon name="i-heroicons-document-magnifying-glass" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No articles found</h3>
                <p class="text-gray-600 dark:text-gray-300">
                  Try adjusting your search or browse different categories.
                </p>
              </div>
            </div>
          </UCard>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="mt-12">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Frequently Asked Questions</h3>
          </template>
          <div class="space-y-4">
            <div
              v-for="faq in frequentlyAsked"
              :key="faq.id"
              class="border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <UButton
                @click="toggleFaq(faq.id)"
                variant="ghost"
                color="gray"
                class="w-full justify-between px-4 py-3 h-auto"
                size="sm"
              >
                <span class="font-medium text-gray-900 dark:text-white">{{ faq.question }}</span>
                <template #trailing>
                  <UIcon
                    :name="expandedFaq === faq.id ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'"
                    class="w-5 h-5 text-gray-400"
                  />
                </template>
              </UButton>
              <div v-if="expandedFaq === faq.id" class="px-4 pb-3">
                <p class="text-gray-600 dark:text-gray-300">{{ faq.answer }}</p>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Back to Home -->
      <div class="text-center mt-12">
        <UButton to="/" variant="outline" icon="i-heroicons-arrow-left">
          Back to Home
        </UButton>
      </div>
    </UContainer>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'default'
})

// Reactive data
const searchQuery = ref('')
const selectedCategory = ref('all')
const expandedFaq = ref(null)

// Help categories
const helpCategories = [
  { id: 'all', name: 'All Articles', icon: 'i-heroicons-document-text', count: 24 },
  { id: 'getting-started', name: 'Getting Started', icon: 'i-heroicons-play', count: 6 },
  { id: 'courses', name: 'Courses', icon: 'i-heroicons-academic-cap', count: 8 },
  { id: 'grades', name: 'Grades', icon: 'i-heroicons-chart-bar', count: 4 },
  { id: 'account', name: 'Account', icon: 'i-heroicons-user', count: 3 },
  { id: 'technical', name: 'Technical Issues', icon: 'i-heroicons-wrench-screwdriver', count: 3 }
]

// Help articles
const helpArticles = ref([
  {
    id: 1,
    title: 'How to log in to your account',
    excerpt: 'Learn how to access your student or faculty account for the first time.',
    category: 'Getting Started',
    categoryId: 'getting-started',
    readTime: 3,
    updatedAt: new Date('2024-01-10')
  },
  {
    id: 2,
    title: 'Enrolling in courses',
    excerpt: 'Step-by-step guide to course registration and enrollment.',
    category: 'Courses',
    categoryId: 'courses',
    readTime: 5,
    updatedAt: new Date('2024-01-12')
  },
  {
    id: 3,
    title: 'Viewing your grades',
    excerpt: 'How to check your current grades and academic progress.',
    category: 'Grades',
    categoryId: 'grades',
    readTime: 2,
    updatedAt: new Date('2024-01-08')
  },
  {
    id: 4,
    title: 'Updating your profile',
    excerpt: 'Change your personal information, photo, and contact details.',
    category: 'Account',
    categoryId: 'account',
    readTime: 4,
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 5,
    title: 'Troubleshooting login issues',
    excerpt: 'Common solutions for login problems and password resets.',
    category: 'Technical Issues',
    categoryId: 'technical',
    readTime: 6,
    updatedAt: new Date('2024-01-14')
  }
])

const filteredArticles = ref([...helpArticles.value])

// FAQ data
const frequentlyAsked = [
  {
    id: 1,
    question: 'How do I reset my password?',
    answer: 'You can reset your password by clicking the "Forgot Password" link on the login page and following the instructions sent to your email.'
  },
  {
    id: 2,
    question: 'When is course registration open?',
    answer: 'Course registration typically opens 2 weeks before each semester begins. Check the academic calendar for specific dates.'
  },
  {
    id: 3,
    question: 'How do I contact my instructor?',
    answer: 'You can contact your instructor through the messaging system in the course page or use their email address listed in the course information.'
  },
  {
    id: 4,
    question: 'Can I drop a course after enrollment?',
    answer: 'Yes, you can drop courses within the add/drop period. Check the academic calendar for deadlines and any applicable fees.'
  }
]

// Methods
const filterArticles = () => {
  let filtered = [...helpArticles.value]
  
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(article => article.categoryId === selectedCategory.value)
  }
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(article => 
      article.title.toLowerCase().includes(query) ||
      article.excerpt.toLowerCase().includes(query) ||
      article.category.toLowerCase().includes(query)
    )
  }
  
  filteredArticles.value = filtered
}

const getCurrentCategoryName = () => {
  const category = helpCategories.find(cat => cat.id === selectedCategory.value)
  return category ? category.name : 'All Articles'
}

const openArticle = (article) => {
  useToast().add({
    title: 'Opening Article',
    description: `Opening "${article.title}"`,
    icon: 'i-heroicons-document-text'
  })
}

const toggleFaq = (id) => {
  expandedFaq.value = expandedFaq.value === id ? null : id
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date)
}

// Watch for category changes
watch(selectedCategory, () => {
  filterArticles()
})

// SEO
useHead({
  title: 'Help & Support - College Management System',
  meta: [
    { name: 'description', content: 'Get help and support for the College Management System. Find answers to common questions and contact our support team.' }
  ]
})

// Initialize
onMounted(() => {
  filterArticles()
})
</script>
