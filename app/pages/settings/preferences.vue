<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Preferences</h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Customize your experience and notification settings
        </p>
      </div>

      <div class="space-y-8">
        <!-- Appearance Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Appearance</h3>
          </template>
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Theme
              </label>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div
                  v-for="theme in themeOptions"
                  :key="theme.value"
                  @click="preferences.theme = theme.value"
                  :class="[
                    'relative rounded-lg border-2 cursor-pointer transition-colors p-4',
                    preferences.theme === theme.value
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  ]"
                >
                  <div class="flex items-center">
                    <UIcon :name="theme.icon" class="w-5 h-5 mr-3" />
                    <div>
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ theme.label }}</div>
                      <div class="text-xs text-gray-500 dark:text-gray-400">{{ theme.description }}</div>
                    </div>
                  </div>
                  <div
                    v-if="preferences.theme === theme.value"
                    class="absolute top-2 right-2"
                  >
                    <UIcon name="i-heroicons-check-circle" class="w-5 h-5 text-primary-500" />
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Language
              </label>
              <USelect v-model="preferences.language" :options="languageOptions" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Time Zone
              </label>
              <USelect v-model="preferences.timezone" :options="timezoneOptions" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Date Format
              </label>
              <USelect v-model="preferences.dateFormat" :options="dateFormatOptions" />
            </div>
          </div>
        </UCard>

        <!-- Notification Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Notifications</h3>
          </template>
          <div class="space-y-6">
            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Email Notifications</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Receive notifications via email</p>
                </div>
                <UToggle v-model="preferences.notifications.email" />
              </div>
            </div>

            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Push Notifications</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Receive browser push notifications</p>
                </div>
                <UToggle v-model="preferences.notifications.push" />
              </div>
            </div>

            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">SMS Notifications</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Receive important alerts via SMS</p>
                </div>
                <UToggle v-model="preferences.notifications.sms" />
              </div>
            </div>

            <!-- Notification Types -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Notification Types</h4>
              <div class="space-y-4">
                <div
                  v-for="type in notificationTypes"
                  :key="type.key"
                  class="flex items-center justify-between"
                >
                  <div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ type.label }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ type.description }}</div>
                  </div>
                  <UToggle v-model="preferences.notificationTypes[type.key]" />
                </div>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Privacy Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Privacy</h3>
          </template>
          <div class="space-y-6">
            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Profile Visibility</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Allow others to see your profile</p>
                </div>
                <UToggle v-model="preferences.privacy.profileVisible" />
              </div>
            </div>

            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Show Online Status</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Display when you're online</p>
                </div>
                <UToggle v-model="preferences.privacy.showOnlineStatus" />
              </div>
            </div>

            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Allow Direct Messages</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Let others send you direct messages</p>
                </div>
                <UToggle v-model="preferences.privacy.allowDirectMessages" />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Who can see your email
              </label>
              <USelect v-model="preferences.privacy.emailVisibility" :options="visibilityOptions" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Who can see your phone number
              </label>
              <USelect v-model="preferences.privacy.phoneVisibility" :options="visibilityOptions" />
            </div>
          </div>
        </UCard>

        <!-- Academic Preferences -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Academic Preferences</h3>
          </template>
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Default Course View
              </label>
              <USelect v-model="preferences.academic.defaultCourseView" :options="courseViewOptions" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Grade Display Format
              </label>
              <USelect v-model="preferences.academic.gradeFormat" :options="gradeFormatOptions" />
            </div>

            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Auto-save Drafts</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Automatically save assignment drafts</p>
                </div>
                <UToggle v-model="preferences.academic.autoSaveDrafts" />
              </div>
            </div>

            <div>
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">Show Upcoming Deadlines</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Display assignment deadlines on dashboard</p>
                </div>
                <UToggle v-model="preferences.academic.showDeadlines" />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Calendar Start Day
              </label>
              <USelect v-model="preferences.academic.calendarStartDay" :options="dayOptions" />
            </div>
          </div>
        </UCard>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
          <UButton variant="outline" @click="resetPreferences">
            Reset to Defaults
          </UButton>
          <UButton color="primary" @click="savePreferences" :loading="isSaving">
            Save Preferences
          </UButton>
        </div>
      </div>

      <!-- Back Button -->
      <div class="text-center mt-8">
        <UButton variant="outline" icon="i-heroicons-arrow-left" @click="$router.back()">
          Back
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'default'
})

// Reactive data
const isSaving = ref(false)

const preferences = ref({
  theme: 'system',
  language: 'en',
  timezone: 'America/New_York',
  dateFormat: 'MM/DD/YYYY',
  notifications: {
    email: true,
    push: true,
    sms: false
  },
  notificationTypes: {
    grades: true,
    assignments: true,
    announcements: true,
    messages: true,
    deadlines: true,
    events: false
  },
  privacy: {
    profileVisible: true,
    showOnlineStatus: true,
    allowDirectMessages: true,
    emailVisibility: 'faculty-only',
    phoneVisibility: 'private'
  },
  academic: {
    defaultCourseView: 'grid',
    gradeFormat: 'letter',
    autoSaveDrafts: true,
    showDeadlines: true,
    calendarStartDay: 'monday'
  }
})

// Options
const themeOptions = [
  {
    value: 'light',
    label: 'Light',
    description: 'Light theme',
    icon: 'i-heroicons-sun'
  },
  {
    value: 'dark',
    label: 'Dark',
    description: 'Dark theme',
    icon: 'i-heroicons-moon'
  },
  {
    value: 'system',
    label: 'System',
    description: 'Follow system',
    icon: 'i-heroicons-computer-desktop'
  }
]

const languageOptions = [
  { label: 'English', value: 'en' },
  { label: 'Spanish', value: 'es' },
  { label: 'French', value: 'fr' },
  { label: 'German', value: 'de' }
]

const timezoneOptions = [
  { label: 'Eastern Time (UTC-5)', value: 'America/New_York' },
  { label: 'Central Time (UTC-6)', value: 'America/Chicago' },
  { label: 'Mountain Time (UTC-7)', value: 'America/Denver' },
  { label: 'Pacific Time (UTC-8)', value: 'America/Los_Angeles' }
]

const dateFormatOptions = [
  { label: 'MM/DD/YYYY', value: 'MM/DD/YYYY' },
  { label: 'DD/MM/YYYY', value: 'DD/MM/YYYY' },
  { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' }
]

const notificationTypes = [
  { key: 'grades', label: 'Grade Updates', description: 'When grades are posted or updated' },
  { key: 'assignments', label: 'New Assignments', description: 'When new assignments are posted' },
  { key: 'announcements', label: 'Course Announcements', description: 'Important course announcements' },
  { key: 'messages', label: 'Direct Messages', description: 'Messages from faculty and students' },
  { key: 'deadlines', label: 'Deadline Reminders', description: 'Upcoming assignment deadlines' },
  { key: 'events', label: 'Calendar Events', description: 'Upcoming events and meetings' }
]

const visibilityOptions = [
  { label: 'Private', value: 'private' },
  { label: 'Faculty Only', value: 'faculty-only' },
  { label: 'Students & Faculty', value: 'students-faculty' },
  { label: 'Public', value: 'public' }
]

const courseViewOptions = [
  { label: 'Grid View', value: 'grid' },
  { label: 'List View', value: 'list' },
  { label: 'Card View', value: 'card' }
]

const gradeFormatOptions = [
  { label: 'Letter Grades (A, B, C)', value: 'letter' },
  { label: 'Percentage (85%)', value: 'percentage' },
  { label: 'Points (85/100)', value: 'points' },
  { label: 'GPA (3.5)', value: 'gpa' }
]

const dayOptions = [
  { label: 'Sunday', value: 'sunday' },
  { label: 'Monday', value: 'monday' }
]

// Methods
const savePreferences = async () => {
  isSaving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    useToast().add({
      title: 'Preferences Saved',
      description: 'Your preferences have been updated successfully',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isSaving.value = false
  }
}

const resetPreferences = () => {
  useToast().add({
    title: 'Preferences Reset',
    description: 'All preferences have been reset to default values',
    icon: 'i-heroicons-arrow-path'
  })
}

// SEO
useHead({
  title: 'Preferences - College Management System',
  meta: [
    { name: 'description', content: 'Customize your experience and notification settings in the College Management System.' }
  ]
})
</script>
