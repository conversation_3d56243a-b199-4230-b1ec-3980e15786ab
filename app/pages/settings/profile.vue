<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Manage your personal information and account settings
        </p>
      </div>

      <!-- Profile Form -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Picture -->
        <div class="lg:col-span-1">
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Profile Picture</h3>
            </template>
            <div class="text-center">
              <UAvatar :src="profileData.avatar" :alt="profileData.name" size="3xl" class="mx-auto mb-4" />
              <div class="space-y-3">
                <UButton color="primary" icon="i-heroicons-photo" @click="uploadPhoto">
                  Upload Photo
                </UButton>
                <UButton variant="outline" icon="i-heroicons-trash" color="red" @click="removePhoto">
                  Remove
                </UButton>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-3">
                JPG, PNG or GIF. Max size 2MB.
              </p>
            </div>
          </UCard>
        </div>

        <!-- Profile Information -->
        <div class="lg:col-span-2">
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Personal Information</h3>
            </template>
            <form @submit.prevent="saveProfile" class="space-y-6">
              <!-- Basic Info -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    First Name
                  </label>
                  <UInput v-model="profileData.firstName" placeholder="Enter first name" />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Last Name
                  </label>
                  <UInput v-model="profileData.lastName" placeholder="Enter last name" />
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Address
                </label>
                <UInput v-model="profileData.email" type="email" placeholder="Enter email address" />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Phone Number
                </label>
                <UInput v-model="profileData.phone" placeholder="Enter phone number" />
              </div>

              <!-- Academic Info -->
              <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h4 class="text-base font-medium text-gray-900 dark:text-white mb-4">Academic Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Student/Employee ID
                    </label>
                    <UInput v-model="profileData.id" placeholder="ID Number" disabled />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Department
                    </label>
                    <USelect v-model="profileData.department" :options="departmentOptions" />
                  </div>
                </div>
              </div>

              <!-- Address -->
              <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h4 class="text-base font-medium text-gray-900 dark:text-white mb-4">Address</h4>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Street Address
                    </label>
                    <UInput v-model="profileData.address.street" placeholder="Enter street address" />
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        City
                      </label>
                      <UInput v-model="profileData.address.city" placeholder="City" />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        State
                      </label>
                      <USelect v-model="profileData.address.state" :options="stateOptions" />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ZIP Code
                      </label>
                      <UInput v-model="profileData.address.zip" placeholder="ZIP" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Emergency Contact -->
              <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h4 class="text-base font-medium text-gray-900 dark:text-white mb-4">Emergency Contact</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Contact Name
                    </label>
                    <UInput v-model="profileData.emergencyContact.name" placeholder="Full name" />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Relationship
                    </label>
                    <USelect v-model="profileData.emergencyContact.relationship" :options="relationshipOptions" />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Phone Number
                    </label>
                    <UInput v-model="profileData.emergencyContact.phone" placeholder="Phone number" />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Email Address
                    </label>
                    <UInput v-model="profileData.emergencyContact.email" type="email" placeholder="Email address" />
                  </div>
                </div>
              </div>

              <!-- Bio -->
              <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h4 class="text-base font-medium text-gray-900 dark:text-white mb-4">Biography</h4>
                <UTextarea
                  v-model="profileData.bio"
                  placeholder="Tell us about yourself..."
                  rows="4"
                />
              </div>

              <!-- Action Buttons -->
              <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                <UButton variant="outline" @click="resetForm">
                  Reset
                </UButton>
                <UButton color="primary" type="submit" :loading="isSaving">
                  Save Changes
                </UButton>
              </div>
            </form>
          </UCard>
        </div>
      </div>

      <!-- Account Actions -->
      <UCard class="mt-8">
        <template #header>
          <h3 class="text-lg font-semibold text-red-600 dark:text-red-400">Danger Zone</h3>
        </template>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">Change Password</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">Update your account password</p>
            </div>
            <UButton variant="outline" @click="changePassword">
              Change Password
            </UButton>
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">Download Data</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">Download a copy of your personal data</p>
            </div>
            <UButton variant="outline" @click="downloadData">
              Download
            </UButton>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-red-600 dark:text-red-400">Delete Account</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">Permanently delete your account and all data</p>
            </div>
            <UButton color="red" variant="outline" @click="deleteAccount">
              Delete Account
            </UButton>
          </div>
        </div>
      </UCard>

      <!-- Back Button -->
      <div class="text-center mt-8">
        <UButton variant="outline" icon="i-heroicons-arrow-left" @click="$router.back()">
          Back
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'default'
})

// Reactive data
const isSaving = ref(false)

const profileData = ref({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '(*************',
  id: 'STU123456',
  department: 'computer-science',
  avatar: 'https://github.com/johndoe.png',
  address: {
    street: '123 Main Street',
    city: 'Springfield',
    state: 'IL',
    zip: '62701'
  },
  emergencyContact: {
    name: 'Jane Doe',
    relationship: 'parent',
    phone: '(*************',
    email: '<EMAIL>'
  },
  bio: 'Computer Science student passionate about software development and artificial intelligence.'
})

// Options
const departmentOptions = [
  { label: 'Computer Science', value: 'computer-science' },
  { label: 'Mathematics', value: 'mathematics' },
  { label: 'English', value: 'english' },
  { label: 'Physics', value: 'physics' },
  { label: 'Chemistry', value: 'chemistry' },
  { label: 'Biology', value: 'biology' }
]

const stateOptions = [
  { label: 'Illinois', value: 'IL' },
  { label: 'California', value: 'CA' },
  { label: 'New York', value: 'NY' },
  { label: 'Texas', value: 'TX' },
  { label: 'Florida', value: 'FL' }
]

const relationshipOptions = [
  { label: 'Parent', value: 'parent' },
  { label: 'Spouse', value: 'spouse' },
  { label: 'Sibling', value: 'sibling' },
  { label: 'Guardian', value: 'guardian' },
  { label: 'Friend', value: 'friend' },
  { label: 'Other', value: 'other' }
]

// Methods
const saveProfile = async () => {
  isSaving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    useToast().add({
      title: 'Profile Updated',
      description: 'Your profile has been saved successfully',
      icon: 'i-heroicons-check-circle'
    })
  } finally {
    isSaving.value = false
  }
}

const resetForm = () => {
  useToast().add({
    title: 'Form Reset',
    description: 'Profile form has been reset to original values',
    icon: 'i-heroicons-arrow-path'
  })
}

const uploadPhoto = () => {
  useToast().add({
    title: 'Upload Photo',
    description: 'Photo upload functionality would be implemented here',
    icon: 'i-heroicons-photo'
  })
}

const removePhoto = () => {
  useToast().add({
    title: 'Photo Removed',
    description: 'Profile photo has been removed',
    icon: 'i-heroicons-trash'
  })
}

const changePassword = () => {
  useToast().add({
    title: 'Change Password',
    description: 'Password change functionality would be implemented here',
    icon: 'i-heroicons-key'
  })
}

const downloadData = () => {
  useToast().add({
    title: 'Download Started',
    description: 'Your data download has been initiated',
    icon: 'i-heroicons-arrow-down-tray'
  })
}

const deleteAccount = () => {
  useToast().add({
    title: 'Delete Account',
    description: 'Account deletion requires additional confirmation',
    color: 'red',
    icon: 'i-heroicons-exclamation-triangle'
  })
}

// SEO
useHead({
  title: 'Profile Settings - College Management System',
  meta: [
    { name: 'description', content: 'Manage your personal information and account settings in the College Management System.' }
  ]
})
</script>
