export interface NavigationItem {
  label: string
  path: string
  icon: string
  children?: NavigationItem[]
  roles?: string[]
  badge?: string
}

export const navigationConfig: Record<string, NavigationItem[]> = {
  admin: [
    {
      label: 'Dashboard',
      path: '/admin',
      icon: 'i-heroicons-home',
      roles: ['admin']
    },
    {
      label: 'User Management',
      path: '/admin/users',
      icon: 'i-heroicons-users',
      roles: ['admin']
    },
    {
      label: 'Academic Management',
      path: '/admin/academic',
      icon: 'i-heroicons-academic-cap',
      roles: ['admin'],
      children: [
        {
          label: 'Departments',
          path: '/admin/academic/departments',
          icon: 'i-heroicons-building-office',
          roles: ['admin']
        },
        {
          label: 'Programs',
          path: '/admin/academic/programs',
          icon: 'i-heroicons-document-text',
          roles: ['admin']
        },
        {
          label: 'Courses',
          path: '/admin/academic/courses',
          icon: 'i-heroicons-book-open',
          roles: ['admin']
        },
        {
          label: 'Course Catalog',
          path: '/academic/courses',
          icon: 'i-heroicons-book-open',
          roles: ['admin']
        },
        {
          label: 'Academic Calendar',
          path: '/academic/calendar',
          icon: 'i-heroicons-calendar-days',
          roles: ['admin']
        },
        {
          label: 'Course Registration',
          path: '/academic/registration',
          icon: 'i-heroicons-plus-circle',
          roles: ['admin']
        }
      ]
    },
    {
      label: 'Financial Management',
      path: '/admin/financial',
      icon: 'i-heroicons-currency-dollar',
      roles: ['admin'],
      children: [
        {
          label: 'Budget',
          path: '/admin/financial/budget',
          icon: 'i-heroicons-chart-pie',
          roles: ['admin']
        },
        {
          label: 'Billing',
          path: '/admin/financial/billing',
          icon: 'i-heroicons-credit-card',
          roles: ['admin']
        }
      ]
    },
    {
      label: 'Analytics',
      path: '/admin/analytics',
      icon: 'i-heroicons-chart-bar',
      roles: ['admin']
    },
    {
      label: 'Settings',
      path: '/admin/settings',
      icon: 'i-heroicons-cog-6-tooth',
      roles: ['admin']
    }
  ],
  student: [
    {
      label: 'Dashboard',
      path: '/student',
      icon: 'i-heroicons-home',
      roles: ['student']
    },
    {
      label: 'Courses',
      path: '/student/courses',
      icon: 'i-heroicons-academic-cap',
      roles: ['student'],
      children: [
        {
          label: 'Course Catalog',
          path: '/academic/courses',
          icon: 'i-heroicons-book-open',
          roles: ['student']
        },
        {
          label: 'My Courses',
          path: '/student/courses/enrolled',
          icon: 'i-heroicons-bookmark',
          roles: ['student']
        },
        {
          label: 'Registration',
          path: '/academic/registration',
          icon: 'i-heroicons-plus-circle',
          roles: ['student']
        }
      ]
    },
    {
      label: 'Grades',
      path: '/student/grades',
      icon: 'i-heroicons-chart-bar-square',
      roles: ['student']
    },
    {
      label: 'Transcript',
      path: '/student/transcript',
      icon: 'i-heroicons-document-text',
      roles: ['student']
    },
    {
      label: 'Financial Aid',
      path: '/student/financial-aid',
      icon: 'i-heroicons-currency-dollar',
      roles: ['student']
    },
    {
      label: 'Messages',
      path: '/student/messages',
      icon: 'i-heroicons-chat-bubble-left-right',
      roles: ['student']
    },
    {
      label: 'Calendar',
      path: '/academic/calendar',
      icon: 'i-heroicons-calendar-days',
      roles: ['student']
    }
  ],
  faculty: [
    {
      label: 'Dashboard',
      path: '/faculty',
      icon: 'i-heroicons-home',
      roles: ['faculty']
    },
    {
      label: 'My Classes',
      path: '/faculty/classes',
      icon: 'i-heroicons-academic-cap',
      roles: ['faculty'],
      children: [
        {
          label: 'Current Classes',
          path: '/faculty/classes/current',
          icon: 'i-heroicons-book-open',
          roles: ['faculty']
        },
        {
          label: 'Class Roster',
          path: '/faculty/classes/roster',
          icon: 'i-heroicons-users',
          roles: ['faculty']
        },
        {
          label: 'Assignments',
          path: '/faculty/classes/assignments',
          icon: 'i-heroicons-document-text',
          roles: ['faculty']
        }
      ]
    },
    {
      label: 'Grading',
      path: '/faculty/grading',
      icon: 'i-heroicons-chart-bar-square',
      roles: ['faculty']
    },
    {
      label: 'Office Hours',
      path: '/faculty/office-hours',
      icon: 'i-heroicons-clock',
      roles: ['faculty']
    },
    {
      label: 'Research',
      path: '/faculty/research',
      icon: 'i-heroicons-beaker',
      roles: ['faculty']
    },
    {
      label: 'Messages',
      path: '/faculty/messages',
      icon: 'i-heroicons-chat-bubble-left-right',
      roles: ['faculty']
    },
    {
      label: 'Calendar',
      path: '/faculty/calendar',
      icon: 'i-heroicons-calendar-days',
      roles: ['faculty']
    }
  ]
}

export const publicNavigation: NavigationItem[] = [
  {
    label: 'Home',
    path: '/',
    icon: 'i-heroicons-home'
  },
  {
    label: 'About',
    path: '/about',
    icon: 'i-heroicons-information-circle'
  },
  {
    label: 'Contact',
    path: '/contact',
    icon: 'i-heroicons-phone'
  }
]
