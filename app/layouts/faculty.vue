<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
    <!-- Mobile backdrop -->
    <div
      v-if="isSidebarOpen"
      class="lg:hidden fixed inset-0 z-40 bg-gray-600 bg-opacity-75"
      @click="isSidebarOpen = false"
      aria-hidden="true"
    />

    <!-- Vertical Navigation Sidebar -->
    <VerticalNavigation
      :is-open="isSidebarOpen"
      @close="isSidebarOpen = false"
    />

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col min-w-0 overflow-hidden">
      <!-- Top Navigation Bar -->
      <div class="sticky top-0 z-30 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
          <!-- Mobile menu button -->
          <UButton
            icon="i-heroicons-bars-3"
            color="gray"
            variant="ghost"
            size="sm"
            class="lg:hidden"
            @click="isSidebarOpen = true"
          />

          <!-- Page Title -->
          <div class="flex-1 min-w-0">
            <h1 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
              {{ pageTitle }}
            </h1>
          </div>

          <!-- Right side actions -->
          <div class="flex items-center space-x-4">
            <!-- Faculty Quick Stats -->
            <div class="hidden md:flex items-center space-x-4 text-sm">
              <div class="text-center">
                <div class="font-bold text-primary-600 dark:text-primary-400">4</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Classes</div>
              </div>
              <div class="text-center">
                <div class="font-bold text-green-600 dark:text-green-400">127</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Students</div>
              </div>
              <div class="text-center">
                <div class="font-bold text-orange-600 dark:text-orange-400">23</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Pending</div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="hidden sm:flex items-center space-x-2">
              <UButton
                to="/faculty/grading"
                color="primary"
                size="xs"
                icon="i-heroicons-chart-bar-square"
              >
                Grade
              </UButton>
              <UButton
                to="/faculty/office-hours"
                color="gray"
                variant="outline"
                size="xs"
                icon="i-heroicons-clock"
              >
                Office Hours
              </UButton>
            </div>

            <!-- Notifications -->
            <UButton
              icon="i-heroicons-bell"
              color="gray"
              variant="ghost"
              size="sm"
              :badge="notificationCount > 0 ? notificationCount : undefined"
            />

            <!-- Theme Toggle -->
            <UButton
              :icon="$colorMode.value === 'dark' ? 'i-heroicons-sun' : 'i-heroicons-moon'"
              color="gray"
              variant="ghost"
              size="sm"
              @click="$colorMode.preference = $colorMode.value === 'dark' ? 'light' : 'dark'"
            />

            <!-- User Profile Dropdown -->
            <UserProfileDropdown />
          </div>
        </div>
      </div>

      <!-- Page Content -->
      <main class="flex-1 p-3 sm:p-4 lg:p-6 overflow-y-auto">
        <!-- Breadcrumb Navigation -->
        <div class="mb-4">
          <BreadcrumbNavigation />
        </div>

        <!-- Faculty Quick Actions Bar (Mobile) -->
        <div class="md:hidden mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <!-- Quick Stats -->
            <div class="flex items-center space-x-6">
              <div class="text-center">
                <div class="text-xl font-bold text-primary-600 dark:text-primary-400">4</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Classes</div>
              </div>
              <div class="text-center">
                <div class="text-xl font-bold text-green-600 dark:text-green-400">127</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Students</div>
              </div>
              <div class="text-center">
                <div class="text-xl font-bold text-orange-600 dark:text-orange-400">23</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Pending</div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="flex items-center space-x-2">
              <UButton
                to="/faculty/grading"
                color="primary"
                size="sm"
                icon="i-heroicons-chart-bar-square"
              >
                Grade
              </UButton>
              <UButton
                to="/faculty/classes/assignments"
                color="gray"
                variant="outline"
                size="sm"
                icon="i-heroicons-document-text"
              >
                Assignments
              </UButton>
            </div>
          </div>
        </div>

        <!-- Page Content Slot -->
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup>
const route = useRoute()
const isSidebarOpen = ref(false)

// Mock notification count
const notificationCount = ref(5)

// Generate page title from route
const pageTitle = computed(() => {
  const segments = route.path.split('/').filter(Boolean)
  if (segments.length === 0) return 'Dashboard'

  const lastSegment = segments[segments.length - 1]
  return lastSegment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
})

// Close sidebar when route changes on mobile
watch(() => route.path, () => {
  isSidebarOpen.value = false
})
</script>
