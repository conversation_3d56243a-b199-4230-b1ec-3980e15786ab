<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
    <!-- Horizontal Navigation -->
    <div class="flex-shrink-0">
      <HorizontalNavigation />
    </div>

    <!-- Main Content -->
    <main class="flex-1 py-8 w-full overflow-y-auto">
      <UContainer>
        <!-- Faculty Quick Actions Bar -->
        <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <!-- Quick Stats -->
            <div class="flex items-center space-x-6">
              <div class="text-center">
                <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">4</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Classes</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">127</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Students</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">23</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Pending</div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="flex items-center space-x-2">
              <UButton
                to="/faculty/grading"
                color="primary"
                size="sm"
                icon="i-heroicons-chart-bar-square"
              >
                Grade
              </UButton>
              <UButton
                to="/faculty/classes/assignments"
                color="gray"
                variant="outline"
                size="sm"
                icon="i-heroicons-document-text"
              >
                Assignments
              </UButton>
              <UButton
                to="/faculty/office-hours"
                color="gray"
                variant="outline"
                size="sm"
                icon="i-heroicons-clock"
              >
                Office Hours
              </UButton>
            </div>
          </div>
        </div>

        <!-- Breadcrumb Navigation -->
        <div class="mb-6">
          <BreadcrumbNavigation />
        </div>

        <!-- Page Content -->
        <slot />
      </UContainer>
    </main>

    <!-- Footer -->
    <footer class="flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <UContainer class="py-6">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            © 2025 College Management System. All rights reserved.
          </p>
          <div class="flex items-center space-x-4 mt-4 md:mt-0">
            <NuxtLink to="/faculty/resources" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              Faculty Resources
            </NuxtLink>
            <NuxtLink to="/faculty/handbook" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              Faculty Handbook
            </NuxtLink>
            <NuxtLink to="/faculty/support" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              IT Support
            </NuxtLink>
          </div>
        </div>
      </UContainer>
    </footer>
  </div>
</template>
