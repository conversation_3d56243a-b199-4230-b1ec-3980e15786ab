<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
    <!-- Horizontal Navigation -->
    <div class="flex-shrink-0">
      <HorizontalNavigation />
    </div>

    <!-- Main Content -->
    <main class="flex-1 py-8 w-full overflow-y-auto">
      <UContainer>
        <!-- Student Quick Actions Bar -->
        <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <!-- Quick Stats -->
            <div class="flex items-center space-x-6">
              <div class="text-center">
                <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">3.8</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">GPA</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">15</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Credits</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">5</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Courses</div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="flex items-center space-x-2">
              <UButton
                to="/student/courses/registration"
                color="primary"
                size="sm"
                icon="i-heroicons-plus"
              >
                Register
              </UButton>
              <UButton
                to="/student/grades"
                color="gray"
                variant="outline"
                size="sm"
                icon="i-heroicons-chart-bar-square"
              >
                Grades
              </UButton>
              <UButton
                to="/student/calendar"
                color="gray"
                variant="outline"
                size="sm"
                icon="i-heroicons-calendar-days"
              >
                Schedule
              </UButton>
            </div>
          </div>
        </div>

        <!-- Breadcrumb Navigation -->
        <div class="mb-6">
          <BreadcrumbNavigation />
        </div>

        <!-- Page Content -->
        <slot />
      </UContainer>
    </main>

    <!-- Footer -->
    <footer class="flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <UContainer class="py-6">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            © 2025 College Management System. All rights reserved.
          </p>
          <div class="flex items-center space-x-4 mt-4 md:mt-0">
            <NuxtLink to="/student/help" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              Student Help
            </NuxtLink>
            <NuxtLink to="/student/handbook" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              Handbook
            </NuxtLink>
            <NuxtLink to="/student/support" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              Support
            </NuxtLink>
          </div>
        </div>
      </UContainer>
    </footer>
  </div>
</template>
