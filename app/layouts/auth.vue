<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
    <!-- Simple Header for Auth Pages -->
    <header class="flex-shrink-0 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <UContainer>
        <div class="flex justify-between items-center h-16">
          <!-- Logo and Brand -->
          <div class="flex items-center">
            <NuxtLink to="/" class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-white" />
              </div>
              <span class="text-xl font-bold text-gray-900 dark:text-white">College Management System</span>
            </NuxtLink>
          </div>

          <!-- Theme Toggle -->
          <div class="flex items-center space-x-4">
            <UButton
              :icon="$colorMode.value === 'dark' ? 'i-heroicons-sun' : 'i-heroicons-moon'"
              color="gray"
              variant="ghost"
              size="sm"
              @click="$colorMode.preference = $colorMode.value === 'dark' ? 'light' : 'dark'"
            />
          </div>
        </div>
      </UContainer>
    </header>

    <!-- Main Content -->
    <main class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div class="w-full max-w-md">
        <slot />
      </div>
    </main>

    <!-- Footer -->
    <footer class="flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <UContainer class="py-4">
        <div class="flex flex-col sm:flex-row justify-between items-center">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            © 2025 College Management System. All rights reserved.
          </p>
          <div class="flex items-center space-x-4 mt-2 sm:mt-0">
            <NuxtLink to="/privacy" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              Privacy
            </NuxtLink>
            <NuxtLink to="/terms" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              Terms
            </NuxtLink>
            <NuxtLink to="/help" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              Help
            </NuxtLink>
          </div>
        </div>
      </UContainer>
    </footer>
  </div>
</template>
