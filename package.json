{"name": "cmt", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "npx nuxthub preview", "deploy": "npx nuxthub deploy", "postinstall": "nuxt prepare && lefthook install", "format": "biome format --write .", "lint": "bunx oxlint@latest --fix --fix-suggestions", "biome:lint": "biome lint .", "biome:check": "biome check --write .", "biome:ci": "biome ci .", "lefthook:install": "lefthook install", "lefthook:uninstall": "lefthook uninstall"}, "dependencies": {"@nuxt/ui": "^3.1.3", "@nuxthub/core": "^0.8.25", "@nuxtjs/color-mode": "^3.5.2", "nuxt": "^3.17.5", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.5", "@compodium/nuxt": "0.1.0-beta.10", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/lucide": "^1.2.45", "lefthook": "^1.11.13", "nuxt-mcp": "^0.2.2", "oxlint": "0.17.0", "typescript": "^5.8.3", "vue-tsc": "^2.2.10", "wrangler": "^4.12.1"}}